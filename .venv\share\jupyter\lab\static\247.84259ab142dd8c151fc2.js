"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[247],{90247:(e,t,n)=>{n.r(t);n.d(t,{sas:()=>c});var r={};var s={eq:"operator",lt:"operator",le:"operator",gt:"operator",ge:"operator",in:"operator",ne:"operator",or:"operator"};var a=/(<=|>=|!=|<>)/;var o=/[=\(:\),{}.*<>+\-\/^\[\]]/;function i(e,t,n){if(n){var s=t.split(" ");for(var a=0;a<s.length;a++){r[s[a]]={style:e,state:n}}}}i("def","stack pgm view source debug nesting nolist",["inDataStep"]);i("def","if while until for do do; end end; then else cancel",["inDataStep"]);i("def","label format _n_ _error_",["inDataStep"]);i("def","ALTER BUFNO BUFSIZE CNTLLEV COMPRESS DLDMGACTION ENCRYPT ENCRYPTKEY EXTENDOBSCOUNTER GENMAX GENNUM INDEX LABEL OBSBUF OUTREP PW PWREQ READ REPEMPTY REPLACE REUSE ROLE SORTEDBY SPILL TOBSNO TYPE WRITE FILECLOSE FIRSTOBS IN OBS POINTOBS WHERE WHEREUP IDXNAME IDXWHERE DROP KEEP RENAME",["inDataStep"]);i("def","filevar finfo finv fipname fipnamel fipstate first firstobs floor",["inDataStep"]);i("def","varfmt varinfmt varlabel varlen varname varnum varray varrayx vartype verify vformat vformatd vformatdx vformatn vformatnx vformatw vformatwx vformatx vinarray vinarrayx vinformat vinformatd vinformatdx vinformatn vinformatnx vinformatw vinformatwx vinformatx vlabel vlabelx vlength vlengthx vname vnamex vnferr vtype vtypex weekday",["inDataStep"]);i("def","zipfips zipname zipnamel zipstate",["inDataStep"]);i("def","put putc putn",["inDataStep"]);i("builtin","data run",["inDataStep"]);i("def","data",["inProc"]);i("def","%if %end %end; %else %else; %do %do; %then",["inMacro"]);i("builtin","proc run; quit; libname filename %macro %mend option options",["ALL"]);i("def","footnote title libname ods",["ALL"]);i("def","%let %put %global %sysfunc %eval ",["ALL"]);i("variable","&sysbuffr &syscc &syscharwidth &syscmd &sysdate &sysdate9 &sysday &sysdevic &sysdmg &sysdsn &sysencoding &sysenv &syserr &syserrortext &sysfilrc &syshostname &sysindex &sysinfo &sysjobid &syslast &syslckrc &syslibrc &syslogapplname &sysmacroname &sysmenv &sysmsg &sysncpu &sysodspath &sysparm &syspbuff &sysprocessid &sysprocessname &sysprocname &sysrc &sysscp &sysscpl &sysscpl &syssite &sysstartid &sysstartname &systcpiphostname &systime &sysuserid &sysver &sysvlong &sysvlong4 &syswarningtext",["ALL"]);i("def","source2 nosource2 page pageno pagesize",["ALL"]);i("def","_all_ _character_ _cmd_ _freq_ _i_ _infile_ _last_ _msg_ _null_ _numeric_ _temporary_ _type_ abort abs addr adjrsq airy alpha alter altlog altprint and arcos array arsin as atan attrc attrib attrn authserver autoexec awscontrol awsdef awsmenu awsmenumerge awstitle backward band base betainv between blocksize blshift bnot bor brshift bufno bufsize bxor by byerr byline byte calculated call cards cards4 catcache cbufno cdf ceil center cexist change chisq cinv class cleanup close cnonct cntllev coalesce codegen col collate collin column comamid comaux1 comaux2 comdef compbl compound compress config continue convert cos cosh cpuid create cross crosstab css curobs cv daccdb daccdbsl daccsl daccsyd dacctab dairy datalines datalines4 datejul datepart datetime day dbcslang dbcstype dclose ddfm ddm delete delimiter depdb depdbsl depsl depsyd deptab dequote descending descript design= device dflang dhms dif digamma dim dinfo display distinct dkricond dkrocond dlm dnum do dopen doptname doptnum dread drop dropnote dsname dsnferr echo else emaildlg emailid emailpw emailserver emailsys encrypt end endsas engine eof eov erf erfc error errorcheck errors exist exp fappend fclose fcol fdelete feedback fetch fetchobs fexist fget file fileclose fileexist filefmt filename fileref  fmterr fmtsearch fnonct fnote font fontalias  fopen foptname foptnum force formatted formchar formdelim formdlim forward fpoint fpos fput fread frewind frlen from fsep fuzz fwrite gaminv gamma getoption getvarc getvarn go goto group gwindow hbar hbound helpenv helploc hms honorappearance hosthelp hostprint hour hpct html hvar ibessel ibr id if index indexc indexw initcmd initstmt inner input inputc inputn inr insert int intck intnx into intrr invaliddata irr is jbessel join juldate keep kentb kurtosis label lag last lbound leave left length levels lgamma lib  library libref line linesize link list log log10 log2 logpdf logpmf logsdf lostcard lowcase lrecl ls macro macrogen maps mautosource max maxdec maxr mdy mean measures median memtype merge merror min minute missing missover mlogic mod mode model modify month mopen mort mprint mrecall msglevel msymtabmax mvarsize myy n nest netpv new news nmiss no nobatch nobs nocaps nocardimage nocenter nocharcode nocmdmac nocol nocum nodate nodbcs nodetails nodmr nodms nodmsbatch nodup nodupkey noduplicates noechoauto noequals noerrorabend noexitwindows nofullstimer noicon noimplmac noint nolist noloadlist nomiss nomlogic nomprint nomrecall nomsgcase nomstored nomultenvappl nonotes nonumber noobs noovp nopad nopercent noprint noprintinit normal norow norsasuser nosetinit  nosplash nosymbolgen note notes notitle notitles notsorted noverbose noxsync noxwait npv null number numkeys nummousekeys nway obs  on open     order ordinal otherwise out outer outp= output over ovp p(1 5 10 25 50 75 90 95 99) pad pad2  paired parm parmcards path pathdll pathname pdf peek peekc pfkey pmf point poisson poke position printer probbeta probbnml probchi probf probgam probhypr probit probnegb probnorm probsig probt procleave prt ps  pw pwreq qtr quote r ranbin rancau random ranexp rangam range ranks rannor ranpoi rantbl rantri ranuni rcorr read recfm register regr remote remove rename repeat repeated replace resolve retain return reuse reverse rewind right round rsquare rtf rtrace rtraceloc s s2 samploc sasautos sascontrol sasfrscr sasmsg sasmstore sasscript sasuser saving scan sdf second select selection separated seq serror set setcomm setot sign simple sin sinh siteinfo skewness skip sle sls sortedby sortpgm sortseq sortsize soundex  spedis splashlocation split spool sqrt start std stderr stdin stfips stimer stname stnamel stop stopover sub subgroup subpopn substr sum sumwgt symbol symbolgen symget symput sysget sysin sysleave sysmsg sysparm sysprint sysprintfont sysprod sysrc system t table tables tan tanh tapeclose tbufsize terminal test then timepart tinv  tnonct to today tol tooldef totper transformout translate trantab tranwrd trigamma trim trimn trunc truncover type unformatted uniform union until upcase update user usericon uss validate value var  weight when where while wincharset window work workinit workterm write wsum xsync xwait yearcutoff yes yyq  min max",["inDataStep","inProc"]);i("operator","and not ",["inDataStep","inProc"]);function l(e,t){var n=e.next();if(n==="/"&&e.eat("*")){t.continueComment=true;return"comment"}else if(t.continueComment===true){if(n==="*"&&e.peek()==="/"){e.next();t.continueComment=false}else if(e.skipTo("*")){e.skipTo("*");e.next();if(e.eat("/"))t.continueComment=false}else{e.skipToEnd()}return"comment"}if(n=="*"&&e.column()==e.indentation()){e.skipToEnd();return"comment"}var i=n+e.peek();if((n==='"'||n==="'")&&!t.continueString){t.continueString=n;return"string"}else if(t.continueString){if(t.continueString==n){t.continueString=null}else if(e.skipTo(t.continueString)){e.next();t.continueString=null}else{e.skipToEnd()}return"string"}else if(t.continueString!==null&&e.eol()){e.skipTo(t.continueString)||e.skipToEnd();return"string"}else if(/[\d\.]/.test(n)){if(n===".")e.match(/^[0-9]+([eE][\-+]?[0-9]+)?/);else if(n==="0")e.match(/^[xX][0-9a-fA-F]+/)||e.match(/^0[0-7]+/);else e.match(/^[0-9]*\.?[0-9]*([eE][\-+]?[0-9]+)?/);return"number"}else if(a.test(n+e.peek())){e.next();return"operator"}else if(s.hasOwnProperty(i)){e.next();if(e.peek()===" ")return s[i.toLowerCase()]}else if(o.test(n)){return"operator"}var l;if(e.match(/[%&;\w]+/,false)!=null){l=n+e.match(/[%&;\w]+/,true);if(/&/.test(l))return"variable"}else{l=n}if(t.nextword){e.match(/[\w]+/);if(e.peek()===".")e.skipTo(" ");t.nextword=false;return"variableName.special"}l=l.toLowerCase();if(t.inDataStep){if(l==="run;"||e.match(/run\s;/)){t.inDataStep=false;return"builtin"}if(l&&e.next()==="."){if(/\w/.test(e.peek()))return"variableName.special";else return"variable"}if(l&&r.hasOwnProperty(l)&&(r[l].state.indexOf("inDataStep")!==-1||r[l].state.indexOf("ALL")!==-1)){if(e.start<e.pos)e.backUp(e.pos-e.start);for(var c=0;c<l.length;++c)e.next();return r[l].style}}if(t.inProc){if(l==="run;"||l==="quit;"){t.inProc=false;return"builtin"}if(l&&r.hasOwnProperty(l)&&(r[l].state.indexOf("inProc")!==-1||r[l].state.indexOf("ALL")!==-1)){e.match(/[\w]+/);return r[l].style}}if(t.inMacro){if(l==="%mend"){if(e.peek()===";")e.next();t.inMacro=false;return"builtin"}if(l&&r.hasOwnProperty(l)&&(r[l].state.indexOf("inMacro")!==-1||r[l].state.indexOf("ALL")!==-1)){e.match(/[\w]+/);return r[l].style}return"atom"}if(l&&r.hasOwnProperty(l)){e.backUp(1);e.match(/[\w]+/);if(l==="data"&&/=/.test(e.peek())===false){t.inDataStep=true;t.nextword=true;return"builtin"}if(l==="proc"){t.inProc=true;t.nextword=true;return"builtin"}if(l==="%macro"){t.inMacro=true;t.nextword=true;return"builtin"}if(/title[1-9]/.test(l))return"def";if(l==="footnote"){e.eat(/[1-9]/);return"def"}if(t.inDataStep===true&&r[l].state.indexOf("inDataStep")!==-1)return r[l].style;if(t.inProc===true&&r[l].state.indexOf("inProc")!==-1)return r[l].style;if(t.inMacro===true&&r[l].state.indexOf("inMacro")!==-1)return r[l].style;if(r[l].state.indexOf("ALL")!==-1)return r[l].style;return null}return null}const c={name:"sas",startState:function(){return{inDataStep:false,inProc:false,inMacro:false,nextword:false,continueString:null,continueComment:false}},token:function(e,t){if(e.eatSpace())return null;return l(e,t)},languageData:{commentTokens:{block:{open:"/*",close:"*/"}}}}}}]);