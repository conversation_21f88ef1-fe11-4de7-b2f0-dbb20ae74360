textblob-0.19.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
textblob-0.19.0.dist-info/LICENSE,sha256=kGtdkFHkJhRMsXOtkRZnuOvQWpxYTCwmwTWzKj7RIAE,1064
textblob-0.19.0.dist-info/METADATA,sha256=llTkzsM4i0rDiQ8PhIVhQtFfWV1z-_xgkmN02u_MMMM,4443
textblob-0.19.0.dist-info/RECORD,,
textblob-0.19.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
textblob-0.19.0.dist-info/WHEEL,sha256=CpUCUxeHQbRN5UGRQHYRJorO5Af-Qy_fHMctcQ8DSGI,82
textblob/__init__.py,sha256=gO3Fp0F6VAwO2-7s6gEhrldhw2Wj-3yrqrYq_tndhlQ,152
textblob/__pycache__/__init__.cpython-312.pyc,,
textblob/__pycache__/_text.cpython-312.pyc,,
textblob/__pycache__/base.cpython-312.pyc,,
textblob/__pycache__/blob.cpython-312.pyc,,
textblob/__pycache__/classifiers.cpython-312.pyc,,
textblob/__pycache__/decorators.cpython-312.pyc,,
textblob/__pycache__/download_corpora.cpython-312.pyc,,
textblob/__pycache__/exceptions.cpython-312.pyc,,
textblob/__pycache__/formats.cpython-312.pyc,,
textblob/__pycache__/inflect.cpython-312.pyc,,
textblob/__pycache__/mixins.cpython-312.pyc,,
textblob/__pycache__/np_extractors.cpython-312.pyc,,
textblob/__pycache__/parsers.cpython-312.pyc,,
textblob/__pycache__/sentiments.cpython-312.pyc,,
textblob/__pycache__/taggers.cpython-312.pyc,,
textblob/__pycache__/tokenizers.cpython-312.pyc,,
textblob/__pycache__/utils.cpython-312.pyc,,
textblob/__pycache__/wordnet.cpython-312.pyc,,
textblob/_text.py,sha256=iKJLyRhHGNZkYDDKUsCmrynw03MsuuG7YebvwM3QPOY,64424
textblob/base.py,sha256=WE1jS-4yjIuiwF5-EjBTe5kdGu7bEsac9iY-jnHDlhg,2843
textblob/blob.py,sha256=6QARHkLTq2sluu9cktHK_W2sWgboO4E-mxJGRitTx20,27449
textblob/classifiers.py,sha256=qFAuu0giqo7c2kP4gKjRHLy9wC6UgopqwKu1E4WiUMs,19291
textblob/decorators.py,sha256=ZqR_xWGJRYeSxWiG4UG1i_llob460c71VLX_N8EIguw,995
textblob/download_corpora.py,sha256=yEctvEwZJYxC4h7NR3sYiQqWljzF3nkydl_RP6fC7dI,1015
textblob/en/__init__.py,sha256=J9_QN5qexiew-6erCjWnJHjA7Lk0s52fMQ32k_KtC_Y,4230
textblob/en/__pycache__/__init__.cpython-312.pyc,,
textblob/en/__pycache__/inflect.cpython-312.pyc,,
textblob/en/__pycache__/np_extractors.cpython-312.pyc,,
textblob/en/__pycache__/parsers.cpython-312.pyc,,
textblob/en/__pycache__/sentiments.cpython-312.pyc,,
textblob/en/__pycache__/taggers.cpython-312.pyc,,
textblob/en/en-context.txt,sha256=143GgHvarMjmnjo2Nc4s7lSKJD6szoqsvx_mUJOYATE,6578
textblob/en/en-entities.txt,sha256=Vohz4KuagHrWlkDl8FBOczmDxCSjZlQl4HwkyPpbtR4,10334
textblob/en/en-lexicon.txt,sha256=lUXHH68W5EYdC_8dmUj-krLPlHZq8Rimxpw3tDNj1dI,1220323
textblob/en/en-morphology.txt,sha256=QDIQCqsyRznQ8m_TLRPi4PWAGAZum3ag5M0GW7_Askg,3274
textblob/en/en-sentiment.xml,sha256=DKYD21VXC6ubKGVxajaW7eAePaArVwxHYoqdvMOzfR4,540708
textblob/en/en-spelling.txt,sha256=6t6RVYFn_gmkgav1nhdq-GQ614Z4zFPdWuryqLMPozI,321210
textblob/en/inflect.py,sha256=d3C1tw3Q61nUveE5_z1aGFB9CPs2yk5kSotATR7oYXI,22745
textblob/en/np_extractors.py,sha256=eSb_rv41Xm40lkAqGdGHfP6xRPdIRJ6eO7I3Dyp70n8,6690
textblob/en/parsers.py,sha256=8sCO_t8EOW4EqvpbHAcaIsgsDkENwmwmRbg9KNNRLxI,417
textblob/en/sentiments.py,sha256=SyYN9iIJObHqzuZ_XAtPfQVXIecsuJKbUBJlsgY66Qk,3844
textblob/en/taggers.py,sha256=41YFA-_61aPuAlWTaXnXxZwyJyn5uV-5ikT7ScEzMSM,931
textblob/exceptions.py,sha256=nvoeiczophoJODsKmqLrxVh8kgC_Fg_rjcmoR43zN3k,1432
textblob/formats.py,sha256=AIH790WgOdtszkFh2smV2AeqnFHcF6Vr8ktidIZp3_E,4205
textblob/inflect.py,sha256=qBUvlR4kuo6dhmGzimcXFgSvBCoWtL0VChQnk6Ull7k,354
textblob/mixins.py,sha256=pY7Wtbjm6JQrDUAc2QoinizIzWim5kWGIm9Q-QZr_hE,5994
textblob/np_extractors.py,sha256=vYHK29mkXzBnFYi7eu0_eS8_9cRqjwGs2q6a5oeM330,444
textblob/parsers.py,sha256=3gaaL7dhKW35qJ7UwnRjH5ZZStOkiS1D_PtngK5piMs,343
textblob/sentiments.py,sha256=GX2UTHm1QyOyPZMQj7ThPk_LgdY1m4c4OXC3vnbwlF4,519
textblob/taggers.py,sha256=4PVSl_skWIZ6U3BwuaSgKaC4vpmM4ZWoimPD5zzWrr0,382
textblob/tokenizers.py,sha256=yAGNODJknK66Wsbl_HiVwMi45ZEqtFYWHiDJsaAaelM,2511
textblob/utils.py,sha256=a8fxKudbXPrjtxpbenoMw7gzAco2okfU072TTkLzw-E,1493
textblob/wordnet.py,sha256=viX6mtFrRDYBh1mqFdZODc4RLLp4wMj6AdY_x63p2dE,399
