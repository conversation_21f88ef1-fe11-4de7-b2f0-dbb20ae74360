{"version": 3, "file": "2581.f9bb29600b7e080b2b84.js?v=f9bb29600b7e080b2b84", "mappings": ";;;;;;;;;;AAE8B;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;;AAIJ;;;;;;;;;;;;;;;;AClB4B;AAGA;AAGA;AAcA;;AAE9B;AACA;AACA;AACA;AACA;AACA,4BAA4B,gFAAqB;AACjD,iCAAiC,qEAAM;AACvC,iBAAiB,4EAAa;AAC9B;AACA,OAAO,wEAAS;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA,CAAC;AACD,6BAA6B,qEAAM;AACnC,EAAE,oEAAK;AACP;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,aAAa;AACb,iBAAiB;AACjB,iBAAiB;AACjB,mBAAmB;AACnB,mBAAmB;AACnB;;AAEA;AAC2C;AAC3C;AACA,+BAA+B,qEAAM;AACrC,EAAE,8EAAgB;AAClB;AACA;AACA;AACA,UAAU,aAAa;AACvB,aAAa,oBAAoB;AACjC;AACA,sCAAsC,OAAO,IAAI,KAAK;AACtD;AACA;AACA;AACA,wBAAwB,OAAO,IAAI,cAAc,0CAA0C,aAAa;AACxG;AACA;AACA;AACA,IAAI,8DAAG,uBAAuB,OAAO,IAAI,UAAU,aAAa,MAAM;AACtE;AACA,uDAAuD,mBAAmB;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB;AAC7B;AACA;AACA;AACA,CAAC;AACD,0CAA0C,qEAAM;AAChD;AACA;AACA;AACA;AACA,mCAAmC,aAAa,4BAA4B,UAAU;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,yBAAyB,qEAAM;AAC/B,sBAAsB,mEAAK;AAC3B,IAAI,8DAAG;AACP;AACA,GAAG;AACH;;AAEA;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA,UAAU,4CAA4C;AACtD;AACA;AACA;AACA;AACA;AACA,cAAc,8EAAgB;AAC9B,6BAA6B,UAAU,EAAE,UAAU;AACnD,EAAE,+EAAgB;AAClB;AACA;AACA;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM,0BAA0B,+DAA+D;AAC9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,qEAAM,IAAI,SAAS,IAAI;AACpD,kBAAkB,4EAAa;AAC/B;AACA;AACA,eAAe;AACf;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV,eAAe;AACf;AACA;AACA,UAAU;AACV,eAAe;AACf;AACA;AACA,YAAY;AACZ,kBAAkB;AAClB,UAAU;AACV;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-4BMEZGHF.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/diagram-VNBRO52H.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/populateCommonDb.ts\nfunction populateCommonDb(ast, db) {\n  if (ast.accDescr) {\n    db.setAccDescription?.(ast.accDescr);\n  }\n  if (ast.accTitle) {\n    db.setAccTitle?.(ast.accTitle);\n  }\n  if (ast.title) {\n    db.setDiagramTitle?.(ast.title);\n  }\n}\n__name(populateCommonDb, \"populateCommonDb\");\n\nexport {\n  populateCommonDb\n};\n", "import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/packet/db.ts\nvar defaultPacketData = {\n  packet: []\n};\nvar data = structuredClone(defaultPacketData);\nvar DEFAULT_PACKET_CONFIG = defaultConfig_default.packet;\nvar getConfig2 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_PACKET_CONFIG,\n    ...getConfig().packet\n  });\n  if (config.showBits) {\n    config.paddingY += 10;\n  }\n  return config;\n}, \"getConfig\");\nvar getPacket = /* @__PURE__ */ __name(() => data.packet, \"getPacket\");\nvar pushWord = /* @__PURE__ */ __name((word) => {\n  if (word.length > 0) {\n    data.packet.push(word);\n  }\n}, \"pushWord\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  clear();\n  data = structuredClone(defaultPacketData);\n}, \"clear\");\nvar db = {\n  pushWord,\n  getPacket,\n  getConfig: getConfig2,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/packet/parser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar maxPacketSize = 1e4;\nvar populate = /* @__PURE__ */ __name((ast) => {\n  populateCommonDb(ast, db);\n  let lastByte = -1;\n  let word = [];\n  let row = 1;\n  const { bitsPerRow } = db.getConfig();\n  for (let { start, end, label } of ast.blocks) {\n    if (end && end < start) {\n      throw new Error(`Packet block ${start} - ${end} is invalid. End must be greater than start.`);\n    }\n    if (start !== lastByte + 1) {\n      throw new Error(\n        `Packet block ${start} - ${end ?? start} is not contiguous. It should start from ${lastByte + 1}.`\n      );\n    }\n    lastByte = end ?? start;\n    log.debug(`Packet block ${start} - ${lastByte} with label ${label}`);\n    while (word.length <= bitsPerRow + 1 && db.getPacket().length < maxPacketSize) {\n      const [block, nextBlock] = getNextFittingBlock({ start, end, label }, row, bitsPerRow);\n      word.push(block);\n      if (block.end + 1 === row * bitsPerRow) {\n        db.pushWord(word);\n        word = [];\n        row++;\n      }\n      if (!nextBlock) {\n        break;\n      }\n      ({ start, end, label } = nextBlock);\n    }\n  }\n  db.pushWord(word);\n}, \"populate\");\nvar getNextFittingBlock = /* @__PURE__ */ __name((block, row, bitsPerRow) => {\n  if (block.end === void 0) {\n    block.end = block.start;\n  }\n  if (block.start > block.end) {\n    throw new Error(`Block start ${block.start} is greater than block end ${block.end}.`);\n  }\n  if (block.end + 1 <= row * bitsPerRow) {\n    return [block, void 0];\n  }\n  return [\n    {\n      start: block.start,\n      end: row * bitsPerRow - 1,\n      label: block.label\n    },\n    {\n      start: row * bitsPerRow,\n      end: block.end,\n      label: block.label\n    }\n  ];\n}, \"getNextFittingBlock\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"packet\", input);\n    log.debug(ast);\n    populate(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/packet/renderer.ts\nvar draw = /* @__PURE__ */ __name((_text, id, _version, diagram2) => {\n  const db2 = diagram2.db;\n  const config = db2.getConfig();\n  const { rowHeight, paddingY, bitWidth, bitsPerRow } = config;\n  const words = db2.getPacket();\n  const title = db2.getDiagramTitle();\n  const totalRowHeight = rowHeight + paddingY;\n  const svgHeight = totalRowHeight * (words.length + 1) - (title ? 0 : rowHeight);\n  const svgWidth = bitWidth * bitsPerRow + 2;\n  const svg = selectSvgElement(id);\n  svg.attr(\"viewbox\", `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n  for (const [word, packet] of words.entries()) {\n    drawWord(svg, packet, word, config);\n  }\n  svg.append(\"text\").text(title).attr(\"x\", svgWidth / 2).attr(\"y\", svgHeight - totalRowHeight / 2).attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").attr(\"class\", \"packetTitle\");\n}, \"draw\");\nvar drawWord = /* @__PURE__ */ __name((svg, word, rowNumber, { rowHeight, paddingX, paddingY, bitWidth, bitsPerRow, showBits }) => {\n  const group = svg.append(\"g\");\n  const wordY = rowNumber * (rowHeight + paddingY) + paddingY;\n  for (const block of word) {\n    const blockX = block.start % bitsPerRow * bitWidth + 1;\n    const width = (block.end - block.start + 1) * bitWidth - paddingX;\n    group.append(\"rect\").attr(\"x\", blockX).attr(\"y\", wordY).attr(\"width\", width).attr(\"height\", rowHeight).attr(\"class\", \"packetBlock\");\n    group.append(\"text\").attr(\"x\", blockX + width / 2).attr(\"y\", wordY + rowHeight / 2).attr(\"class\", \"packetLabel\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").text(block.label);\n    if (!showBits) {\n      continue;\n    }\n    const isSingleBlock = block.end === block.start;\n    const bitNumberY = wordY - 2;\n    group.append(\"text\").attr(\"x\", blockX + (isSingleBlock ? width / 2 : 0)).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte start\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", isSingleBlock ? \"middle\" : \"start\").text(block.start);\n    if (!isSingleBlock) {\n      group.append(\"text\").attr(\"x\", blockX + width).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte end\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", \"end\").text(block.end);\n    }\n  }\n}, \"drawWord\");\nvar renderer = { draw };\n\n// src/diagrams/packet/styles.ts\nvar defaultPacketStyleOptions = {\n  byteFontSize: \"10px\",\n  startByteColor: \"black\",\n  endByteColor: \"black\",\n  labelColor: \"black\",\n  labelFontSize: \"12px\",\n  titleColor: \"black\",\n  titleFontSize: \"14px\",\n  blockStrokeColor: \"black\",\n  blockStrokeWidth: \"1\",\n  blockFillColor: \"#efefef\"\n};\nvar styles = /* @__PURE__ */ __name(({ packet } = {}) => {\n  const options = cleanAndMerge(defaultPacketStyleOptions, packet);\n  return `\n\t.packetByte {\n\t\tfont-size: ${options.byteFontSize};\n\t}\n\t.packetByte.start {\n\t\tfill: ${options.startByteColor};\n\t}\n\t.packetByte.end {\n\t\tfill: ${options.endByteColor};\n\t}\n\t.packetLabel {\n\t\tfill: ${options.labelColor};\n\t\tfont-size: ${options.labelFontSize};\n\t}\n\t.packetTitle {\n\t\tfill: ${options.titleColor};\n\t\tfont-size: ${options.titleFontSize};\n\t}\n\t.packetBlock {\n\t\tstroke: ${options.blockStrokeColor};\n\t\tstroke-width: ${options.blockStrokeWidth};\n\t\tfill: ${options.blockFillColor};\n\t}\n\t`;\n}, \"styles\");\n\n// src/diagrams/packet/diagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}