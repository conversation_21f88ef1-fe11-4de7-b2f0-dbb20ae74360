{"version": 3, "file": "7997.1469ff294f8b64fd26ec.js?v=1469ff294f8b64fd26ec", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,oCAAoC;AACvE;AACA;AACA;AACA;AACA;AACA,mCAAmC,oCAAoC;AACvE;AACA;AACA;AACA;AACA;AACA,gCAAgC,oCAAoC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,sCAAsC,eAAe;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,eAAe;AACnD;AACA;AACA;AACA;AACA;AACA,kCAAkC,eAAe;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE,kCAAkC;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,4BAA4B;AAC7B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,2CAA2C;AACzD;AACA,oEAAoE;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,kBAAkB;AAClB,gBAAgB;AAChB,iBAAiB;AACjB,gBAAgB;AAChB,aAAa;AACb,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,4BAA4B,2BAA2B;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,SAAS;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,QAAQ;AAC5D;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,iBAAiB;AACjB,eAAe;AACf;AACA,gCAAgC;AAChC,uBAAuB,sBAAsB,oDAAoD,QAAQ;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,sBAAsB;AACtB,sBAAsB;AACtB,uBAAuB;AACvB;AACA;AACA;AACA;AACA,uBAAuB,WAAW;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,OAAO;AACvC;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,QAAQ;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA,uBAAuB;AACvB,sBAAsB;AACtB,sBAAsB;AACtB,uBAAuB;AACvB;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,8EAA8E,MAAM;AACpF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,GAAG;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,gBAAgB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,oBAAoB;AACpB;AACA,6BAA6B,SAAS;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,gBAAgB;AAChD;AACA;AACA;AACA,eAAe,gBAAgB;AAC/B;AACA;AACA,eAAe,yBAAyB;AACxC;AACA,eAAe,UAAU,yBAAyB;AAClD;AACA,2FAA2F,QAAQ;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE,OAAO;AAC7E,oCAAoC,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,uBAAuB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS,iBAAiB,QAAQ;AAChD,oEAAoE,QAAQ;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,4GAA4G;AACtH;AACA;AACA;AACA;AACA,cAAc,uBAAuB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,KAAK;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,kBAAkB,uBAAuB;AACzC,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,QAAQ;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,oDAAoD,kBAAkB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,uBAAuB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,QAAQ;AAC/B;AACA;AACA;AACA;AACA,2BAA2B,OAAO;AAClC;AACA;AACA,mBAAmB,QAAQ;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,kBAAkB;AAClB,uBAAuB;AACvB,qBAAqB;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,eAAe;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,uBAAuB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,uBAAuB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,UAAU;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,OAAO;AACjB;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,KAAK;AACxD;AACA;AACA,6CAA6C,2BAA2B;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,kBAAkB;AAC7C;AACA,eAAe,oBAAoB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,mBAAmB;AAClC;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEgK", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@lezer/common/dist/index.js"], "sourcesContent": ["/**\nThe default maximum length of a `TreeBuffer` node.\n*/\nconst DefaultBufferLength = 1024;\nlet nextPropID = 0;\nclass Range {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n    }\n}\n/**\nEach [node type](#common.NodeType) or [individual tree](#common.Tree)\ncan have metadata associated with it in props. Instances of this\nclass represent prop names.\n*/\nclass NodeProp {\n    /**\n    Create a new node prop type.\n    */\n    constructor(config = {}) {\n        this.id = nextPropID++;\n        this.perNode = !!config.perNode;\n        this.deserialize = config.deserialize || (() => {\n            throw new Error(\"This node type doesn't define a deserialize function\");\n        });\n    }\n    /**\n    This is meant to be used with\n    [`NodeSet.extend`](#common.NodeSet.extend) or\n    [`LRParser.configure`](#lr.ParserConfig.props) to compute\n    prop values for each node type in the set. Takes a [match\n    object](#common.NodeType^match) or function that returns undefined\n    if the node type doesn't get this prop, and the prop's value if\n    it does.\n    */\n    add(match) {\n        if (this.perNode)\n            throw new RangeError(\"Can't add per-node props to node types\");\n        if (typeof match != \"function\")\n            match = NodeType.match(match);\n        return (type) => {\n            let result = match(type);\n            return result === undefined ? null : [this, result];\n        };\n    }\n}\n/**\nProp that is used to describe matching delimiters. For opening\ndelimiters, this holds an array of node names (written as a\nspace-separated string when declaring this prop in a grammar)\nfor the node types of closing delimiters that match it.\n*/\nNodeProp.closedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nThe inverse of [`closedBy`](#common.NodeProp^closedBy). This is\nattached to closing delimiters, holding an array of node names\nof types of matching opening delimiters.\n*/\nNodeProp.openedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nUsed to assign node types to groups (for example, all node\ntypes that represent an expression could be tagged with an\n`\"Expression\"` group).\n*/\nNodeProp.group = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nAttached to nodes to indicate these should be\n[displayed](https://codemirror.net/docs/ref/#language.syntaxTree)\nin a bidirectional text isolate, so that direction-neutral\ncharacters on their sides don't incorrectly get associated with\nsurrounding text. You'll generally want to set this for nodes\nthat contain arbitrary text, like strings and comments, and for\nnodes that appear _inside_ arbitrary text, like HTML tags. When\nnot given a value, in a grammar declaration, defaults to\n`\"auto\"`.\n*/\nNodeProp.isolate = new NodeProp({ deserialize: value => {\n        if (value && value != \"rtl\" && value != \"ltr\" && value != \"auto\")\n            throw new RangeError(\"Invalid value for isolate: \" + value);\n        return value || \"auto\";\n    } });\n/**\nThe hash of the [context](#lr.ContextTracker.constructor)\nthat the node was parsed in, if any. Used to limit reuse of\ncontextual nodes.\n*/\nNodeProp.contextHash = new NodeProp({ perNode: true });\n/**\nThe distance beyond the end of the node that the tokenizer\nlooked ahead for any of the tokens inside the node. (The LR\nparser only stores this when it is larger than 25, for\nefficiency reasons.)\n*/\nNodeProp.lookAhead = new NodeProp({ perNode: true });\n/**\nThis per-node prop is used to replace a given node, or part of a\nnode, with another tree. This is useful to include trees from\ndifferent languages in mixed-language parsers.\n*/\nNodeProp.mounted = new NodeProp({ perNode: true });\n/**\nA mounted tree, which can be [stored](#common.NodeProp^mounted) on\na tree node to indicate that parts of its content are\nrepresented by another tree.\n*/\nclass MountedTree {\n    constructor(\n    /**\n    The inner tree.\n    */\n    tree, \n    /**\n    If this is null, this tree replaces the entire node (it will\n    be included in the regular iteration instead of its host\n    node). If not, only the given ranges are considered to be\n    covered by this tree. This is used for trees that are mixed in\n    a way that isn't strictly hierarchical. Such mounted trees are\n    only entered by [`resolveInner`](#common.Tree.resolveInner)\n    and [`enter`](#common.SyntaxNode.enter).\n    */\n    overlay, \n    /**\n    The parser used to create this subtree.\n    */\n    parser) {\n        this.tree = tree;\n        this.overlay = overlay;\n        this.parser = parser;\n    }\n    /**\n    @internal\n    */\n    static get(tree) {\n        return tree && tree.props && tree.props[NodeProp.mounted.id];\n    }\n}\nconst noProps = Object.create(null);\n/**\nEach node in a syntax tree has a node type associated with it.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the node type. Not necessarily unique, but if the\n    grammar was written properly, different node types with the\n    same name within a node set should play the same semantic\n    role.\n    */\n    name, \n    /**\n    @internal\n    */\n    props, \n    /**\n    The id of this node in its set. Corresponds to the term ids\n    used in the parser.\n    */\n    id, \n    /**\n    @internal\n    */\n    flags = 0) {\n        this.name = name;\n        this.props = props;\n        this.id = id;\n        this.flags = flags;\n    }\n    /**\n    Define a node type.\n    */\n    static define(spec) {\n        let props = spec.props && spec.props.length ? Object.create(null) : noProps;\n        let flags = (spec.top ? 1 /* NodeFlag.Top */ : 0) | (spec.skipped ? 2 /* NodeFlag.Skipped */ : 0) |\n            (spec.error ? 4 /* NodeFlag.Error */ : 0) | (spec.name == null ? 8 /* NodeFlag.Anonymous */ : 0);\n        let type = new NodeType(spec.name || \"\", props, spec.id, flags);\n        if (spec.props)\n            for (let src of spec.props) {\n                if (!Array.isArray(src))\n                    src = src(type);\n                if (src) {\n                    if (src[0].perNode)\n                        throw new RangeError(\"Can't store a per-node prop on a node type\");\n                    props[src[0].id] = src[1];\n                }\n            }\n        return type;\n    }\n    /**\n    Retrieves a node prop for this type. Will return `undefined` if\n    the prop isn't present on this node.\n    */\n    prop(prop) { return this.props[prop.id]; }\n    /**\n    True when this is the top node of a grammar.\n    */\n    get isTop() { return (this.flags & 1 /* NodeFlag.Top */) > 0; }\n    /**\n    True when this node is produced by a skip rule.\n    */\n    get isSkipped() { return (this.flags & 2 /* NodeFlag.Skipped */) > 0; }\n    /**\n    Indicates whether this is an error node.\n    */\n    get isError() { return (this.flags & 4 /* NodeFlag.Error */) > 0; }\n    /**\n    When true, this node type doesn't correspond to a user-declared\n    named node, for example because it is used to cache repetition.\n    */\n    get isAnonymous() { return (this.flags & 8 /* NodeFlag.Anonymous */) > 0; }\n    /**\n    Returns true when this node's name or one of its\n    [groups](#common.NodeProp^group) matches the given string.\n    */\n    is(name) {\n        if (typeof name == 'string') {\n            if (this.name == name)\n                return true;\n            let group = this.prop(NodeProp.group);\n            return group ? group.indexOf(name) > -1 : false;\n        }\n        return this.id == name;\n    }\n    /**\n    Create a function from node types to arbitrary values by\n    specifying an object whose property names are node or\n    [group](#common.NodeProp^group) names. Often useful with\n    [`NodeProp.add`](#common.NodeProp.add). You can put multiple\n    names, separated by spaces, in a single property name to map\n    multiple node names to a single value.\n    */\n    static match(map) {\n        let direct = Object.create(null);\n        for (let prop in map)\n            for (let name of prop.split(\" \"))\n                direct[name] = map[prop];\n        return (node) => {\n            for (let groups = node.prop(NodeProp.group), i = -1; i < (groups ? groups.length : 0); i++) {\n                let found = direct[i < 0 ? node.name : groups[i]];\n                if (found)\n                    return found;\n            }\n        };\n    }\n}\n/**\nAn empty dummy node type to use when no actual type is available.\n*/\nNodeType.none = new NodeType(\"\", Object.create(null), 0, 8 /* NodeFlag.Anonymous */);\n/**\nA node set holds a collection of node types. It is used to\ncompactly represent trees by storing their type ids, rather than a\nfull pointer to the type object, in a numeric array. Each parser\n[has](#lr.LRParser.nodeSet) a node set, and [tree\nbuffers](#common.TreeBuffer) can only store collections of nodes\nfrom the same set. A set can have a maximum of 2**16 (65536) node\ntypes in it, so that the ids fit into 16-bit typed array slots.\n*/\nclass NodeSet {\n    /**\n    Create a set with the given types. The `id` property of each\n    type should correspond to its position within the array.\n    */\n    constructor(\n    /**\n    The node types in this set, by id.\n    */\n    types) {\n        this.types = types;\n        for (let i = 0; i < types.length; i++)\n            if (types[i].id != i)\n                throw new RangeError(\"Node type ids should correspond to array positions when creating a node set\");\n    }\n    /**\n    Create a copy of this set with some node properties added. The\n    arguments to this method can be created with\n    [`NodeProp.add`](#common.NodeProp.add).\n    */\n    extend(...props) {\n        let newTypes = [];\n        for (let type of this.types) {\n            let newProps = null;\n            for (let source of props) {\n                let add = source(type);\n                if (add) {\n                    if (!newProps)\n                        newProps = Object.assign({}, type.props);\n                    newProps[add[0].id] = add[1];\n                }\n            }\n            newTypes.push(newProps ? new NodeType(type.name, newProps, type.id, type.flags) : type);\n        }\n        return new NodeSet(newTypes);\n    }\n}\nconst CachedNode = new WeakMap(), CachedInnerNode = new WeakMap();\n/**\nOptions that control iteration. Can be combined with the `|`\noperator to enable multiple ones.\n*/\nvar IterMode;\n(function (IterMode) {\n    /**\n    When enabled, iteration will only visit [`Tree`](#common.Tree)\n    objects, not nodes packed into\n    [`TreeBuffer`](#common.TreeBuffer)s.\n    */\n    IterMode[IterMode[\"ExcludeBuffers\"] = 1] = \"ExcludeBuffers\";\n    /**\n    Enable this to make iteration include anonymous nodes (such as\n    the nodes that wrap repeated grammar constructs into a balanced\n    tree).\n    */\n    IterMode[IterMode[\"IncludeAnonymous\"] = 2] = \"IncludeAnonymous\";\n    /**\n    By default, regular [mounted](#common.NodeProp^mounted) nodes\n    replace their base node in iteration. Enable this to ignore them\n    instead.\n    */\n    IterMode[IterMode[\"IgnoreMounts\"] = 4] = \"IgnoreMounts\";\n    /**\n    This option only applies in\n    [`enter`](#common.SyntaxNode.enter)-style methods. It tells the\n    library to not enter mounted overlays if one covers the given\n    position.\n    */\n    IterMode[IterMode[\"IgnoreOverlays\"] = 8] = \"IgnoreOverlays\";\n})(IterMode || (IterMode = {}));\n/**\nA piece of syntax tree. There are two ways to approach these\ntrees: the way they are actually stored in memory, and the\nconvenient way.\n\nSyntax trees are stored as a tree of `Tree` and `TreeBuffer`\nobjects. By packing detail information into `TreeBuffer` leaf\nnodes, the representation is made a lot more memory-efficient.\n\nHowever, when you want to actually work with tree nodes, this\nrepresentation is very awkward, so most client code will want to\nuse the [`TreeCursor`](#common.TreeCursor) or\n[`SyntaxNode`](#common.SyntaxNode) interface instead, which provides\na view on some part of this data structure, and can be used to\nmove around to adjacent nodes.\n*/\nclass Tree {\n    /**\n    Construct a new tree. See also [`Tree.build`](#common.Tree^build).\n    */\n    constructor(\n    /**\n    The type of the top node.\n    */\n    type, \n    /**\n    This node's child nodes.\n    */\n    children, \n    /**\n    The positions (offsets relative to the start of this tree) of\n    the children.\n    */\n    positions, \n    /**\n    The total length of this tree\n    */\n    length, \n    /**\n    Per-node [node props](#common.NodeProp) to associate with this node.\n    */\n    props) {\n        this.type = type;\n        this.children = children;\n        this.positions = positions;\n        this.length = length;\n        /**\n        @internal\n        */\n        this.props = null;\n        if (props && props.length) {\n            this.props = Object.create(null);\n            for (let [prop, value] of props)\n                this.props[typeof prop == \"number\" ? prop : prop.id] = value;\n        }\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let mounted = MountedTree.get(this);\n        if (mounted && !mounted.overlay)\n            return mounted.tree.toString();\n        let children = \"\";\n        for (let ch of this.children) {\n            let str = ch.toString();\n            if (str) {\n                if (children)\n                    children += \",\";\n                children += str;\n            }\n        }\n        return !this.type.name ? children :\n            (/\\W/.test(this.type.name) && !this.type.isError ? JSON.stringify(this.type.name) : this.type.name) +\n                (children.length ? \"(\" + children + \")\" : \"\");\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) positioned at the top of\n    the tree. Mode can be used to [control](#common.IterMode) which\n    nodes the cursor visits.\n    */\n    cursor(mode = 0) {\n        return new TreeCursor(this.topNode, mode);\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) pointing into this tree\n    at the given position and side (see\n    [`moveTo`](#common.TreeCursor.moveTo).\n    */\n    cursorAt(pos, side = 0, mode = 0) {\n        let scope = CachedNode.get(this) || this.topNode;\n        let cursor = new TreeCursor(scope);\n        cursor.moveTo(pos, side);\n        CachedNode.set(this, cursor._tree);\n        return cursor;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) object for the top of the\n    tree.\n    */\n    get topNode() {\n        return new TreeNode(this, 0, 0, null);\n    }\n    /**\n    Get the [syntax node](#common.SyntaxNode) at the given position.\n    If `side` is -1, this will move into nodes that end at the\n    position. If 1, it'll move into nodes that start at the\n    position. With 0, it'll only enter nodes that cover the position\n    from both sides.\n    \n    Note that this will not enter\n    [overlays](#common.MountedTree.overlay), and you often want\n    [`resolveInner`](#common.Tree.resolveInner) instead.\n    */\n    resolve(pos, side = 0) {\n        let node = resolveNode(CachedNode.get(this) || this.topNode, pos, side, false);\n        CachedNode.set(this, node);\n        return node;\n    }\n    /**\n    Like [`resolve`](#common.Tree.resolve), but will enter\n    [overlaid](#common.MountedTree.overlay) nodes, producing a syntax node\n    pointing into the innermost overlaid tree at the given position\n    (with parent links going through all parent structure, including\n    the host trees).\n    */\n    resolveInner(pos, side = 0) {\n        let node = resolveNode(CachedInnerNode.get(this) || this.topNode, pos, side, true);\n        CachedInnerNode.set(this, node);\n        return node;\n    }\n    /**\n    In some situations, it can be useful to iterate through all\n    nodes around a position, including those in overlays that don't\n    directly cover the position. This method gives you an iterator\n    that will produce all nodes, from small to big, around the given\n    position.\n    */\n    resolveStack(pos, side = 0) {\n        return stackIterator(this, pos, side);\n    }\n    /**\n    Iterate over the tree and its children, calling `enter` for any\n    node that touches the `from`/`to` region (if given) before\n    running over such a node's children, and `leave` (if given) when\n    leaving the node. When `enter` returns `false`, that node will\n    not have its children iterated over (or `leave` called).\n    */\n    iterate(spec) {\n        let { enter, leave, from = 0, to = this.length } = spec;\n        let mode = spec.mode || 0, anon = (mode & IterMode.IncludeAnonymous) > 0;\n        for (let c = this.cursor(mode | IterMode.IncludeAnonymous);;) {\n            let entered = false;\n            if (c.from <= to && c.to >= from && (!anon && c.type.isAnonymous || enter(c) !== false)) {\n                if (c.firstChild())\n                    continue;\n                entered = true;\n            }\n            for (;;) {\n                if (entered && leave && (anon || !c.type.isAnonymous))\n                    leave(c);\n                if (c.nextSibling())\n                    break;\n                if (!c.parent())\n                    return;\n                entered = true;\n            }\n        }\n    }\n    /**\n    Get the value of the given [node prop](#common.NodeProp) for this\n    node. Works with both per-node and per-type props.\n    */\n    prop(prop) {\n        return !prop.perNode ? this.type.prop(prop) : this.props ? this.props[prop.id] : undefined;\n    }\n    /**\n    Returns the node's [per-node props](#common.NodeProp.perNode) in a\n    format that can be passed to the [`Tree`](#common.Tree)\n    constructor.\n    */\n    get propValues() {\n        let result = [];\n        if (this.props)\n            for (let id in this.props)\n                result.push([+id, this.props[id]]);\n        return result;\n    }\n    /**\n    Balance the direct children of this tree, producing a copy of\n    which may have children grouped into subtrees with type\n    [`NodeType.none`](#common.NodeType^none).\n    */\n    balance(config = {}) {\n        return this.children.length <= 8 /* Balance.BranchFactor */ ? this :\n            balanceRange(NodeType.none, this.children, this.positions, 0, this.children.length, 0, this.length, (children, positions, length) => new Tree(this.type, children, positions, length, this.propValues), config.makeTree || ((children, positions, length) => new Tree(NodeType.none, children, positions, length)));\n    }\n    /**\n    Build a tree from a postfix-ordered buffer of node information,\n    or a cursor over such a buffer.\n    */\n    static build(data) { return buildTree(data); }\n}\n/**\nThe empty tree\n*/\nTree.empty = new Tree(NodeType.none, [], [], 0);\nclass FlatBufferCursor {\n    constructor(buffer, index) {\n        this.buffer = buffer;\n        this.index = index;\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    get pos() { return this.index; }\n    next() { this.index -= 4; }\n    fork() { return new FlatBufferCursor(this.buffer, this.index); }\n}\n/**\nTree buffers contain (type, start, end, endIndex) quads for each\nnode. In such a buffer, nodes are stored in prefix order (parents\nbefore children, with the endIndex of the parent indicating which\nchildren belong to it).\n*/\nclass TreeBuffer {\n    /**\n    Create a tree buffer.\n    */\n    constructor(\n    /**\n    The buffer's content.\n    */\n    buffer, \n    /**\n    The total length of the group of nodes in the buffer.\n    */\n    length, \n    /**\n    The node set used in this buffer.\n    */\n    set) {\n        this.buffer = buffer;\n        this.length = length;\n        this.set = set;\n    }\n    /**\n    @internal\n    */\n    get type() { return NodeType.none; }\n    /**\n    @internal\n    */\n    toString() {\n        let result = [];\n        for (let index = 0; index < this.buffer.length;) {\n            result.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result.join(\",\");\n    }\n    /**\n    @internal\n    */\n    childString(index) {\n        let id = this.buffer[index], endIndex = this.buffer[index + 3];\n        let type = this.set.types[id], result = type.name;\n        if (/\\W/.test(result) && !type.isError)\n            result = JSON.stringify(result);\n        index += 4;\n        if (endIndex == index)\n            return result;\n        let children = [];\n        while (index < endIndex) {\n            children.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result + \"(\" + children.join(\",\") + \")\";\n    }\n    /**\n    @internal\n    */\n    findChild(startIndex, endIndex, dir, pos, side) {\n        let { buffer } = this, pick = -1;\n        for (let i = startIndex; i != endIndex; i = buffer[i + 3]) {\n            if (checkSide(side, pos, buffer[i + 1], buffer[i + 2])) {\n                pick = i;\n                if (dir > 0)\n                    break;\n            }\n        }\n        return pick;\n    }\n    /**\n    @internal\n    */\n    slice(startI, endI, from) {\n        let b = this.buffer;\n        let copy = new Uint16Array(endI - startI), len = 0;\n        for (let i = startI, j = 0; i < endI;) {\n            copy[j++] = b[i++];\n            copy[j++] = b[i++] - from;\n            let to = copy[j++] = b[i++] - from;\n            copy[j++] = b[i++] - startI;\n            len = Math.max(len, to);\n        }\n        return new TreeBuffer(copy, len, this.set);\n    }\n}\nfunction checkSide(side, pos, from, to) {\n    switch (side) {\n        case -2 /* Side.Before */: return from < pos;\n        case -1 /* Side.AtOrBefore */: return to >= pos && from < pos;\n        case 0 /* Side.Around */: return from < pos && to > pos;\n        case 1 /* Side.AtOrAfter */: return from <= pos && to > pos;\n        case 2 /* Side.After */: return to > pos;\n        case 4 /* Side.DontCare */: return true;\n    }\n}\nfunction resolveNode(node, pos, side, overlays) {\n    var _a;\n    // Move up to a node that actually holds the position, if possible\n    while (node.from == node.to ||\n        (side < 1 ? node.from >= pos : node.from > pos) ||\n        (side > -1 ? node.to <= pos : node.to < pos)) {\n        let parent = !overlays && node instanceof TreeNode && node.index < 0 ? null : node.parent;\n        if (!parent)\n            return node;\n        node = parent;\n    }\n    let mode = overlays ? 0 : IterMode.IgnoreOverlays;\n    // Must go up out of overlays when those do not overlap with pos\n    if (overlays)\n        for (let scan = node, parent = scan.parent; parent; scan = parent, parent = scan.parent) {\n            if (scan instanceof TreeNode && scan.index < 0 && ((_a = parent.enter(pos, side, mode)) === null || _a === void 0 ? void 0 : _a.from) != scan.from)\n                node = parent;\n        }\n    for (;;) {\n        let inner = node.enter(pos, side, mode);\n        if (!inner)\n            return node;\n        node = inner;\n    }\n}\nclass BaseNode {\n    cursor(mode = 0) { return new TreeCursor(this, mode); }\n    getChild(type, before = null, after = null) {\n        let r = getChildren(this, type, before, after);\n        return r.length ? r[0] : null;\n    }\n    getChildren(type, before = null, after = null) {\n        return getChildren(this, type, before, after);\n    }\n    resolve(pos, side = 0) {\n        return resolveNode(this, pos, side, false);\n    }\n    resolveInner(pos, side = 0) {\n        return resolveNode(this, pos, side, true);\n    }\n    matchContext(context) {\n        return matchNodeContext(this, context);\n    }\n    enterUnfinishedNodesBefore(pos) {\n        let scan = this.childBefore(pos), node = this;\n        while (scan) {\n            let last = scan.lastChild;\n            if (!last || last.to != scan.to)\n                break;\n            if (last.type.isError && last.from == last.to) {\n                node = scan;\n                scan = last.prevSibling;\n            }\n            else {\n                scan = last;\n            }\n        }\n        return node;\n    }\n    get node() { return this; }\n    get next() { return this.parent; }\n}\nclass TreeNode extends BaseNode {\n    constructor(_tree, from, \n    // Index in parent node, set to -1 if the node is not a direct child of _parent.node (overlay)\n    index, _parent) {\n        super();\n        this._tree = _tree;\n        this.from = from;\n        this.index = index;\n        this._parent = _parent;\n    }\n    get type() { return this._tree.type; }\n    get name() { return this._tree.type.name; }\n    get to() { return this.from + this._tree.length; }\n    nextChild(i, dir, pos, side, mode = 0) {\n        for (let parent = this;;) {\n            for (let { children, positions } = parent._tree, e = dir > 0 ? children.length : -1; i != e; i += dir) {\n                let next = children[i], start = positions[i] + parent.from;\n                if (!checkSide(side, pos, start, start + next.length))\n                    continue;\n                if (next instanceof TreeBuffer) {\n                    if (mode & IterMode.ExcludeBuffers)\n                        continue;\n                    let index = next.findChild(0, next.buffer.length, dir, pos - start, side);\n                    if (index > -1)\n                        return new BufferNode(new BufferContext(parent, next, i, start), null, index);\n                }\n                else if ((mode & IterMode.IncludeAnonymous) || (!next.type.isAnonymous || hasChild(next))) {\n                    let mounted;\n                    if (!(mode & IterMode.IgnoreMounts) && (mounted = MountedTree.get(next)) && !mounted.overlay)\n                        return new TreeNode(mounted.tree, start, i, parent);\n                    let inner = new TreeNode(next, start, i, parent);\n                    return (mode & IterMode.IncludeAnonymous) || !inner.type.isAnonymous ? inner\n                        : inner.nextChild(dir < 0 ? next.children.length - 1 : 0, dir, pos, side);\n                }\n            }\n            if ((mode & IterMode.IncludeAnonymous) || !parent.type.isAnonymous)\n                return null;\n            if (parent.index >= 0)\n                i = parent.index + dir;\n            else\n                i = dir < 0 ? -1 : parent._parent._tree.children.length;\n            parent = parent._parent;\n            if (!parent)\n                return null;\n        }\n    }\n    get firstChild() { return this.nextChild(0, 1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.nextChild(this._tree.children.length - 1, -1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.nextChild(0, 1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.nextChild(this._tree.children.length - 1, -1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        let mounted;\n        if (!(mode & IterMode.IgnoreOverlays) && (mounted = MountedTree.get(this._tree)) && mounted.overlay) {\n            let rPos = pos - this.from;\n            for (let { from, to } of mounted.overlay) {\n                if ((side > 0 ? from <= rPos : from < rPos) &&\n                    (side < 0 ? to >= rPos : to > rPos))\n                    return new TreeNode(mounted.tree, mounted.overlay[0].from + this.from, -1, this);\n            }\n        }\n        return this.nextChild(0, 1, pos, side, mode);\n    }\n    nextSignificantParent() {\n        let val = this;\n        while (val.type.isAnonymous && val._parent)\n            val = val._parent;\n        return val;\n    }\n    get parent() {\n        return this._parent ? this._parent.nextSignificantParent() : null;\n    }\n    get nextSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index + 1, 1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get prevSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index - 1, -1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get tree() { return this._tree; }\n    toTree() { return this._tree; }\n    /**\n    @internal\n    */\n    toString() { return this._tree.toString(); }\n}\nfunction getChildren(node, type, before, after) {\n    let cur = node.cursor(), result = [];\n    if (!cur.firstChild())\n        return result;\n    if (before != null)\n        for (let found = false; !found;) {\n            found = cur.type.is(before);\n            if (!cur.nextSibling())\n                return result;\n        }\n    for (;;) {\n        if (after != null && cur.type.is(after))\n            return result;\n        if (cur.type.is(type))\n            result.push(cur.node);\n        if (!cur.nextSibling())\n            return after == null ? result : [];\n    }\n}\nfunction matchNodeContext(node, context, i = context.length - 1) {\n    for (let p = node.parent; i >= 0; p = p.parent) {\n        if (!p)\n            return false;\n        if (!p.type.isAnonymous) {\n            if (context[i] && context[i] != p.name)\n                return false;\n            i--;\n        }\n    }\n    return true;\n}\nclass BufferContext {\n    constructor(parent, buffer, index, start) {\n        this.parent = parent;\n        this.buffer = buffer;\n        this.index = index;\n        this.start = start;\n    }\n}\nclass BufferNode extends BaseNode {\n    get name() { return this.type.name; }\n    get from() { return this.context.start + this.context.buffer.buffer[this.index + 1]; }\n    get to() { return this.context.start + this.context.buffer.buffer[this.index + 2]; }\n    constructor(context, _parent, index) {\n        super();\n        this.context = context;\n        this._parent = _parent;\n        this.index = index;\n        this.type = context.buffer.set.types[context.buffer.buffer[index]];\n    }\n    child(dir, pos, side) {\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get firstChild() { return this.child(1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.child(-1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.child(1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.child(-1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        if (mode & IterMode.ExcludeBuffers)\n            return null;\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], side > 0 ? 1 : -1, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get parent() {\n        return this._parent || this.context.parent.nextSignificantParent();\n    }\n    externalSibling(dir) {\n        return this._parent ? null : this.context.parent.nextChild(this.context.index + dir, dir, 0, 4 /* Side.DontCare */);\n    }\n    get nextSibling() {\n        let { buffer } = this.context;\n        let after = buffer.buffer[this.index + 3];\n        if (after < (this._parent ? buffer.buffer[this._parent.index + 3] : buffer.buffer.length))\n            return new BufferNode(this.context, this._parent, after);\n        return this.externalSibling(1);\n    }\n    get prevSibling() {\n        let { buffer } = this.context;\n        let parentStart = this._parent ? this._parent.index + 4 : 0;\n        if (this.index == parentStart)\n            return this.externalSibling(-1);\n        return new BufferNode(this.context, this._parent, buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n    }\n    get tree() { return null; }\n    toTree() {\n        let children = [], positions = [];\n        let { buffer } = this.context;\n        let startI = this.index + 4, endI = buffer.buffer[this.index + 3];\n        if (endI > startI) {\n            let from = buffer.buffer[this.index + 1];\n            children.push(buffer.slice(startI, endI, from));\n            positions.push(0);\n        }\n        return new Tree(this.type, children, positions, this.to - this.from);\n    }\n    /**\n    @internal\n    */\n    toString() { return this.context.buffer.childString(this.index); }\n}\nfunction iterStack(heads) {\n    if (!heads.length)\n        return null;\n    let pick = 0, picked = heads[0];\n    for (let i = 1; i < heads.length; i++) {\n        let node = heads[i];\n        if (node.from > picked.from || node.to < picked.to) {\n            picked = node;\n            pick = i;\n        }\n    }\n    let next = picked instanceof TreeNode && picked.index < 0 ? null : picked.parent;\n    let newHeads = heads.slice();\n    if (next)\n        newHeads[pick] = next;\n    else\n        newHeads.splice(pick, 1);\n    return new StackIterator(newHeads, picked);\n}\nclass StackIterator {\n    constructor(heads, node) {\n        this.heads = heads;\n        this.node = node;\n    }\n    get next() { return iterStack(this.heads); }\n}\nfunction stackIterator(tree, pos, side) {\n    let inner = tree.resolveInner(pos, side), layers = null;\n    for (let scan = inner instanceof TreeNode ? inner : inner.context.parent; scan; scan = scan.parent) {\n        if (scan.index < 0) { // This is an overlay root\n            let parent = scan.parent;\n            (layers || (layers = [inner])).push(parent.resolve(pos, side));\n            scan = parent;\n        }\n        else {\n            let mount = MountedTree.get(scan.tree);\n            // Relevant overlay branching off\n            if (mount && mount.overlay && mount.overlay[0].from <= pos && mount.overlay[mount.overlay.length - 1].to >= pos) {\n                let root = new TreeNode(mount.tree, mount.overlay[0].from + scan.from, -1, scan);\n                (layers || (layers = [inner])).push(resolveNode(root, pos, side, false));\n            }\n        }\n    }\n    return layers ? iterStack(layers) : inner;\n}\n/**\nA tree cursor object focuses on a given node in a syntax tree, and\nallows you to move to adjacent nodes.\n*/\nclass TreeCursor {\n    /**\n    Shorthand for `.type.name`.\n    */\n    get name() { return this.type.name; }\n    /**\n    @internal\n    */\n    constructor(node, \n    /**\n    @internal\n    */\n    mode = 0) {\n        this.mode = mode;\n        /**\n        @internal\n        */\n        this.buffer = null;\n        this.stack = [];\n        /**\n        @internal\n        */\n        this.index = 0;\n        this.bufferNode = null;\n        if (node instanceof TreeNode) {\n            this.yieldNode(node);\n        }\n        else {\n            this._tree = node.context.parent;\n            this.buffer = node.context;\n            for (let n = node._parent; n; n = n._parent)\n                this.stack.unshift(n.index);\n            this.bufferNode = node;\n            this.yieldBuf(node.index);\n        }\n    }\n    yieldNode(node) {\n        if (!node)\n            return false;\n        this._tree = node;\n        this.type = node.type;\n        this.from = node.from;\n        this.to = node.to;\n        return true;\n    }\n    yieldBuf(index, type) {\n        this.index = index;\n        let { start, buffer } = this.buffer;\n        this.type = type || buffer.set.types[buffer.buffer[index]];\n        this.from = start + buffer.buffer[index + 1];\n        this.to = start + buffer.buffer[index + 2];\n        return true;\n    }\n    /**\n    @internal\n    */\n    yield(node) {\n        if (!node)\n            return false;\n        if (node instanceof TreeNode) {\n            this.buffer = null;\n            return this.yieldNode(node);\n        }\n        this.buffer = node.context;\n        return this.yieldBuf(node.index, node.type);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.buffer ? this.buffer.buffer.childString(this.index) : this._tree.toString();\n    }\n    /**\n    @internal\n    */\n    enterChild(dir, pos, side) {\n        if (!this.buffer)\n            return this.yield(this._tree.nextChild(dir < 0 ? this._tree._tree.children.length - 1 : 0, dir, pos, side, this.mode));\n        let { buffer } = this.buffer;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.buffer.start, side);\n        if (index < 0)\n            return false;\n        this.stack.push(this.index);\n        return this.yieldBuf(index);\n    }\n    /**\n    Move the cursor to this node's first child. When this returns\n    false, the node has no child, and the cursor has not been moved.\n    */\n    firstChild() { return this.enterChild(1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to this node's last child.\n    */\n    lastChild() { return this.enterChild(-1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to the first child that ends after `pos`.\n    */\n    childAfter(pos) { return this.enterChild(1, pos, 2 /* Side.After */); }\n    /**\n    Move to the last child that starts before `pos`.\n    */\n    childBefore(pos) { return this.enterChild(-1, pos, -2 /* Side.Before */); }\n    /**\n    Move the cursor to the child around `pos`. If side is -1 the\n    child may end at that position, when 1 it may start there. This\n    will also enter [overlaid](#common.MountedTree.overlay)\n    [mounted](#common.NodeProp^mounted) trees unless `overlays` is\n    set to false.\n    */\n    enter(pos, side, mode = this.mode) {\n        if (!this.buffer)\n            return this.yield(this._tree.enter(pos, side, mode));\n        return mode & IterMode.ExcludeBuffers ? false : this.enterChild(1, pos, side);\n    }\n    /**\n    Move to the node's parent node, if this isn't the top node.\n    */\n    parent() {\n        if (!this.buffer)\n            return this.yieldNode((this.mode & IterMode.IncludeAnonymous) ? this._tree._parent : this._tree.parent);\n        if (this.stack.length)\n            return this.yieldBuf(this.stack.pop());\n        let parent = (this.mode & IterMode.IncludeAnonymous) ? this.buffer.parent : this.buffer.parent.nextSignificantParent();\n        this.buffer = null;\n        return this.yieldNode(parent);\n    }\n    /**\n    @internal\n    */\n    sibling(dir) {\n        if (!this.buffer)\n            return !this._tree._parent ? false\n                : this.yield(this._tree.index < 0 ? null\n                    : this._tree._parent.nextChild(this._tree.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode));\n        let { buffer } = this.buffer, d = this.stack.length - 1;\n        if (dir < 0) {\n            let parentStart = d < 0 ? 0 : this.stack[d] + 4;\n            if (this.index != parentStart)\n                return this.yieldBuf(buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n        }\n        else {\n            let after = buffer.buffer[this.index + 3];\n            if (after < (d < 0 ? buffer.buffer.length : buffer.buffer[this.stack[d] + 3]))\n                return this.yieldBuf(after);\n        }\n        return d < 0 ? this.yield(this.buffer.parent.nextChild(this.buffer.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode)) : false;\n    }\n    /**\n    Move to this node's next sibling, if any.\n    */\n    nextSibling() { return this.sibling(1); }\n    /**\n    Move to this node's previous sibling, if any.\n    */\n    prevSibling() { return this.sibling(-1); }\n    atLastNode(dir) {\n        let index, parent, { buffer } = this;\n        if (buffer) {\n            if (dir > 0) {\n                if (this.index < buffer.buffer.buffer.length)\n                    return false;\n            }\n            else {\n                for (let i = 0; i < this.index; i++)\n                    if (buffer.buffer.buffer[i + 3] < this.index)\n                        return false;\n            }\n            ({ index, parent } = buffer);\n        }\n        else {\n            ({ index, _parent: parent } = this._tree);\n        }\n        for (; parent; { index, _parent: parent } = parent) {\n            if (index > -1)\n                for (let i = index + dir, e = dir < 0 ? -1 : parent._tree.children.length; i != e; i += dir) {\n                    let child = parent._tree.children[i];\n                    if ((this.mode & IterMode.IncludeAnonymous) ||\n                        child instanceof TreeBuffer ||\n                        !child.type.isAnonymous ||\n                        hasChild(child))\n                        return false;\n                }\n        }\n        return true;\n    }\n    move(dir, enter) {\n        if (enter && this.enterChild(dir, 0, 4 /* Side.DontCare */))\n            return true;\n        for (;;) {\n            if (this.sibling(dir))\n                return true;\n            if (this.atLastNode(dir) || !this.parent())\n                return false;\n        }\n    }\n    /**\n    Move to the next node in a\n    [pre-order](https://en.wikipedia.org/wiki/Tree_traversal#Pre-order,_NLR)\n    traversal, going from a node to its first child or, if the\n    current node is empty or `enter` is false, its next sibling or\n    the next sibling of the first parent node that has one.\n    */\n    next(enter = true) { return this.move(1, enter); }\n    /**\n    Move to the next node in a last-to-first pre-order traveral. A\n    node is followed by its last child or, if it has none, its\n    previous sibling or the previous sibling of the first parent\n    node that has one.\n    */\n    prev(enter = true) { return this.move(-1, enter); }\n    /**\n    Move the cursor to the innermost node that covers `pos`. If\n    `side` is -1, it will enter nodes that end at `pos`. If it is 1,\n    it will enter nodes that start at `pos`.\n    */\n    moveTo(pos, side = 0) {\n        // Move up to a node that actually holds the position, if possible\n        while (this.from == this.to ||\n            (side < 1 ? this.from >= pos : this.from > pos) ||\n            (side > -1 ? this.to <= pos : this.to < pos))\n            if (!this.parent())\n                break;\n        // Then scan down into child nodes as far as possible\n        while (this.enterChild(1, pos, side)) { }\n        return this;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) at the cursor's current\n    position.\n    */\n    get node() {\n        if (!this.buffer)\n            return this._tree;\n        let cache = this.bufferNode, result = null, depth = 0;\n        if (cache && cache.context == this.buffer) {\n            scan: for (let index = this.index, d = this.stack.length; d >= 0;) {\n                for (let c = cache; c; c = c._parent)\n                    if (c.index == index) {\n                        if (index == this.index)\n                            return c;\n                        result = c;\n                        depth = d + 1;\n                        break scan;\n                    }\n                index = this.stack[--d];\n            }\n        }\n        for (let i = depth; i < this.stack.length; i++)\n            result = new BufferNode(this.buffer, result, this.stack[i]);\n        return this.bufferNode = new BufferNode(this.buffer, result, this.index);\n    }\n    /**\n    Get the [tree](#common.Tree) that represents the current node, if\n    any. Will return null when the node is in a [tree\n    buffer](#common.TreeBuffer).\n    */\n    get tree() {\n        return this.buffer ? null : this._tree._tree;\n    }\n    /**\n    Iterate over the current node and all its descendants, calling\n    `enter` when entering a node and `leave`, if given, when leaving\n    one. When `enter` returns `false`, any children of that node are\n    skipped, and `leave` isn't called for it.\n    */\n    iterate(enter, leave) {\n        for (let depth = 0;;) {\n            let mustLeave = false;\n            if (this.type.isAnonymous || enter(this) !== false) {\n                if (this.firstChild()) {\n                    depth++;\n                    continue;\n                }\n                if (!this.type.isAnonymous)\n                    mustLeave = true;\n            }\n            for (;;) {\n                if (mustLeave && leave)\n                    leave(this);\n                mustLeave = this.type.isAnonymous;\n                if (this.nextSibling())\n                    break;\n                if (!depth)\n                    return;\n                this.parent();\n                depth--;\n                mustLeave = true;\n            }\n        }\n    }\n    /**\n    Test whether the current node matches a given context—a sequence\n    of direct parent node names. Empty strings in the context array\n    are treated as wildcards.\n    */\n    matchContext(context) {\n        if (!this.buffer)\n            return matchNodeContext(this.node, context);\n        let { buffer } = this.buffer, { types } = buffer.set;\n        for (let i = context.length - 1, d = this.stack.length - 1; i >= 0; d--) {\n            if (d < 0)\n                return matchNodeContext(this.node, context, i);\n            let type = types[buffer.buffer[this.stack[d]]];\n            if (!type.isAnonymous) {\n                if (context[i] && context[i] != type.name)\n                    return false;\n                i--;\n            }\n        }\n        return true;\n    }\n}\nfunction hasChild(tree) {\n    return tree.children.some(ch => ch instanceof TreeBuffer || !ch.type.isAnonymous || hasChild(ch));\n}\nfunction buildTree(data) {\n    var _a;\n    let { buffer, nodeSet, maxBufferLength = DefaultBufferLength, reused = [], minRepeatType = nodeSet.types.length } = data;\n    let cursor = Array.isArray(buffer) ? new FlatBufferCursor(buffer, buffer.length) : buffer;\n    let types = nodeSet.types;\n    let contextHash = 0, lookAhead = 0;\n    function takeNode(parentStart, minPos, children, positions, inRepeat, depth) {\n        let { id, start, end, size } = cursor;\n        let lookAheadAtStart = lookAhead;\n        while (size < 0) {\n            cursor.next();\n            if (size == -1 /* SpecialRecord.Reuse */) {\n                let node = reused[id];\n                children.push(node);\n                positions.push(start - parentStart);\n                return;\n            }\n            else if (size == -3 /* SpecialRecord.ContextChange */) { // Context change\n                contextHash = id;\n                return;\n            }\n            else if (size == -4 /* SpecialRecord.LookAhead */) {\n                lookAhead = id;\n                return;\n            }\n            else {\n                throw new RangeError(`Unrecognized record size: ${size}`);\n            }\n        }\n        let type = types[id], node, buffer;\n        let startPos = start - parentStart;\n        if (end - start <= maxBufferLength && (buffer = findBufferSize(cursor.pos - minPos, inRepeat))) {\n            // Small enough for a buffer, and no reused nodes inside\n            let data = new Uint16Array(buffer.size - buffer.skip);\n            let endPos = cursor.pos - buffer.size, index = data.length;\n            while (cursor.pos > endPos)\n                index = copyToBuffer(buffer.start, data, index);\n            node = new TreeBuffer(data, end - buffer.start, nodeSet);\n            startPos = buffer.start - parentStart;\n        }\n        else { // Make it a node\n            let endPos = cursor.pos - size;\n            cursor.next();\n            let localChildren = [], localPositions = [];\n            let localInRepeat = id >= minRepeatType ? id : -1;\n            let lastGroup = 0, lastEnd = end;\n            while (cursor.pos > endPos) {\n                if (localInRepeat >= 0 && cursor.id == localInRepeat && cursor.size >= 0) {\n                    if (cursor.end <= lastEnd - maxBufferLength) {\n                        makeRepeatLeaf(localChildren, localPositions, start, lastGroup, cursor.end, lastEnd, localInRepeat, lookAheadAtStart);\n                        lastGroup = localChildren.length;\n                        lastEnd = cursor.end;\n                    }\n                    cursor.next();\n                }\n                else if (depth > 2500 /* CutOff.Depth */) {\n                    takeFlatNode(start, endPos, localChildren, localPositions);\n                }\n                else {\n                    takeNode(start, endPos, localChildren, localPositions, localInRepeat, depth + 1);\n                }\n            }\n            if (localInRepeat >= 0 && lastGroup > 0 && lastGroup < localChildren.length)\n                makeRepeatLeaf(localChildren, localPositions, start, lastGroup, start, lastEnd, localInRepeat, lookAheadAtStart);\n            localChildren.reverse();\n            localPositions.reverse();\n            if (localInRepeat > -1 && lastGroup > 0) {\n                let make = makeBalanced(type);\n                node = balanceRange(type, localChildren, localPositions, 0, localChildren.length, 0, end - start, make, make);\n            }\n            else {\n                node = makeTree(type, localChildren, localPositions, end - start, lookAheadAtStart - end);\n            }\n        }\n        children.push(node);\n        positions.push(startPos);\n    }\n    function takeFlatNode(parentStart, minPos, children, positions) {\n        let nodes = []; // Temporary, inverted array of leaf nodes found, with absolute positions\n        let nodeCount = 0, stopAt = -1;\n        while (cursor.pos > minPos) {\n            let { id, start, end, size } = cursor;\n            if (size > 4) { // Not a leaf\n                cursor.next();\n            }\n            else if (stopAt > -1 && start < stopAt) {\n                break;\n            }\n            else {\n                if (stopAt < 0)\n                    stopAt = end - maxBufferLength;\n                nodes.push(id, start, end);\n                nodeCount++;\n                cursor.next();\n            }\n        }\n        if (nodeCount) {\n            let buffer = new Uint16Array(nodeCount * 4);\n            let start = nodes[nodes.length - 2];\n            for (let i = nodes.length - 3, j = 0; i >= 0; i -= 3) {\n                buffer[j++] = nodes[i];\n                buffer[j++] = nodes[i + 1] - start;\n                buffer[j++] = nodes[i + 2] - start;\n                buffer[j++] = j;\n            }\n            children.push(new TreeBuffer(buffer, nodes[2] - start, nodeSet));\n            positions.push(start - parentStart);\n        }\n    }\n    function makeBalanced(type) {\n        return (children, positions, length) => {\n            let lookAhead = 0, lastI = children.length - 1, last, lookAheadProp;\n            if (lastI >= 0 && (last = children[lastI]) instanceof Tree) {\n                if (!lastI && last.type == type && last.length == length)\n                    return last;\n                if (lookAheadProp = last.prop(NodeProp.lookAhead))\n                    lookAhead = positions[lastI] + last.length + lookAheadProp;\n            }\n            return makeTree(type, children, positions, length, lookAhead);\n        };\n    }\n    function makeRepeatLeaf(children, positions, base, i, from, to, type, lookAhead) {\n        let localChildren = [], localPositions = [];\n        while (children.length > i) {\n            localChildren.push(children.pop());\n            localPositions.push(positions.pop() + base - from);\n        }\n        children.push(makeTree(nodeSet.types[type], localChildren, localPositions, to - from, lookAhead - to));\n        positions.push(from - base);\n    }\n    function makeTree(type, children, positions, length, lookAhead = 0, props) {\n        if (contextHash) {\n            let pair = [NodeProp.contextHash, contextHash];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        if (lookAhead > 25) {\n            let pair = [NodeProp.lookAhead, lookAhead];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        return new Tree(type, children, positions, length, props);\n    }\n    function findBufferSize(maxSize, inRepeat) {\n        // Scan through the buffer to find previous siblings that fit\n        // together in a TreeBuffer, and don't contain any reused nodes\n        // (which can't be stored in a buffer).\n        // If `inRepeat` is > -1, ignore node boundaries of that type for\n        // nesting, but make sure the end falls either at the start\n        // (`maxSize`) or before such a node.\n        let fork = cursor.fork();\n        let size = 0, start = 0, skip = 0, minStart = fork.end - maxBufferLength;\n        let result = { size: 0, start: 0, skip: 0 };\n        scan: for (let minPos = fork.pos - maxSize; fork.pos > minPos;) {\n            let nodeSize = fork.size;\n            // Pretend nested repeat nodes of the same type don't exist\n            if (fork.id == inRepeat && nodeSize >= 0) {\n                // Except that we store the current state as a valid return\n                // value.\n                result.size = size;\n                result.start = start;\n                result.skip = skip;\n                skip += 4;\n                size += 4;\n                fork.next();\n                continue;\n            }\n            let startPos = fork.pos - nodeSize;\n            if (nodeSize < 0 || startPos < minPos || fork.start < minStart)\n                break;\n            let localSkipped = fork.id >= minRepeatType ? 4 : 0;\n            let nodeStart = fork.start;\n            fork.next();\n            while (fork.pos > startPos) {\n                if (fork.size < 0) {\n                    if (fork.size == -3 /* SpecialRecord.ContextChange */)\n                        localSkipped += 4;\n                    else\n                        break scan;\n                }\n                else if (fork.id >= minRepeatType) {\n                    localSkipped += 4;\n                }\n                fork.next();\n            }\n            start = nodeStart;\n            size += nodeSize;\n            skip += localSkipped;\n        }\n        if (inRepeat < 0 || size == maxSize) {\n            result.size = size;\n            result.start = start;\n            result.skip = skip;\n        }\n        return result.size > 4 ? result : undefined;\n    }\n    function copyToBuffer(bufferStart, buffer, index) {\n        let { id, start, end, size } = cursor;\n        cursor.next();\n        if (size >= 0 && id < minRepeatType) {\n            let startIndex = index;\n            if (size > 4) {\n                let endPos = cursor.pos - (size - 4);\n                while (cursor.pos > endPos)\n                    index = copyToBuffer(bufferStart, buffer, index);\n            }\n            buffer[--index] = startIndex;\n            buffer[--index] = end - bufferStart;\n            buffer[--index] = start - bufferStart;\n            buffer[--index] = id;\n        }\n        else if (size == -3 /* SpecialRecord.ContextChange */) {\n            contextHash = id;\n        }\n        else if (size == -4 /* SpecialRecord.LookAhead */) {\n            lookAhead = id;\n        }\n        return index;\n    }\n    let children = [], positions = [];\n    while (cursor.pos > 0)\n        takeNode(data.start || 0, data.bufferStart || 0, children, positions, -1, 0);\n    let length = (_a = data.length) !== null && _a !== void 0 ? _a : (children.length ? positions[0] + children[0].length : 0);\n    return new Tree(types[data.topID], children.reverse(), positions.reverse(), length);\n}\nconst nodeSizeCache = new WeakMap;\nfunction nodeSize(balanceType, node) {\n    if (!balanceType.isAnonymous || node instanceof TreeBuffer || node.type != balanceType)\n        return 1;\n    let size = nodeSizeCache.get(node);\n    if (size == null) {\n        size = 1;\n        for (let child of node.children) {\n            if (child.type != balanceType || !(child instanceof Tree)) {\n                size = 1;\n                break;\n            }\n            size += nodeSize(balanceType, child);\n        }\n        nodeSizeCache.set(node, size);\n    }\n    return size;\n}\nfunction balanceRange(\n// The type the balanced tree's inner nodes.\nbalanceType, \n// The direct children and their positions\nchildren, positions, \n// The index range in children/positions to use\nfrom, to, \n// The start position of the nodes, relative to their parent.\nstart, \n// Length of the outer node\nlength, \n// Function to build the top node of the balanced tree\nmkTop, \n// Function to build internal nodes for the balanced tree\nmkTree) {\n    let total = 0;\n    for (let i = from; i < to; i++)\n        total += nodeSize(balanceType, children[i]);\n    let maxChild = Math.ceil((total * 1.5) / 8 /* Balance.BranchFactor */);\n    let localChildren = [], localPositions = [];\n    function divide(children, positions, from, to, offset) {\n        for (let i = from; i < to;) {\n            let groupFrom = i, groupStart = positions[i], groupSize = nodeSize(balanceType, children[i]);\n            i++;\n            for (; i < to; i++) {\n                let nextSize = nodeSize(balanceType, children[i]);\n                if (groupSize + nextSize >= maxChild)\n                    break;\n                groupSize += nextSize;\n            }\n            if (i == groupFrom + 1) {\n                if (groupSize > maxChild) {\n                    let only = children[groupFrom]; // Only trees can have a size > 1\n                    divide(only.children, only.positions, 0, only.children.length, positions[groupFrom] + offset);\n                    continue;\n                }\n                localChildren.push(children[groupFrom]);\n            }\n            else {\n                let length = positions[i - 1] + children[i - 1].length - groupStart;\n                localChildren.push(balanceRange(balanceType, children, positions, groupFrom, i, groupStart, length, null, mkTree));\n            }\n            localPositions.push(groupStart + offset - start);\n        }\n    }\n    divide(children, positions, from, to, 0);\n    return (mkTop || mkTree)(localChildren, localPositions, length);\n}\n/**\nProvides a way to associate values with pieces of trees. As long\nas that part of the tree is reused, the associated values can be\nretrieved from an updated tree.\n*/\nclass NodeWeakMap {\n    constructor() {\n        this.map = new WeakMap();\n    }\n    setBuffer(buffer, index, value) {\n        let inner = this.map.get(buffer);\n        if (!inner)\n            this.map.set(buffer, inner = new Map);\n        inner.set(index, value);\n    }\n    getBuffer(buffer, index) {\n        let inner = this.map.get(buffer);\n        return inner && inner.get(index);\n    }\n    /**\n    Set the value for this syntax node.\n    */\n    set(node, value) {\n        if (node instanceof BufferNode)\n            this.setBuffer(node.context.buffer, node.index, value);\n        else if (node instanceof TreeNode)\n            this.map.set(node.tree, value);\n    }\n    /**\n    Retrieve value for this syntax node, if it exists in the map.\n    */\n    get(node) {\n        return node instanceof BufferNode ? this.getBuffer(node.context.buffer, node.index)\n            : node instanceof TreeNode ? this.map.get(node.tree) : undefined;\n    }\n    /**\n    Set the value for the node that a cursor currently points to.\n    */\n    cursorSet(cursor, value) {\n        if (cursor.buffer)\n            this.setBuffer(cursor.buffer.buffer, cursor.index, value);\n        else\n            this.map.set(cursor.tree, value);\n    }\n    /**\n    Retrieve the value for the node that a cursor currently points\n    to.\n    */\n    cursorGet(cursor) {\n        return cursor.buffer ? this.getBuffer(cursor.buffer.buffer, cursor.index) : this.map.get(cursor.tree);\n    }\n}\n\n/**\nTree fragments are used during [incremental\nparsing](#common.Parser.startParse) to track parts of old trees\nthat can be reused in a new parse. An array of fragments is used\nto track regions of an old tree whose nodes might be reused in new\nparses. Use the static\n[`applyChanges`](#common.TreeFragment^applyChanges) method to\nupdate fragments for document changes.\n*/\nclass TreeFragment {\n    /**\n    Construct a tree fragment. You'll usually want to use\n    [`addTree`](#common.TreeFragment^addTree) and\n    [`applyChanges`](#common.TreeFragment^applyChanges) instead of\n    calling this directly.\n    */\n    constructor(\n    /**\n    The start of the unchanged range pointed to by this fragment.\n    This refers to an offset in the _updated_ document (as opposed\n    to the original tree).\n    */\n    from, \n    /**\n    The end of the unchanged range.\n    */\n    to, \n    /**\n    The tree that this fragment is based on.\n    */\n    tree, \n    /**\n    The offset between the fragment's tree and the document that\n    this fragment can be used against. Add this when going from\n    document to tree positions, subtract it to go from tree to\n    document positions.\n    */\n    offset, openStart = false, openEnd = false) {\n        this.from = from;\n        this.to = to;\n        this.tree = tree;\n        this.offset = offset;\n        this.open = (openStart ? 1 /* Open.Start */ : 0) | (openEnd ? 2 /* Open.End */ : 0);\n    }\n    /**\n    Whether the start of the fragment represents the start of a\n    parse, or the end of a change. (In the second case, it may not\n    be safe to reuse some nodes at the start, depending on the\n    parsing algorithm.)\n    */\n    get openStart() { return (this.open & 1 /* Open.Start */) > 0; }\n    /**\n    Whether the end of the fragment represents the end of a\n    full-document parse, or the start of a change.\n    */\n    get openEnd() { return (this.open & 2 /* Open.End */) > 0; }\n    /**\n    Create a set of fragments from a freshly parsed tree, or update\n    an existing set of fragments by replacing the ones that overlap\n    with a tree with content from the new tree. When `partial` is\n    true, the parse is treated as incomplete, and the resulting\n    fragment has [`openEnd`](#common.TreeFragment.openEnd) set to\n    true.\n    */\n    static addTree(tree, fragments = [], partial = false) {\n        let result = [new TreeFragment(0, tree.length, tree, 0, false, partial)];\n        for (let f of fragments)\n            if (f.to > tree.length)\n                result.push(f);\n        return result;\n    }\n    /**\n    Apply a set of edits to an array of fragments, removing or\n    splitting fragments as necessary to remove edited ranges, and\n    adjusting offsets for fragments that moved.\n    */\n    static applyChanges(fragments, changes, minGap = 128) {\n        if (!changes.length)\n            return fragments;\n        let result = [];\n        let fI = 1, nextF = fragments.length ? fragments[0] : null;\n        for (let cI = 0, pos = 0, off = 0;; cI++) {\n            let nextC = cI < changes.length ? changes[cI] : null;\n            let nextPos = nextC ? nextC.fromA : 1e9;\n            if (nextPos - pos >= minGap)\n                while (nextF && nextF.from < nextPos) {\n                    let cut = nextF;\n                    if (pos >= cut.from || nextPos <= cut.to || off) {\n                        let fFrom = Math.max(cut.from, pos) - off, fTo = Math.min(cut.to, nextPos) - off;\n                        cut = fFrom >= fTo ? null : new TreeFragment(fFrom, fTo, cut.tree, cut.offset + off, cI > 0, !!nextC);\n                    }\n                    if (cut)\n                        result.push(cut);\n                    if (nextF.to > nextPos)\n                        break;\n                    nextF = fI < fragments.length ? fragments[fI++] : null;\n                }\n            if (!nextC)\n                break;\n            pos = nextC.toA;\n            off = nextC.toA - nextC.toB;\n        }\n        return result;\n    }\n}\n/**\nA superclass that parsers should extend.\n*/\nclass Parser {\n    /**\n    Start a parse, returning a [partial parse](#common.PartialParse)\n    object. [`fragments`](#common.TreeFragment) can be passed in to\n    make the parse incremental.\n    \n    By default, the entire input is parsed. You can pass `ranges`,\n    which should be a sorted array of non-empty, non-overlapping\n    ranges, to parse only those ranges. The tree returned in that\n    case will start at `ranges[0].from`.\n    */\n    startParse(input, fragments, ranges) {\n        if (typeof input == \"string\")\n            input = new StringInput(input);\n        ranges = !ranges ? [new Range(0, input.length)] : ranges.length ? ranges.map(r => new Range(r.from, r.to)) : [new Range(0, 0)];\n        return this.createParse(input, fragments || [], ranges);\n    }\n    /**\n    Run a full parse, returning the resulting tree.\n    */\n    parse(input, fragments, ranges) {\n        let parse = this.startParse(input, fragments, ranges);\n        for (;;) {\n            let done = parse.advance();\n            if (done)\n                return done;\n        }\n    }\n}\nclass StringInput {\n    constructor(string) {\n        this.string = string;\n    }\n    get length() { return this.string.length; }\n    chunk(from) { return this.string.slice(from); }\n    get lineChunks() { return false; }\n    read(from, to) { return this.string.slice(from, to); }\n}\n\n/**\nCreate a parse wrapper that, after the inner parse completes,\nscans its tree for mixed language regions with the `nest`\nfunction, runs the resulting [inner parses](#common.NestedParse),\nand then [mounts](#common.NodeProp^mounted) their results onto the\ntree.\n*/\nfunction parseMixed(nest) {\n    return (parse, input, fragments, ranges) => new MixedParse(parse, nest, input, fragments, ranges);\n}\nclass InnerParse {\n    constructor(parser, parse, overlay, target, from) {\n        this.parser = parser;\n        this.parse = parse;\n        this.overlay = overlay;\n        this.target = target;\n        this.from = from;\n    }\n}\nfunction checkRanges(ranges) {\n    if (!ranges.length || ranges.some(r => r.from >= r.to))\n        throw new RangeError(\"Invalid inner parse ranges given: \" + JSON.stringify(ranges));\n}\nclass ActiveOverlay {\n    constructor(parser, predicate, mounts, index, start, target, prev) {\n        this.parser = parser;\n        this.predicate = predicate;\n        this.mounts = mounts;\n        this.index = index;\n        this.start = start;\n        this.target = target;\n        this.prev = prev;\n        this.depth = 0;\n        this.ranges = [];\n    }\n}\nconst stoppedInner = new NodeProp({ perNode: true });\nclass MixedParse {\n    constructor(base, nest, input, fragments, ranges) {\n        this.nest = nest;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.inner = [];\n        this.innerDone = 0;\n        this.baseTree = null;\n        this.stoppedAt = null;\n        this.baseParse = base;\n    }\n    advance() {\n        if (this.baseParse) {\n            let done = this.baseParse.advance();\n            if (!done)\n                return null;\n            this.baseParse = null;\n            this.baseTree = done;\n            this.startInner();\n            if (this.stoppedAt != null)\n                for (let inner of this.inner)\n                    inner.parse.stopAt(this.stoppedAt);\n        }\n        if (this.innerDone == this.inner.length) {\n            let result = this.baseTree;\n            if (this.stoppedAt != null)\n                result = new Tree(result.type, result.children, result.positions, result.length, result.propValues.concat([[stoppedInner, this.stoppedAt]]));\n            return result;\n        }\n        let inner = this.inner[this.innerDone], done = inner.parse.advance();\n        if (done) {\n            this.innerDone++;\n            // This is a somewhat dodgy but super helpful hack where we\n            // patch up nodes created by the inner parse (and thus\n            // presumably not aliased anywhere else) to hold the information\n            // about the inner parse.\n            let props = Object.assign(Object.create(null), inner.target.props);\n            props[NodeProp.mounted.id] = new MountedTree(done, inner.overlay, inner.parser);\n            inner.target.props = props;\n        }\n        return null;\n    }\n    get parsedPos() {\n        if (this.baseParse)\n            return 0;\n        let pos = this.input.length;\n        for (let i = this.innerDone; i < this.inner.length; i++) {\n            if (this.inner[i].from < pos)\n                pos = Math.min(pos, this.inner[i].parse.parsedPos);\n        }\n        return pos;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n        if (this.baseParse)\n            this.baseParse.stopAt(pos);\n        else\n            for (let i = this.innerDone; i < this.inner.length; i++)\n                this.inner[i].parse.stopAt(pos);\n    }\n    startInner() {\n        let fragmentCursor = new FragmentCursor(this.fragments);\n        let overlay = null;\n        let covered = null;\n        let cursor = new TreeCursor(new TreeNode(this.baseTree, this.ranges[0].from, 0, null), IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n        scan: for (let nest, isCovered;;) {\n            let enter = true, range;\n            if (this.stoppedAt != null && cursor.from >= this.stoppedAt) {\n                enter = false;\n            }\n            else if (fragmentCursor.hasNode(cursor)) {\n                if (overlay) {\n                    let match = overlay.mounts.find(m => m.frag.from <= cursor.from && m.frag.to >= cursor.to && m.mount.overlay);\n                    if (match)\n                        for (let r of match.mount.overlay) {\n                            let from = r.from + match.pos, to = r.to + match.pos;\n                            if (from >= cursor.from && to <= cursor.to && !overlay.ranges.some(r => r.from < to && r.to > from))\n                                overlay.ranges.push({ from, to });\n                        }\n                }\n                enter = false;\n            }\n            else if (covered && (isCovered = checkCover(covered.ranges, cursor.from, cursor.to))) {\n                enter = isCovered != 2 /* Cover.Full */;\n            }\n            else if (!cursor.type.isAnonymous && (nest = this.nest(cursor, this.input)) &&\n                (cursor.from < cursor.to || !nest.overlay)) {\n                if (!cursor.tree)\n                    materialize(cursor);\n                let oldMounts = fragmentCursor.findMounts(cursor.from, nest.parser);\n                if (typeof nest.overlay == \"function\") {\n                    overlay = new ActiveOverlay(nest.parser, nest.overlay, oldMounts, this.inner.length, cursor.from, cursor.tree, overlay);\n                }\n                else {\n                    let ranges = punchRanges(this.ranges, nest.overlay ||\n                        (cursor.from < cursor.to ? [new Range(cursor.from, cursor.to)] : []));\n                    if (ranges.length)\n                        checkRanges(ranges);\n                    if (ranges.length || !nest.overlay)\n                        this.inner.push(new InnerParse(nest.parser, ranges.length ? nest.parser.startParse(this.input, enterFragments(oldMounts, ranges), ranges)\n                            : nest.parser.startParse(\"\"), nest.overlay ? nest.overlay.map(r => new Range(r.from - cursor.from, r.to - cursor.from)) : null, cursor.tree, ranges.length ? ranges[0].from : cursor.from));\n                    if (!nest.overlay)\n                        enter = false;\n                    else if (ranges.length)\n                        covered = { ranges, depth: 0, prev: covered };\n                }\n            }\n            else if (overlay && (range = overlay.predicate(cursor))) {\n                if (range === true)\n                    range = new Range(cursor.from, cursor.to);\n                if (range.from < range.to)\n                    overlay.ranges.push(range);\n            }\n            if (enter && cursor.firstChild()) {\n                if (overlay)\n                    overlay.depth++;\n                if (covered)\n                    covered.depth++;\n            }\n            else {\n                for (;;) {\n                    if (cursor.nextSibling())\n                        break;\n                    if (!cursor.parent())\n                        break scan;\n                    if (overlay && !--overlay.depth) {\n                        let ranges = punchRanges(this.ranges, overlay.ranges);\n                        if (ranges.length) {\n                            checkRanges(ranges);\n                            this.inner.splice(overlay.index, 0, new InnerParse(overlay.parser, overlay.parser.startParse(this.input, enterFragments(overlay.mounts, ranges), ranges), overlay.ranges.map(r => new Range(r.from - overlay.start, r.to - overlay.start)), overlay.target, ranges[0].from));\n                        }\n                        overlay = overlay.prev;\n                    }\n                    if (covered && !--covered.depth)\n                        covered = covered.prev;\n                }\n            }\n        }\n    }\n}\nfunction checkCover(covered, from, to) {\n    for (let range of covered) {\n        if (range.from >= to)\n            break;\n        if (range.to > from)\n            return range.from <= from && range.to >= to ? 2 /* Cover.Full */ : 1 /* Cover.Partial */;\n    }\n    return 0 /* Cover.None */;\n}\n// Take a piece of buffer and convert it into a stand-alone\n// TreeBuffer.\nfunction sliceBuf(buf, startI, endI, nodes, positions, off) {\n    if (startI < endI) {\n        let from = buf.buffer[startI + 1];\n        nodes.push(buf.slice(startI, endI, from));\n        positions.push(from - off);\n    }\n}\n// This function takes a node that's in a buffer, and converts it, and\n// its parent buffer nodes, into a Tree. This is again acting on the\n// assumption that the trees and buffers have been constructed by the\n// parse that was ran via the mix parser, and thus aren't shared with\n// any other code, making violations of the immutability safe.\nfunction materialize(cursor) {\n    let { node } = cursor, stack = [];\n    let buffer = node.context.buffer;\n    // Scan up to the nearest tree\n    do {\n        stack.push(cursor.index);\n        cursor.parent();\n    } while (!cursor.tree);\n    // Find the index of the buffer in that tree\n    let base = cursor.tree, i = base.children.indexOf(buffer);\n    let buf = base.children[i], b = buf.buffer, newStack = [i];\n    // Split a level in the buffer, putting the nodes before and after\n    // the child that contains `node` into new buffers.\n    function split(startI, endI, type, innerOffset, length, stackPos) {\n        let targetI = stack[stackPos];\n        let children = [], positions = [];\n        sliceBuf(buf, startI, targetI, children, positions, innerOffset);\n        let from = b[targetI + 1], to = b[targetI + 2];\n        newStack.push(children.length);\n        let child = stackPos\n            ? split(targetI + 4, b[targetI + 3], buf.set.types[b[targetI]], from, to - from, stackPos - 1)\n            : node.toTree();\n        children.push(child);\n        positions.push(from - innerOffset);\n        sliceBuf(buf, b[targetI + 3], endI, children, positions, innerOffset);\n        return new Tree(type, children, positions, length);\n    }\n    base.children[i] = split(0, b.length, NodeType.none, 0, buf.length, stack.length - 1);\n    // Move the cursor back to the target node\n    for (let index of newStack) {\n        let tree = cursor.tree.children[index], pos = cursor.tree.positions[index];\n        cursor.yield(new TreeNode(tree, pos + cursor.from, index, cursor._tree));\n    }\n}\nclass StructureCursor {\n    constructor(root, offset) {\n        this.offset = offset;\n        this.done = false;\n        this.cursor = root.cursor(IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n    }\n    // Move to the first node (in pre-order) that starts at or after `pos`.\n    moveTo(pos) {\n        let { cursor } = this, p = pos - this.offset;\n        while (!this.done && cursor.from < p) {\n            if (cursor.to >= pos && cursor.enter(p, 1, IterMode.IgnoreOverlays | IterMode.ExcludeBuffers)) ;\n            else if (!cursor.next(false))\n                this.done = true;\n        }\n    }\n    hasNode(cursor) {\n        this.moveTo(cursor.from);\n        if (!this.done && this.cursor.from + this.offset == cursor.from && this.cursor.tree) {\n            for (let tree = this.cursor.tree;;) {\n                if (tree == cursor.tree)\n                    return true;\n                if (tree.children.length && tree.positions[0] == 0 && tree.children[0] instanceof Tree)\n                    tree = tree.children[0];\n                else\n                    break;\n            }\n        }\n        return false;\n    }\n}\nclass FragmentCursor {\n    constructor(fragments) {\n        var _a;\n        this.fragments = fragments;\n        this.curTo = 0;\n        this.fragI = 0;\n        if (fragments.length) {\n            let first = this.curFrag = fragments[0];\n            this.curTo = (_a = first.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : first.to;\n            this.inner = new StructureCursor(first.tree, -first.offset);\n        }\n        else {\n            this.curFrag = this.inner = null;\n        }\n    }\n    hasNode(node) {\n        while (this.curFrag && node.from >= this.curTo)\n            this.nextFrag();\n        return this.curFrag && this.curFrag.from <= node.from && this.curTo >= node.to && this.inner.hasNode(node);\n    }\n    nextFrag() {\n        var _a;\n        this.fragI++;\n        if (this.fragI == this.fragments.length) {\n            this.curFrag = this.inner = null;\n        }\n        else {\n            let frag = this.curFrag = this.fragments[this.fragI];\n            this.curTo = (_a = frag.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : frag.to;\n            this.inner = new StructureCursor(frag.tree, -frag.offset);\n        }\n    }\n    findMounts(pos, parser) {\n        var _a;\n        let result = [];\n        if (this.inner) {\n            this.inner.cursor.moveTo(pos, 1);\n            for (let pos = this.inner.cursor.node; pos; pos = pos.parent) {\n                let mount = (_a = pos.tree) === null || _a === void 0 ? void 0 : _a.prop(NodeProp.mounted);\n                if (mount && mount.parser == parser) {\n                    for (let i = this.fragI; i < this.fragments.length; i++) {\n                        let frag = this.fragments[i];\n                        if (frag.from >= pos.to)\n                            break;\n                        if (frag.tree == this.curFrag.tree)\n                            result.push({\n                                frag,\n                                pos: pos.from - frag.offset,\n                                mount\n                            });\n                    }\n                }\n            }\n        }\n        return result;\n    }\n}\nfunction punchRanges(outer, ranges) {\n    let copy = null, current = ranges;\n    for (let i = 1, j = 0; i < outer.length; i++) {\n        let gapFrom = outer[i - 1].to, gapTo = outer[i].from;\n        for (; j < current.length; j++) {\n            let r = current[j];\n            if (r.from >= gapTo)\n                break;\n            if (r.to <= gapFrom)\n                continue;\n            if (!copy)\n                current = copy = ranges.slice();\n            if (r.from < gapFrom) {\n                copy[j] = new Range(r.from, gapFrom);\n                if (r.to > gapTo)\n                    copy.splice(j + 1, 0, new Range(gapTo, r.to));\n            }\n            else if (r.to > gapTo) {\n                copy[j--] = new Range(gapTo, r.to);\n            }\n            else {\n                copy.splice(j--, 1);\n            }\n        }\n    }\n    return current;\n}\nfunction findCoverChanges(a, b, from, to) {\n    let iA = 0, iB = 0, inA = false, inB = false, pos = -1e9;\n    let result = [];\n    for (;;) {\n        let nextA = iA == a.length ? 1e9 : inA ? a[iA].to : a[iA].from;\n        let nextB = iB == b.length ? 1e9 : inB ? b[iB].to : b[iB].from;\n        if (inA != inB) {\n            let start = Math.max(pos, from), end = Math.min(nextA, nextB, to);\n            if (start < end)\n                result.push(new Range(start, end));\n        }\n        pos = Math.min(nextA, nextB);\n        if (pos == 1e9)\n            break;\n        if (nextA == pos) {\n            if (!inA)\n                inA = true;\n            else {\n                inA = false;\n                iA++;\n            }\n        }\n        if (nextB == pos) {\n            if (!inB)\n                inB = true;\n            else {\n                inB = false;\n                iB++;\n            }\n        }\n    }\n    return result;\n}\n// Given a number of fragments for the outer tree, and a set of ranges\n// to parse, find fragments for inner trees mounted around those\n// ranges, if any.\nfunction enterFragments(mounts, ranges) {\n    let result = [];\n    for (let { pos, mount, frag } of mounts) {\n        let startPos = pos + (mount.overlay ? mount.overlay[0].from : 0), endPos = startPos + mount.tree.length;\n        let from = Math.max(frag.from, startPos), to = Math.min(frag.to, endPos);\n        if (mount.overlay) {\n            let overlay = mount.overlay.map(r => new Range(r.from + pos, r.to + pos));\n            let changes = findCoverChanges(ranges, overlay, from, to);\n            for (let i = 0, pos = from;; i++) {\n                let last = i == changes.length, end = last ? to : changes[i].from;\n                if (end > pos)\n                    result.push(new TreeFragment(pos, end, mount.tree, -startPos, frag.from >= pos || frag.openStart, frag.to <= end || frag.openEnd));\n                if (last)\n                    break;\n                pos = changes[i].to;\n            }\n        }\n        else {\n            result.push(new TreeFragment(from, to, mount.tree, -startPos, frag.from >= startPos || frag.openStart, frag.to <= endPos || frag.openEnd));\n        }\n    }\n    return result;\n}\n\nexport { DefaultBufferLength, IterMode, MountedTree, NodeProp, NodeSet, NodeType, NodeWeakMap, Parser, Tree, TreeBuffer, TreeCursor, TreeFragment, parseMixed };\n"], "names": [], "sourceRoot": ""}