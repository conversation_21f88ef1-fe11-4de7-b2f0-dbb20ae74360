{"version": 3, "file": "5573.bb30245305dc034dfc1d.js?v=bb30245305dc034dfc1d", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAmD;AACI;AACJ;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA,kCAAkC,4BAAQ;AAC1C;AACA;AACA;AACA;AACA;AACA,cAAc,sCAAe;AAC7B;AACA;AACA,IAAI,6BAAS;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,YAAY,sCAAmB,aAAa,2DAA2D,oBAAoB,IAAI,gBAAgB;AAC/I;AACA,oEAAoE;AACpE;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,uBAAuB;AACxD,eAAe,qBAAW,QAAQ,sCAAmB,kBAAkB,4CAA4C;AACnH;AACA,CAAC,4CAA4C;;;ACtE7C;AACA;AAC0F;AACnB;AACb;AACT;AACuC;AACzB;AACO;AACP;AACxB;AACE;AACI;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,gCAAgC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,yCAAgB,EAAE,qCAAW;AAC5C,eAAe,wCAAc,EAAE,gCAAsB;AACrD;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA,mCAAmC,yBAAM,GAAG,MAAM;AAClD,4BAA4B,kBAAQ;AACpC;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,kBAAI;AACnE;AACA;AACA;AACA;AACA,YAAY,kBAAI;AAChB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gCAAS;AACxB,eAAe,qCAAW;AAC1B;AACA,gBAAgB,WAAW;AAC3B,iFAAiF,wCAAc;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,iBAAiB;AACtF;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,qCAAW;AAC1B;AACA,gBAAgB,WAAW;AAC3B,iFAAiF,wCAAc;AAC/F;AACA;AACA;AACA;AACA;AACA,4BAA4B,oBAAM,MAAM,wBAAU;AAClD;AACA,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,uCAAgB;AAC/B,eAAe,yBAAe,EAAE,8CAAgB,EAAE,qCAAW;AAC7D;AACA,mFAAmF,wCAAc;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,wCAAc;AAC7B,eAAe,gCAAsB;AACrC;AACA,gBAAgB,iBAAiB;AACjC;AACA;AACA;AACA;AACA;AACA,qCAAqC,oCAAa;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,yBAAM,GAAG,MAAM;AAClD;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,wCAAc,EAAE,qCAAW;AAC1C;AACA;AACA,2BAA2B,yBAAM;AACjC;AACA,wCAAwC,aAAa;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,kBAAI,mBAAmB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,oCAAa;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,uCAAgB;AAC/B,eAAe,8CAAgB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,aAAa;AACjC;AACA;AACA;AACA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,wCAAc;AAC7B,eAAe,qCAAc;AAC7B;AACA;AACA;AACA,qCAAqC,oCAAa;AAClD;AACA;AACA;AACA;AACA,oDAAoD,4BAA4B;AAChF;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,uCAAgB;AAC/B;AACA;AACA,wBAAwB,wBAAU;AAClC,6BAA6B,oBAAM;AACnC,yBAAyB,oBAAM;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,wCAAc,EAAE,qCAAW;AAC1C;AACA;AACA;AACA,qCAAqC,oCAAa;AAClD;AACA;AACA;AACA;AACA,2BAA2B,gBAAgB,UAAU,sBAAsB;AAC3E;AACA;AACA,aAAa;AACb;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,yBAAe,EAAE,qCAAW,EAAE,qCAAc;AAC3D;AACA,gBAAgB,kBAAkB;AAClC,iFAAiF,wCAAc;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,qBAAqB;AACrB;AACA,aAAa;AACb;AACA,+CAA+C,oCAAa;AAC5D,SAAS;AACT;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAAe,OAAO,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/notebook-extension/lib/trusted.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/notebook-extension/lib/index.js"], "sourcesContent": ["import { ReactWidget } from '@jupyterlab/apputils';\nimport { NotebookActions } from '@jupyterlab/notebook';\nimport React, { useEffect, useState } from 'react';\n/**\n * Check if a notebook is trusted\n * @param notebook The notebook to check\n * @returns true if the notebook is trusted, false otherwise\n */\nconst isTrusted = (notebook) => {\n    const model = notebook.model;\n    if (!model) {\n        return false;\n    }\n    const cells = Array.from(model.cells);\n    let total = 0;\n    let trusted = 0;\n    for (const currentCell of cells) {\n        if (currentCell.type !== 'code') {\n            continue;\n        }\n        total++;\n        if (currentCell.trusted) {\n            trusted++;\n        }\n    }\n    return trusted === total;\n};\n/**\n * A React component to display the Trusted badge in the menu bar.\n * @param notebook The Notebook\n * @param translator The Translation service\n */\nconst TrustedButton = ({ notebook, translator, }) => {\n    const trans = translator.load('notebook');\n    const [trusted, setTrusted] = useState(isTrusted(notebook));\n    const checkTrust = () => {\n        const v = isTrusted(notebook);\n        setTrusted(v);\n    };\n    const trust = async () => {\n        await NotebookActions.trust(notebook, translator);\n        checkTrust();\n    };\n    useEffect(() => {\n        notebook.modelContentChanged.connect(checkTrust);\n        notebook.activeCellChanged.connect(checkTrust);\n        checkTrust();\n        return () => {\n            notebook.modelContentChanged.disconnect(checkTrust);\n            notebook.activeCellChanged.disconnect(checkTrust);\n        };\n    });\n    return (React.createElement(\"button\", { className: 'jp-NotebookTrustedStatus', style: !trusted ? { cursor: 'pointer' } : { cursor: 'help' }, onClick: () => !trusted && trust(), title: trusted\n            ? trans.__('JavaScript enabled for notebook display')\n            : trans.__('JavaScript disabled for notebook display') }, trusted ? trans.__('Trusted') : trans.__('Not Trusted')));\n};\n/**\n * A namespace for TrustedComponent static methods.\n */\nexport var TrustedComponent;\n(function (TrustedComponent) {\n    /**\n     * Create a new TrustedComponent\n     *\n     * @param notebook The notebook\n     * @param translator The translator\n     */\n    TrustedComponent.create = ({ notebook, translator, }) => {\n        return ReactWidget.create(React.createElement(TrustedButton, { notebook: notebook, translator: translator }));\n    };\n})(TrustedComponent || (TrustedComponent = {}));\n", "// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { DOMUtils, IToolbarWidgetRegistry, ICommandPalette, } from '@jupyterlab/apputils';\nimport { PageConfig, Text, Time, URLExt } from '@jupyterlab/coreutils';\nimport { IDocumentManager } from '@jupyterlab/docmanager';\nimport { IMainMenu } from '@jupyterlab/mainmenu';\nimport { NotebookPanel, INotebookTracker, INotebookTools, } from '@jupyterlab/notebook';\nimport { ISettingRegistry } from '@jupyterlab/settingregistry';\nimport { ITranslator, nullTranslator } from '@jupyterlab/translation';\nimport { INotebookShell } from '@jupyter-notebook/application';\nimport { Poll } from '@lumino/polling';\nimport { Widget } from '@lumino/widgets';\nimport { TrustedComponent } from './trusted';\n/**\n * The class for kernel status errors.\n */\nconst KERNEL_STATUS_ERROR_CLASS = 'jp-NotebookKernelStatus-error';\n/**\n * The class for kernel status warnings.\n */\nconst KERNEL_STATUS_WARN_CLASS = 'jp-NotebookKernelStatus-warn';\n/**\n * The class for kernel status infos.\n */\nconst KERNEL_STATUS_INFO_CLASS = 'jp-NotebookKernelStatus-info';\n/**\n * The class to fade out the kernel status.\n */\nconst KERNEL_STATUS_FADE_OUT_CLASS = 'jp-NotebookKernelStatus-fade';\n/**\n * The class for scrolled outputs\n */\nconst SCROLLED_OUTPUTS_CLASS = 'jp-mod-outputsScrolled';\n/**\n * The class for the full width notebook\n */\nconst FULL_WIDTH_NOTEBOOK_CLASS = 'jp-mod-fullwidth';\n/**\n * The command IDs used by the notebook plugins.\n */\nvar CommandIDs;\n(function (CommandIDs) {\n    /**\n     * A command to open right sidebar for Editing Notebook Metadata\n     */\n    CommandIDs.openEditNotebookMetadata = 'notebook:edit-metadata';\n    /**\n     * A command to toggle full width of the notebook\n     */\n    CommandIDs.toggleFullWidth = 'notebook:toggle-full-width';\n})(CommandIDs || (CommandIDs = {}));\n/**\n * A plugin for the checkpoint indicator\n */\nconst checkpoints = {\n    id: '@jupyter-notebook/notebook-extension:checkpoints',\n    description: 'A plugin for the checkpoint indicator.',\n    autoStart: true,\n    requires: [IDocumentManager, ITranslator],\n    optional: [INotebookShell, IToolbarWidgetRegistry],\n    activate: (app, docManager, translator, notebookShell, toolbarRegistry) => {\n        const { shell } = app;\n        const trans = translator.load('notebook');\n        const node = document.createElement('div');\n        if (toolbarRegistry) {\n            toolbarRegistry.addFactory('TopBar', 'checkpoint', (toolbar) => {\n                const widget = new Widget({ node });\n                widget.id = DOMUtils.createDomID();\n                widget.addClass('jp-NotebookCheckpoint');\n                return widget;\n            });\n        }\n        const onChange = async () => {\n            const current = shell.currentWidget;\n            if (!current) {\n                return;\n            }\n            const context = docManager.contextForWidget(current);\n            context === null || context === void 0 ? void 0 : context.fileChanged.disconnect(onChange);\n            context === null || context === void 0 ? void 0 : context.fileChanged.connect(onChange);\n            const checkpoints = await (context === null || context === void 0 ? void 0 : context.listCheckpoints());\n            if (!checkpoints || !checkpoints.length) {\n                return;\n            }\n            const checkpoint = checkpoints[checkpoints.length - 1];\n            node.textContent = trans.__('Last Checkpoint: %1', Time.formatHuman(new Date(checkpoint.last_modified)));\n        };\n        if (notebookShell) {\n            notebookShell.currentChanged.connect(onChange);\n        }\n        new Poll({\n            auto: true,\n            factory: () => onChange(),\n            frequency: {\n                interval: 2000,\n                backoff: false,\n            },\n            standby: 'when-hidden',\n        });\n    },\n};\n/**\n * Add a command to close the browser tab when clicking on \"Close and Shut Down\"\n */\nconst closeTab = {\n    id: '@jupyter-notebook/notebook-extension:close-tab',\n    description: 'Add a command to close the browser tab when clicking on \"Close and Shut Down\".',\n    autoStart: true,\n    requires: [IMainMenu],\n    optional: [ITranslator],\n    activate: (app, menu, translator) => {\n        const { commands } = app;\n        translator = translator !== null && translator !== void 0 ? translator : nullTranslator;\n        const trans = translator.load('notebook');\n        const id = 'notebook:close-and-halt';\n        commands.addCommand(id, {\n            label: trans.__('Close and Shut Down Notebook'),\n            execute: async () => {\n                // Shut the kernel down, without confirmation\n                await commands.execute('notebook:shutdown-kernel', { activate: false });\n                window.close();\n            },\n        });\n        menu.fileMenu.closeAndCleaners.add({\n            id,\n            // use a small rank to it takes precedence over the default\n            // shut down action for the notebook\n            rank: 0,\n        });\n    },\n};\n/**\n * Add a command to open the tree view from the notebook view\n */\nconst openTreeTab = {\n    id: '@jupyter-notebook/notebook-extension:open-tree-tab',\n    description: 'Add a command to open a browser tab on the tree view when clicking \"Open...\".',\n    autoStart: true,\n    optional: [ITranslator],\n    activate: (app, translator) => {\n        const { commands } = app;\n        translator = translator !== null && translator !== void 0 ? translator : nullTranslator;\n        const trans = translator.load('notebook');\n        const id = 'notebook:open-tree-tab';\n        commands.addCommand(id, {\n            label: trans.__('Open…'),\n            execute: async () => {\n                const url = URLExt.join(PageConfig.getBaseUrl(), 'tree');\n                window.open(url);\n            },\n        });\n    },\n};\n/**\n * A plugin to set the notebook to full width.\n */\nconst fullWidthNotebook = {\n    id: '@jupyter-notebook/notebook-extension:full-width-notebook',\n    description: 'A plugin to set the notebook to full width.',\n    autoStart: true,\n    requires: [INotebookTracker],\n    optional: [ICommandPalette, ISettingRegistry, ITranslator],\n    activate: (app, tracker, palette, settingRegistry, translator) => {\n        const trans = (translator !== null && translator !== void 0 ? translator : nullTranslator).load('notebook');\n        let fullWidth = false;\n        const toggleFullWidth = () => {\n            const current = tracker.currentWidget;\n            fullWidth = !fullWidth;\n            if (!current) {\n                return;\n            }\n            const content = current;\n            content.toggleClass(FULL_WIDTH_NOTEBOOK_CLASS, fullWidth);\n        };\n        let notebookSettings;\n        if (settingRegistry) {\n            const loadSettings = settingRegistry.load(fullWidthNotebook.id);\n            const updateSettings = (settings) => {\n                const newFullWidth = settings.get('fullWidthNotebook')\n                    .composite;\n                if (newFullWidth !== fullWidth) {\n                    toggleFullWidth();\n                }\n            };\n            Promise.all([loadSettings, app.restored])\n                .then(([settings]) => {\n                notebookSettings = settings;\n                updateSettings(settings);\n                settings.changed.connect((settings) => {\n                    updateSettings(settings);\n                });\n            })\n                .catch((reason) => {\n                console.error(reason.message);\n            });\n        }\n        app.commands.addCommand(CommandIDs.toggleFullWidth, {\n            label: trans.__('Enable Full Width Notebook'),\n            execute: () => {\n                toggleFullWidth();\n                if (notebookSettings) {\n                    notebookSettings.set('fullWidthNotebook', fullWidth);\n                }\n            },\n            isEnabled: () => tracker.currentWidget !== null,\n            isToggled: () => fullWidth,\n        });\n        if (palette) {\n            palette.addItem({\n                command: CommandIDs.toggleFullWidth,\n                category: 'Notebook Operations',\n            });\n        }\n    },\n};\n/**\n * The kernel logo plugin.\n */\nconst kernelLogo = {\n    id: '@jupyter-notebook/notebook-extension:kernel-logo',\n    description: 'The kernel logo plugin.',\n    autoStart: true,\n    requires: [INotebookShell],\n    optional: [IToolbarWidgetRegistry],\n    activate: (app, shell, toolbarRegistry) => {\n        const { serviceManager } = app;\n        const node = document.createElement('div');\n        const img = document.createElement('img');\n        const onChange = async () => {\n            var _a, _b, _c, _d, _e;\n            const current = shell.currentWidget;\n            if (!(current instanceof NotebookPanel)) {\n                return;\n            }\n            if (!node.hasChildNodes()) {\n                node.appendChild(img);\n            }\n            await current.sessionContext.ready;\n            current.sessionContext.kernelChanged.disconnect(onChange);\n            current.sessionContext.kernelChanged.connect(onChange);\n            const name = (_c = (_b = (_a = current.sessionContext.session) === null || _a === void 0 ? void 0 : _a.kernel) === null || _b === void 0 ? void 0 : _b.name) !== null && _c !== void 0 ? _c : '';\n            const spec = (_e = (_d = serviceManager.kernelspecs) === null || _d === void 0 ? void 0 : _d.specs) === null || _e === void 0 ? void 0 : _e.kernelspecs[name];\n            if (!spec) {\n                node.childNodes[0].remove();\n                return;\n            }\n            const kernelIconUrl = spec.resources['logo-64x64'];\n            if (!kernelIconUrl) {\n                node.childNodes[0].remove();\n                return;\n            }\n            img.src = kernelIconUrl;\n            img.title = spec.display_name;\n        };\n        if (toolbarRegistry) {\n            toolbarRegistry.addFactory('TopBar', 'kernelLogo', (toolbar) => {\n                const widget = new Widget({ node });\n                widget.addClass('jp-NotebookKernelLogo');\n                return widget;\n            });\n        }\n        app.started.then(() => {\n            shell.currentChanged.connect(onChange);\n        });\n    },\n};\n/**\n * A plugin to display the kernel status;\n */\nconst kernelStatus = {\n    id: '@jupyter-notebook/notebook-extension:kernel-status',\n    description: 'A plugin to display the kernel status.',\n    autoStart: true,\n    requires: [INotebookShell, ITranslator],\n    activate: (app, shell, translator) => {\n        const trans = translator.load('notebook');\n        const widget = new Widget();\n        widget.addClass('jp-NotebookKernelStatus');\n        app.shell.add(widget, 'menu', { rank: 10010 });\n        const removeClasses = () => {\n            widget.removeClass(KERNEL_STATUS_ERROR_CLASS);\n            widget.removeClass(KERNEL_STATUS_WARN_CLASS);\n            widget.removeClass(KERNEL_STATUS_INFO_CLASS);\n            widget.removeClass(KERNEL_STATUS_FADE_OUT_CLASS);\n        };\n        const onStatusChanged = (sessionContext) => {\n            const status = sessionContext.kernelDisplayStatus;\n            let text = `Kernel ${Text.titleCase(status)}`;\n            removeClasses();\n            switch (status) {\n                case 'busy':\n                case 'idle':\n                    text = '';\n                    widget.addClass(KERNEL_STATUS_FADE_OUT_CLASS);\n                    break;\n                case 'dead':\n                case 'terminating':\n                    widget.addClass(KERNEL_STATUS_ERROR_CLASS);\n                    break;\n                case 'unknown':\n                    widget.addClass(KERNEL_STATUS_WARN_CLASS);\n                    break;\n                default:\n                    widget.addClass(KERNEL_STATUS_INFO_CLASS);\n                    widget.addClass(KERNEL_STATUS_FADE_OUT_CLASS);\n                    break;\n            }\n            widget.node.textContent = trans.__(text);\n        };\n        const onChange = async () => {\n            const current = shell.currentWidget;\n            if (!(current instanceof NotebookPanel)) {\n                return;\n            }\n            const sessionContext = current.sessionContext;\n            sessionContext.statusChanged.connect(onStatusChanged);\n        };\n        shell.currentChanged.connect(onChange);\n    },\n};\n/**\n * A plugin to enable scrolling for outputs by default.\n * Mimic the logic from the classic notebook, as found here:\n * https://github.com/jupyter/notebook/blob/a9a31c096eeffe1bff4e9164c6a0442e0e13cdb3/notebook/static/notebook/js/outputarea.js#L96-L120\n */\nconst scrollOutput = {\n    id: '@jupyter-notebook/notebook-extension:scroll-output',\n    description: 'A plugin to enable scrolling for outputs by default.',\n    autoStart: true,\n    requires: [INotebookTracker],\n    optional: [ISettingRegistry],\n    activate: async (app, tracker, settingRegistry) => {\n        const autoScrollThreshold = 100;\n        let autoScrollOutputs = true;\n        // decide whether to scroll the output of the cell based on some heuristics\n        const autoScroll = (cell) => {\n            if (!autoScrollOutputs) {\n                // bail if disabled via the settings\n                cell.removeClass(SCROLLED_OUTPUTS_CLASS);\n                return;\n            }\n            const { outputArea } = cell;\n            // respect cells with an explicit scrolled state\n            const scrolled = cell.model.getMetadata('scrolled');\n            if (scrolled !== undefined) {\n                return;\n            }\n            const { node } = outputArea;\n            const height = node.scrollHeight;\n            const fontSize = parseFloat(node.style.fontSize.replace('px', ''));\n            const lineHeight = (fontSize || 14) * 1.3;\n            // do not set via cell.outputScrolled = true, as this would\n            // otherwise synchronize the scrolled state to the notebook metadata\n            const scroll = height > lineHeight * autoScrollThreshold;\n            cell.toggleClass(SCROLLED_OUTPUTS_CLASS, scroll);\n        };\n        const handlers = {};\n        const setAutoScroll = (cell) => {\n            if (cell.model.type === 'code') {\n                const codeCell = cell;\n                const id = codeCell.model.id;\n                autoScroll(codeCell);\n                if (handlers[id]) {\n                    codeCell.outputArea.model.changed.disconnect(handlers[id]);\n                }\n                handlers[id] = () => autoScroll(codeCell);\n                codeCell.outputArea.model.changed.connect(handlers[id]);\n            }\n        };\n        tracker.widgetAdded.connect((sender, notebook) => {\n            var _a;\n            // when the notebook widget is created, process all the cells\n            notebook.sessionContext.ready.then(() => {\n                notebook.content.widgets.forEach(setAutoScroll);\n            });\n            (_a = notebook.model) === null || _a === void 0 ? void 0 : _a.cells.changed.connect((sender, args) => {\n                notebook.content.widgets.forEach(setAutoScroll);\n            });\n        });\n        if (settingRegistry) {\n            const loadSettings = settingRegistry.load(scrollOutput.id);\n            const updateSettings = (settings) => {\n                autoScrollOutputs = settings.get('autoScrollOutputs')\n                    .composite;\n            };\n            Promise.all([loadSettings, app.restored])\n                .then(([settings]) => {\n                updateSettings(settings);\n                settings.changed.connect((settings) => {\n                    updateSettings(settings);\n                });\n            })\n                .catch((reason) => {\n                console.error(reason.message);\n            });\n        }\n    },\n};\n/**\n * A plugin to add the NotebookTools to the side panel;\n */\nconst notebookToolsWidget = {\n    id: '@jupyter-notebook/notebook-extension:notebook-tools',\n    description: 'A plugin to add the NotebookTools to the side panel.',\n    autoStart: true,\n    requires: [INotebookShell],\n    optional: [INotebookTools],\n    activate: (app, shell, notebookTools) => {\n        const onChange = async () => {\n            const current = shell.currentWidget;\n            if (!(current instanceof NotebookPanel)) {\n                return;\n            }\n            // Add the notebook tools in right area.\n            if (notebookTools) {\n                shell.add(notebookTools, 'right', { type: 'Property Inspector' });\n            }\n        };\n        shell.currentChanged.connect(onChange);\n    },\n};\n/**\n * A plugin to update the tab icon based on the kernel status.\n */\nconst tabIcon = {\n    id: '@jupyter-notebook/notebook-extension:tab-icon',\n    description: 'A plugin to update the tab icon based on the kernel status.',\n    autoStart: true,\n    requires: [INotebookTracker],\n    activate: (app, tracker) => {\n        // the favicons are provided by Jupyter Server\n        const baseURL = PageConfig.getBaseUrl();\n        const notebookIcon = URLExt.join(baseURL, 'static/favicons/favicon-notebook.ico');\n        const busyIcon = URLExt.join(baseURL, 'static/favicons/favicon-busy-1.ico');\n        const updateBrowserFavicon = (status) => {\n            const link = document.querySelector(\"link[rel*='icon']\");\n            switch (status) {\n                case 'busy':\n                    link.href = busyIcon;\n                    break;\n                case 'idle':\n                    link.href = notebookIcon;\n                    break;\n            }\n        };\n        const onChange = async () => {\n            const current = tracker.currentWidget;\n            const sessionContext = current === null || current === void 0 ? void 0 : current.sessionContext;\n            if (!sessionContext) {\n                return;\n            }\n            sessionContext.statusChanged.connect(() => {\n                const status = sessionContext.kernelDisplayStatus;\n                updateBrowserFavicon(status);\n            });\n        };\n        tracker.currentChanged.connect(onChange);\n    },\n};\n/**\n * A plugin that adds a Trusted indicator to the menu area\n */\nconst trusted = {\n    id: '@jupyter-notebook/notebook-extension:trusted',\n    description: 'A plugin that adds a Trusted indicator to the menu area.',\n    autoStart: true,\n    requires: [INotebookShell, ITranslator],\n    activate: (app, notebookShell, translator) => {\n        const onChange = async () => {\n            const current = notebookShell.currentWidget;\n            if (!(current instanceof NotebookPanel)) {\n                return;\n            }\n            const notebook = current.content;\n            await current.context.ready;\n            const widget = TrustedComponent.create({ notebook, translator });\n            notebookShell.add(widget, 'menu', {\n                rank: 11000,\n            });\n        };\n        notebookShell.currentChanged.connect(onChange);\n    },\n};\n/**\n * Add a command to open right sidebar for Editing Notebook Metadata when clicking on \"Edit Notebook Metadata\" under Edit menu\n */\nconst editNotebookMetadata = {\n    id: '@jupyter-notebook/notebook-extension:edit-notebook-metadata',\n    description: 'Add a command to open right sidebar for Editing Notebook Metadata when clicking on \"Edit Notebook Metadata\" under Edit menu',\n    autoStart: true,\n    optional: [ICommandPalette, ITranslator, INotebookTools],\n    activate: (app, palette, translator, notebookTools) => {\n        const { commands, shell } = app;\n        translator = translator !== null && translator !== void 0 ? translator : nullTranslator;\n        const trans = translator.load('notebook');\n        commands.addCommand(CommandIDs.openEditNotebookMetadata, {\n            label: trans.__('Edit Notebook Metadata'),\n            execute: async () => {\n                const command = 'application:toggle-panel';\n                const args = {\n                    side: 'right',\n                    title: 'Show Notebook Tools',\n                    id: 'notebook-tools',\n                };\n                // Check if Show Notebook Tools (Right Sidebar) is open (expanded)\n                if (!commands.isToggled(command, args)) {\n                    await commands.execute(command, args).then((_) => {\n                        // For expanding the 'Advanced Tools' section (default: collapsed)\n                        if (notebookTools) {\n                            const tools = (notebookTools === null || notebookTools === void 0 ? void 0 : notebookTools.layout).widgets;\n                            tools.forEach((tool) => {\n                                if (tool.widget.title.label === trans.__('Advanced Tools') &&\n                                    tool.collapsed) {\n                                    tool.toggle();\n                                }\n                            });\n                        }\n                    });\n                }\n            },\n            isVisible: () => shell.currentWidget !== null &&\n                shell.currentWidget instanceof NotebookPanel,\n        });\n        if (palette) {\n            palette.addItem({\n                command: CommandIDs.openEditNotebookMetadata,\n                category: 'Notebook Operations',\n            });\n        }\n    },\n};\n/**\n * Export the plugins as default.\n */\nconst plugins = [\n    checkpoints,\n    closeTab,\n    openTreeTab,\n    editNotebookMetadata,\n    fullWidthNotebook,\n    kernelLogo,\n    kernelStatus,\n    notebookToolsWidget,\n    scrollOutput,\n    tabIcon,\n    trusted,\n];\nexport default plugins;\n"], "names": [], "sourceRoot": ""}