{"version": 3, "file": "2065.e9b5d8d0a8bec3304454.js?v=e9b5d8d0a8bec3304454", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc,GAAG,qBAAqB;AACtC,oBAAoB,mBAAO,CAAC,IAAuB;AACnD,iBAAiB,mBAAO,CAAC,KAAY;AACrC,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,aAAa,mBAAO,CAAC,IAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,UAAU;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,8EAA8E,UAAU;AACxF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,kFAAkF,UAAU;AAC5F;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,+EAA+E,UAAU;AACzF;AACA;AACA;AACA;AACA,wGAAwG,UAAU;AAClH;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,wEAAwE,UAAU;AAClF;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,qCAAqC,8CAA8C;AACnI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,qBAAqB;AACrB;AACA;AACA;AACA,sEAAsE,UAAU;AAChF;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,cAAc;AACd;;;;;;;ACrYa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,aAAa;AACb;;;;;;;AC9Ba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,oBAAoB,mBAAO,CAAC,KAAgB;AAC5C,eAAe,mBAAO,CAAC,KAA2B;AAClD,uBAAuB,mBAAO,CAAC,KAAsB;AACrD,gBAAgB,mBAAO,CAAC,KAAe;AACvC,yBAAyB,mBAAO,CAAC,KAAwB;AACzD,wBAAwB,mBAAO,CAAC,KAAuB;AACvD,mBAAmB,mBAAO,CAAC,KAAkB;AAC7C,kBAAkB,mBAAO,CAAC,IAAiB;AAC3C,mBAAmB,mBAAO,CAAC,IAAkB;AAC7C,qBAAqB,mBAAO,CAAC,KAAoB;AACjD,kBAAkB,mBAAO,CAAC,KAAiB;AAC3C,kCAAkC,mBAAO,CAAC,KAAiC;AAC3E,2BAA2B,mBAAO,CAAC,IAA0B;AAC7D,6BAA6B,mBAAO,CAAC,KAA4B;AACjE,sBAAsB,mBAAO,CAAC,KAAqB;AACnD,uBAAuB,mBAAO,CAAC,KAAsB;AACrD,kBAAkB,mBAAO,CAAC,KAAiB;AAC3C,mBAAmB,mBAAO,CAAC,KAAkB;AAC7C,iCAAiC,mBAAO,CAAC,KAAgC;AACzE,4BAA4B,mBAAO,CAAC,KAA2B;AAC/D,sBAAsB,mBAAO,CAAC,KAAqB;AACnD,6BAA6B,mBAAO,CAAC,KAA4B;AACjE,wBAAwB,mBAAO,CAAC,KAAuB;AACvD,qBAAqB,mBAAO,CAAC,KAAoB;AACjD,qBAAqB,mBAAO,CAAC,KAAoB;AACjD,uBAAuB,mBAAO,CAAC,KAAsB;AACrD,sBAAsB,mBAAO,CAAC,KAAsC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,gDAAgD;AAChG;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,SAAS;AACT;AACA,WAAW;AACX,+CAA+C,+CAA+C;AAC9F;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,WAAW;AACX;AACA,CAAC;AACD,eAAe;AACf;;;;;;;AC7Pa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,uBAAuB,mBAAO,CAAC,KAA0C;AACzE,kBAAkB;AAClB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,cAAc,QAAQ;AACtB,cAAc,YAAY;AAC1B,cAAc,QAAQ;AACtB,CAAC;AACD;;;;;;;ACZa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,YAAY;AACZ,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,gBAAgB,mBAAO,CAAC,KAAmC;AAC3D,YAAY;AACZ,YAAY,aAAa;AACzB,aAAa,OAAO;AACpB,aAAa,OAAO;AACpB,aAAa,aAAa;AAC1B,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,aAAa;AAC3B,cAAc,mBAAmB;AACjC,cAAc,yBAAyB;AACvC,cAAc,YAAY;AAC1B,cAAc,QAAQ;AACtB,cAAc,+BAA+B;AAC7C,cAAc,sBAAsB;AACpC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,YAAY;AAC1B,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,iBAAiB;AAC/B,cAAc,WAAW;AACzB,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,CAAC;AACD;;;;;;;ACpEa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,yBAAyB,mBAAO,CAAC,KAA4C;AAC7E,gDAA+C,EAAE,qCAAqC,2CAA2C,EAAC;AAClI;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,wBAAwB,mBAAO,CAAC,KAA2C;AAC3E,mBAAmB;AACnB,cAAc,QAAQ;AACtB,CAAC;AACD;;;;;;;ACRa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,mBAAmB,mBAAO,CAAC,KAAsC;AACjE,eAAe;AACf,cAAc,QAAQ;AACtB,CAAC;AACD;;;;;;;ACRa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc;AACd,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,kBAAkB,mBAAO,CAAC,KAAqC;AAC/D,cAAc;AACd,YAAY,QAAQ;AACpB,aAAa,qBAAqB;AAClC,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,gBAAgB;AAC9B,cAAc,oBAAoB;AAClC,cAAc,gBAAgB;AAC9B,CAAC;AACD;;;;;;;ACda;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,mBAAmB,mBAAO,CAAC,IAAsC;AACjE,eAAe;AACf,cAAc,SAAS;AACvB,cAAc,QAAQ;AACtB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,SAAS;AACvB,cAAc,sBAAsB;AACpC,cAAc,mBAAmB;AACjC,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,CAAC;AACD;;;;;;;ACtBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,qBAAqB,mBAAO,CAAC,KAAwC;AACrE,iBAAiB;AACjB,aAAa,aAAa;AAC1B,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,cAAc,QAAQ;AACtB,cAAc,mBAAmB;AACjC,cAAc,yBAAyB;AACvC,cAAc,QAAQ;AACtB,cAAc,+BAA+B;AAC7C,cAAc,YAAY;AAC1B,CAAC;AACD;;;;;;;AC3Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc;AACd,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,kBAAkB,mBAAO,CAAC,KAAqC;AAC/D,cAAc;AACd,YAAY,SAAS;AACrB,YAAY,QAAQ;AACpB,YAAY,QAAQ;AACpB,YAAY,aAAa;AACzB,YAAY,QAAQ;AACpB,aAAa,aAAa;AAC1B,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,aAAa;AAC3B,cAAc,mBAAmB;AACjC,cAAc,yBAAyB;AACvC,cAAc,QAAQ;AACtB,cAAc,2BAA2B;AACzC,cAAc,iCAAiC;AAC/C,cAAc,YAAY;AAC1B,cAAc,QAAQ;AACtB,cAAc,+BAA+B;AAC7C,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,qBAAqB;AACnC,cAAc,gBAAgB;AAC9B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,gBAAgB;AAC9B,cAAc,gBAAgB;AAC9B,cAAc,QAAQ;AACtB,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,gBAAgB;AAC9B,cAAc,gBAAgB;AAC9B,cAAc,gBAAgB;AAC9B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,gBAAgB;AAC9B,cAAc,gBAAgB;AAC9B,cAAc,YAAY;AAC1B,cAAc,QAAQ;AACtB,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B,cAAc,QAAQ;AACtB,cAAc,iBAAiB;AAC/B,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,kBAAkB;AAChC,cAAc,YAAY;AAC1B,cAAc,kBAAkB;AAChC,cAAc,QAAQ;AACtB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,kBAAkB;AAChC,cAAc,qBAAqB;AACnC,cAAc,kBAAkB;AAChC,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,kBAAkB;AAChC,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,kBAAkB;AAChC,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,qBAAqB;AACnC,cAAc,qBAAqB;AACnC,cAAc,qBAAqB;AACnC,cAAc,qBAAqB;AACnC,cAAc,qBAAqB;AACnC,cAAc,qBAAqB;AACnC,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,SAAS;AACvB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,qBAAqB;AACnC,cAAc,QAAQ;AACtB,cAAc,qBAAqB;AACnC,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,qBAAqB;AACnC,cAAc,QAAQ;AACtB,cAAc,qBAAqB;AACnC,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,qBAAqB;AACnC,cAAc,QAAQ;AACtB,cAAc,qBAAqB;AACnC,cAAc,qBAAqB;AACnC,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,gBAAgB;AAC9B,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,4BAA4B;AAC1C,cAAc,WAAW;AACzB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,qBAAqB;AACpC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,gBAAgB;AAC/B,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,aAAa;AAC5B,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,qBAAqB;AACpC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,oBAAoB;AACnC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,qBAAqB;AACpC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,qBAAqB;AACpC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,qBAAqB;AACpC,eAAe,iBAAiB;AAChC,eAAe,qBAAqB;AACpC,eAAe,iBAAiB;AAChC,eAAe,qBAAqB;AACpC,eAAe,iBAAiB;AAChC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,iBAAiB;AAChC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,qBAAqB;AACpC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,sBAAsB;AACrC,eAAe,sBAAsB;AACrC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,sBAAsB;AACrC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,sBAAsB;AACrC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,sBAAsB;AACrC,eAAe,kBAAkB;AACjC,eAAe,sBAAsB;AACrC,eAAe,kBAAkB;AACjC,eAAe,sBAAsB;AACrC,eAAe,kBAAkB;AACjC,eAAe,sBAAsB;AACrC,eAAe,sBAAsB;AACrC,eAAe,kBAAkB;AACjC,eAAe,sBAAsB;AACrC,eAAe,sBAAsB;AACrC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,iBAAiB;AAChC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,kBAAkB;AACjC,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,eAAe,gBAAgB;AAC/B,CAAC;AACD;;;;;;;ACrgCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,kCAAkC,mBAAO,CAAC,KAAqD;AAC/F,2BAA2B;AAC3B,aAAa,UAAU;AACvB,aAAa,UAAU;AACvB,CAAC;AACD;;;;;;;ACTa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,2BAA2B,mBAAO,CAAC,KAA8C;AACjF,qBAAqB;AACrB,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,YAAY;AAC1B,CAAC;AACD;;;;;;;ACXa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,6BAA6B,mBAAO,CAAC,KAAgD;AACrF,uBAAuB;AACvB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,YAAY;AAC1B,CAAC;AACD;;;;;;;ACxBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,sBAAsB,mBAAO,CAAC,KAAyC;AACvE,iBAAiB;AACjB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,YAAY;AAC1B,CAAC;AACD;;;;;;;ACxBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,uBAAuB,mBAAO,CAAC,KAA0C;AACzE,8CAA6C,EAAE,qCAAqC,uCAAuC,EAAC;AAC5H;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc;AACd,kBAAkB,mBAAO,CAAC,KAAqC;AAC/D,0CAAyC,EAAE,qCAAqC,8BAA8B,EAAC;AAC/G;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,mBAAmB,mBAAO,CAAC,KAAsC;AACjE,eAAe;AACf,cAAc,QAAQ;AACtB,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,mBAAmB;AACjC,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,CAAC;AACD;;;;;;;ACda;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,iCAAiC,mBAAO,CAAC,KAAoD;AAC7F,2BAA2B;AAC3B,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,CAAC;AACD;;;;;;;ACTa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,4BAA4B,mBAAO,CAAC,KAA+C;AACnF,mDAAkD,EAAE,qCAAqC,iDAAiD,EAAC;AAC3I;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,sBAAsB,mBAAO,CAAC,IAAyC;AACvE,6CAA4C,EAAE,qCAAqC,qCAAqC,EAAC;AACzH;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,6BAA6B,mBAAO,CAAC,KAAgD;AACrF,mDAAkD,EAAE,qCAAqC,kDAAkD,EAAC;AAC5I;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,wBAAwB,mBAAO,CAAC,KAA2C;AAC3E,+CAA8C,EAAE,qCAAqC,yCAAyC,EAAC;AAC/H;;;;;;;ACLa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,qBAAqB,mBAAO,CAAC,KAAwC;AACrE,gBAAgB;AAChB,cAAc,QAAQ;AACtB,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,CAAC;AACD;;;;;;;ACZa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,qBAAqB,mBAAO,CAAC,KAAwC;AACrE,gBAAgB;AAChB,cAAc,QAAQ;AACtB,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,mBAAmB;AACjC,cAAc,mBAAmB;AACjC,CAAC;AACD;;;;;;;ACda;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,oBAAoB,mBAAO,CAAC,KAAmB;AAC/C,uBAAuB,mBAAO,CAAC,KAA0C;AACzE,kBAAkB;AAClB,aAAa,aAAa;AAC1B,cAAc,OAAO;AACrB,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,cAAc,aAAa;AAC3B,CAAC;AACD;;;;;;;ACvBa;AACb;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,GAAG,iBAAiB,GAAG,SAAS,GAAG,SAAS;AAC5D,mBAAmB,mBAAO,CAAC,IAAuB;AAClD,SAAS;AACT,SAAS;AACT,iBAAiB,KAAK;AACtB;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,wFAAwF;AACxF,iCAAiC;AACjC;AACA;AACA,qCAAqC;AACrC;AACA,kFAAkF,UAAU;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,qFAAqF,UAAU;AAC/F;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,kCAAkC;AAClC,+BAA+B;AAC/B;AACA;AACA,6EAA6E;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA,qCAAqC,SAAS;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8GAA8G,qBAAqB,qBAAqB,UAAU;AAClK;AACA;AACA;AACA;AACA,wCAAwC,QAAQ;AAChD;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,wFAAwF,oBAAoB;AAC5G;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,UAAU;AAC9E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;AClba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,2BAA2B,iXAAiX;AAC7b;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;;;;;;;ACrFa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,kBAAkB;AAClB;AACA,gCAAgC,WAAW;AAC3C,gCAAgC,WAAW;AAC3C;AACA,8BAA8B,UAAU;AACxC;AACA;AACA;;;;;;;ACXa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,YAAY;AACZ,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,WAAW;AACxC,gCAAgC,WAAW;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,aAAa;AAC9C,iCAAiC,UAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,SAAS;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,UAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACpQa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,GAAG,cAAc,GAAG,YAAY,GAAG,YAAY,GAAG,YAAY;AAChF,oBAAoB,mBAAO,CAAC,IAAmB;AAC/C,YAAY;AACZ,YAAY;AACZ,YAAY;AACZ,cAAc;AACd,gBAAgB;AAChB,gBAAgB;AAChB,iBAAiB;AACjB,iBAAiB;AACjB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB;AACA,kBAAkB;AAClB;AACA,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB;AAClB,YAAY,wGAAwG;AACpH,YAAY,wGAAwG;AACpH;AACA;AACA,YAAY,4EAA4E;AACxF,YAAY,mGAAmG;AAC/G,YAAY,6CAA6C;AACzD,YAAY,mGAAmG;AAC/G;AACA;AACA,YAAY,gHAAgH;AAC5H,YAAY,+EAA+E;AAC3F,YAAY,gHAAgH;AAC5H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,0HAA0H;AACxI;AACA;AACA;AACA;AACA,cAAc,oFAAoF;AAClG;AACA,cAAc,uFAAuF;AACrG;AACA,cAAc,4FAA4F;AAC1G,cAAc,gFAAgF;AAC9F,cAAc,mFAAmF;AACjG;AACA,cAAc,wFAAwF;AACtG;AACA,cAAc,wFAAwF;AACtG,cAAc,mFAAmF;AACjG,cAAc,mFAAmF;AACjG,cAAc,gFAAgF;AAC9F,cAAc,gFAAgF;AAC9F,cAAc,mFAAmF;AACjG,cAAc,mFAAmF;AACjG,cAAc,mFAAmF;AACjG,cAAc,mFAAmF;AACjG,cAAc,sFAAsF;AACpG,cAAc,sFAAsF;AACpG;AACA,cAAc,sFAAsF;AACpG;AACA,cAAc,uFAAuF;AACrG;AACA,cAAc,gGAAgG;AAC9G,cAAc,sFAAsF;AACpG,cAAc,yFAAyF;AACvG;AACA;AACA,cAAc,6HAA6H;AAC3I;AACA,cAAc,iFAAiF;AAC/F,cAAc,2FAA2F;AACzG,cAAc,2FAA2F;AACzG,cAAc,8FAA8F;AAC5G,cAAc,8FAA8F;AAC5G;AACA;AACA;AACA;AACA;AACA,cAAc,+FAA+F;AAC7G;AACA,cAAc,+FAA+F;AAC7G,cAAc,+FAA+F;AAC7G,cAAc,oFAAoF;AAClG,cAAc,oFAAoF;AAClG,cAAc,0HAA0H;AACxI;AACA;AACA;AACA;AACA,cAAc,sFAAsF;AACpG,cAAc,sFAAsF;AACpG;AACA;AACA;AACA;AACA,cAAc,+FAA+F;AAC7G,cAAc,+FAA+F;AAC7G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,oFAAoF;AAClG,cAAc,wFAAwF;AACtG,cAAc,oFAAoF;AAClG,cAAc,qFAAqF;AACnG,cAAc,wFAAwF;AACtG,cAAc,wFAAwF;AACtG,cAAc,wFAAwF;AACtG,cAAc,wFAAwF;AACtG,cAAc,wFAAwF;AACtG,cAAc,wFAAwF;AACtG,cAAc,wFAAwF;AACtG,cAAc,wFAAwF;AACtG;AACA;AACA;AACA;AACA;AACA;;;;;;;AC1Ja;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,oBAAoB;AACpB;;;;;;;ACJa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,UAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC5Ca;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,UAAU;AAC3C,iCAAiC,UAAU;AAC3C;AACA;;;;;;;AC7Ca;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc;AACd,cAAc;AACd,4BAA4B,UAAU;AACtC,iCAAiC,UAAU;AAC3C,+BAA+B,SAAS;AACxC,8BAA8B,UAAU;AACxC,+BAA+B,UAAU;AACzC,iCAAiC,SAAS;AAC1C,6BAA6B,UAAU;AACvC;AACA,+BAA+B,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA,+BAA+B,UAAU;AACzC;AACA,+BAA+B,SAAS;AACxC,+BAA+B,UAAU;AACzC;AACA,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC;AACA;AACA;AACA,4BAA4B,SAAS;AACrC,+BAA+B,UAAU;AACzC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,iCAAiC,UAAU;AAC3C,iCAAiC,UAAU;AAC3C,iCAAiC,SAAS;AAC1C,+BAA+B,UAAU;AACzC;AACA;AACA,6BAA6B,UAAU;AACvC,+BAA+B,UAAU;AACzC,+BAA+B,SAAS;AACxC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC;AACA,+BAA+B,UAAU;AACzC;AACA,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC;AACA,mCAAmC,UAAU;AAC7C,oCAAoC,UAAU;AAC9C,oCAAoC,UAAU;AAC9C,mCAAmC,UAAU;AAC7C,mCAAmC,UAAU;AAC7C,mCAAmC,SAAS;AAC5C,mCAAmC,UAAU;AAC7C,mCAAmC,UAAU;AAC7C;AACA,gCAAgC,UAAU;AAC1C,8BAA8B,UAAU;AACxC;AACA;AACA;;;;;;;ACjEa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,SAAS;AACtC;AACA;AACA,iCAAiC,UAAU;AAC3C,kCAAkC,UAAU;AAC5C,kCAAkC,UAAU;AAC5C,iCAAiC,UAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,UAAU;AAC5C;AACA;AACA;AACA;;;;;;;ACvDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACnFa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc;AACd,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,UAAU;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,WAAW;AACxC,gCAAgC,WAAW;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,oBAAoB;AACrD;AACA;AACA,iCAAiC,aAAa;AAC9C,gCAAgC,UAAU;AAC1C,iCAAiC,oBAAoB;AACrD;AACA,iCAAiC,oBAAoB;AACrD,gCAAgC,UAAU;AAC1C;AACA,iCAAiC,UAAU;AAC3C;AACA;AACA,gCAAgC,oBAAoB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,mBAAmB;AACpD;AACA,iCAAiC,oBAAoB;AACrD,iCAAiC,oBAAoB;AACrD;AACA,gCAAgC,oBAAoB;AACpD;AACA,iCAAiC,SAAS;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,qBAAqB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,SAAS;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,UAAU;AAC1C;AACA,6BAA6B,UAAU;AACvC;AACA;AACA;AACA;AACA,iCAAiC,UAAU;AAC3C,iCAAiC,UAAU;AAC3C,kCAAkC,UAAU;AAC5C,iCAAiC,UAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,UAAU;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,UAAU;AAC1C,iCAAiC,UAAU;AAC3C;AACA,iCAAiC,UAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,UAAU;AACxC,+BAA+B,WAAW;AAC1C,kCAAkC,qBAAqB;AACvD,+BAA+B,WAAW;AAC1C,8BAA8B,qBAAqB;AACnD,8BAA8B,qBAAqB;AACnD,kCAAkC,WAAW;AAC7C,+BAA+B,qBAAqB;AACpD,8BAA8B,oBAAoB;AAClD,kCAAkC,oBAAoB;AACtD,+BAA+B,oBAAoB;AACnD,+BAA+B,WAAW;AAC1C,8BAA8B,qBAAqB;AACnD,+BAA+B,qBAAqB;AACpD,kCAAkC,WAAW;AAC7C,+BAA+B,qBAAqB;AACpD,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,qBAAqB;AACvD,+BAA+B,oBAAoB;AACnD,kCAAkC,qBAAqB;AACvD,kCAAkC,UAAU;AAC5C,kCAAkC,UAAU;AAC5C,+BAA+B,qBAAqB;AACpD,+BAA+B,UAAU;AACzC,+BAA+B,oBAAoB;AACnD;AACA;AACA,kCAAkC,WAAW;AAC7C,gCAAgC,UAAU;AAC1C,kCAAkC,WAAW;AAC7C,iCAAiC,mBAAmB;AACpD,kCAAkC,WAAW;AAC7C;AACA;AACA;AACA,kCAAkC,WAAW;AAC7C;AACA;AACA,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,qBAAqB;AACvD,kCAAkC,WAAW;AAC7C,iCAAiC,WAAW;AAC5C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,iCAAiC,WAAW;AAC5C,kCAAkC,WAAW;AAC7C,+BAA+B,SAAS;AACxC,+BAA+B,WAAW;AAC1C,kCAAkC,qBAAqB;AACvD,+BAA+B,WAAW;AAC1C,6BAA6B,qBAAqB;AAClD,8BAA8B,oBAAoB;AAClD,kCAAkC,WAAW;AAC7C,+BAA+B,qBAAqB;AACpD,+BAA+B,oBAAoB;AACnD,kCAAkC,oBAAoB;AACtD,+BAA+B,qBAAqB;AACpD,+BAA+B,WAAW;AAC1C,gCAAgC,qBAAqB;AACrD,8BAA8B,qBAAqB;AACnD,kCAAkC,WAAW;AAC7C,+BAA+B,qBAAqB;AACpD,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,qBAAqB;AACvD,+BAA+B,qBAAqB;AACpD,gCAAgC,qBAAqB;AACrD,kCAAkC,UAAU;AAC5C,mCAAmC,UAAU;AAC7C,+BAA+B,WAAW;AAC1C,+BAA+B,UAAU;AACzC,+BAA+B,qBAAqB;AACpD;AACA;AACA,kCAAkC,WAAW;AAC7C,iCAAiC,UAAU;AAC3C,kCAAkC,WAAW;AAC7C,kCAAkC,oBAAoB;AACtD,kCAAkC,WAAW;AAC7C,kCAAkC,aAAa;AAC/C;AACA;AACA;AACA,kCAAkC,WAAW;AAC7C;AACA;AACA,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,iCAAiC,WAAW;AAC5C,kCAAkC,WAAW;AAC7C,kCAAkC,oBAAoB;AACtD,kCAAkC,oBAAoB;AACtD,kCAAkC,oBAAoB;AACtD,iCAAiC,mBAAmB;AACpD,mCAAmC,oBAAoB;AACvD,kCAAkC,mBAAmB;AACrD,kCAAkC,oBAAoB;AACtD,kCAAkC,oBAAoB;AACtD,mCAAmC,oBAAoB;AACvD,kCAAkC,UAAU;AAC5C,kCAAkC,oBAAoB;AACtD,kCAAkC,oBAAoB;AACtD,gCAAgC,kBAAkB;AAClD,kCAAkC,oBAAoB;AACtD,kCAAkC,mBAAmB;AACrD,kCAAkC,oBAAoB;AACtD,iCAAiC,oBAAoB;AACrD,kCAAkC,oBAAoB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,UAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,UAAU;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,UAAU;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,UAAU;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,UAAU;AAC1C,kCAAkC,SAAS;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,UAAU;AAC5C,kCAAkC,UAAU;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,UAAU;AACzC,iCAAiC,SAAS;AAC1C,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,kCAAkC,UAAU;AAC5C,+BAA+B,SAAS;AACxC,+BAA+B,SAAS;AACxC,kCAAkC,UAAU;AAC5C,+BAA+B,UAAU;AACzC;AACA,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,kCAAkC,UAAU;AAC5C,+BAA+B,UAAU;AACzC,kCAAkC,UAAU;AAC5C,+BAA+B,UAAU;AACzC,kCAAkC,UAAU;AAC5C,+BAA+B,UAAU;AACzC,kCAAkC,UAAU;AAC5C,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC;AACA,kCAAkC,UAAU;AAC5C,iCAAiC,UAAU;AAC3C,iCAAiC,UAAU;AAC3C,iCAAiC,UAAU;AAC3C,+BAA+B,UAAU;AACzC,gCAAgC,UAAU;AAC1C;AACA,8BAA8B,UAAU;AACxC,iCAAiC,UAAU;AAC3C,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC;AACA;AACA,gCAAgC,UAAU;AAC1C,kCAAkC,UAAU;AAC5C,kCAAkC,UAAU;AAC5C,+BAA+B,UAAU;AACzC,kCAAkC,UAAU;AAC5C,kCAAkC,UAAU;AAC5C,iCAAiC,SAAS;AAC1C,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,kCAAkC,UAAU;AAC5C,+BAA+B,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,UAAU;AACzC;AACA;AACA;AACA,kCAAkC,SAAS;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,UAAU;AACxC,+BAA+B,WAAW;AAC1C,8BAA8B,qBAAqB;AACnD,+BAA+B,UAAU;AACzC,8BAA8B,qBAAqB;AACnD,+BAA+B,oBAAoB;AACnD,+BAA+B,qBAAqB;AACpD,kCAAkC,WAAW;AAC7C,8BAA8B,oBAAoB;AAClD,+BAA+B,oBAAoB;AACnD,+BAA+B,UAAU;AACzC,8BAA8B,qBAAqB;AACnD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,kCAAkC,WAAW;AAC7C,8BAA8B,qBAAqB;AACnD,+BAA+B,qBAAqB;AACpD,8BAA8B,qBAAqB;AACnD,+BAA+B,oBAAoB;AACnD,+BAA+B,qBAAqB;AACpD,+BAA+B,WAAW;AAC1C,+BAA+B,qBAAqB;AACpD,+BAA+B,oBAAoB;AACnD,+BAA+B,qBAAqB;AACpD,iCAAiC,WAAW;AAC5C,kCAAkC,WAAW;AAC7C,kCAAkC,UAAU;AAC5C,iCAAiC,WAAW;AAC5C,kCAAkC,WAAW;AAC7C,kCAAkC,qBAAqB;AACvD,kCAAkC,WAAW;AAC7C,iCAAiC,WAAW;AAC5C,iCAAiC,WAAW;AAC5C;AACA;AACA,kCAAkC,WAAW;AAC7C,+BAA+B,qBAAqB;AACpD,kCAAkC,UAAU;AAC5C,kCAAkC,WAAW;AAC7C;AACA,kCAAkC,WAAW;AAC7C,kCAAkC,qBAAqB;AACvD;AACA,kCAAkC,oBAAoB;AACtD,gCAAgC,WAAW;AAC3C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,UAAU;AAC5C;AACA,kCAAkC,qBAAqB;AACvD,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,UAAU;AAC5C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C;AACA,+BAA+B,SAAS;AACxC,+BAA+B,WAAW;AAC1C,8BAA8B,oBAAoB;AAClD,+BAA+B,UAAU;AACzC,6BAA6B,qBAAqB;AAClD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,kCAAkC,WAAW;AAC7C,+BAA+B,oBAAoB;AACnD,+BAA+B,qBAAqB;AACpD,+BAA+B,UAAU;AACzC,gCAAgC,qBAAqB;AACrD,8BAA8B,qBAAqB;AACnD,+BAA+B,qBAAqB;AACpD,kCAAkC,WAAW;AAC7C,8BAA8B,qBAAqB;AACnD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,+BAA+B,WAAW;AAC1C,+BAA+B,WAAW;AAC1C,+BAA+B,qBAAqB;AACpD,+BAA+B,WAAW;AAC1C,kCAAkC,WAAW;AAC7C,iCAAiC,WAAW;AAC5C,iCAAiC,UAAU;AAC3C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,qBAAqB;AACvD,gCAAgC,WAAW;AAC3C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C;AACA;AACA,kCAAkC,WAAW;AAC7C,+BAA+B,qBAAqB;AACpD,kCAAkC,UAAU;AAC5C,kCAAkC,WAAW;AAC7C;AACA,kCAAkC,WAAW;AAC7C,kCAAkC,qBAAqB;AACvD;AACA,kCAAkC,qBAAqB;AACvD,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,UAAU;AAC5C;AACA,iCAAiC,qBAAqB;AACtD,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C,kCAAkC,UAAU;AAC5C,kCAAkC,WAAW;AAC7C,kCAAkC,WAAW;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvxCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B,2BAA2B;AAC3B;AACA;AACA;AACA;;;;;;;ACPa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACrDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,uBAAuB;AACvB,4BAA4B,UAAU;AACtC;AACA,+BAA+B,UAAU;AACzC,4BAA4B,UAAU;AACtC;AACA;AACA,iCAAiC,UAAU;AAC3C,6BAA6B,UAAU;AACvC;AACA,8BAA8B,UAAU;AACxC;AACA;AACA;AACA;AACA,2BAA2B,QAAQ;AACnC,6BAA6B,UAAU;AACvC;AACA,0BAA0B,UAAU;AACpC,6BAA6B,UAAU;AACvC,0BAA0B,UAAU;AACpC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC;AACA;AACA,+BAA+B,UAAU;AACzC,4BAA4B,UAAU;AACtC,8BAA8B,SAAS;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,SAAS;AACxC,6BAA6B,UAAU;AACvC,gCAAgC,UAAU;AAC1C;AACA,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,+BAA+B,SAAS;AACxC,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC;AACA,8BAA8B,SAAS;AACvC;AACA,+BAA+B,UAAU;AACzC,+BAA+B,SAAS;AACxC;AACA,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC;AACA,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,SAAS;AACtC,gCAAgC,UAAU;AAC1C,6BAA6B,SAAS;AACtC,6BAA6B,UAAU;AACvC;AACA,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,gCAAgC,UAAU;AAC1C,6BAA6B,SAAS;AACtC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,6BAA6B,UAAU;AACvC,iCAAiC,UAAU;AAC3C,gCAAgC,UAAU;AAC1C,gCAAgC,UAAU;AAC1C,iCAAiC,UAAU;AAC3C,mCAAmC,UAAU;AAC7C,mCAAmC,UAAU;AAC7C,iCAAiC,UAAU;AAC3C;AACA,6BAA6B,QAAQ;AACrC;AACA;AACA;;;;;;;ACvFa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC7Ea;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,kBAAkB;AAClB;;;;;;;ACJa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc;AACd,cAAc;AACd;;;;;;;ACJa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,SAAS;AACrC;AACA;AACA,iCAAiC,UAAU;AAC3C,iCAAiC,UAAU;AAC3C,kCAAkC,UAAU;AAC5C,iCAAiC,UAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,UAAU;AAC5C;AACA;AACA;AACA;;;;;;;ACvDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B;AAC3B,2BAA2B;AAC3B,+BAA+B,oBAAoB;AACnD,+BAA+B,SAAS;AACxC,8BAA8B,SAAS;AACvC,4BAA4B,WAAW;AACvC,+BAA+B,mBAAmB;AAClD,8BAA8B,oBAAoB;AAClD,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,4BAA4B,qBAAqB;AACjD,+BAA+B,oBAAoB;AACnD,+BAA+B,WAAW;AAC1C,+BAA+B,SAAS;AACxC,gCAAgC,SAAS;AACzC,8BAA8B,qBAAqB;AACnD,+BAA+B,UAAU;AACzC,8BAA8B,qBAAqB;AACnD,+BAA+B,UAAU;AACzC,8BAA8B,WAAW;AACzC,+BAA+B,mBAAmB;AAClD,8BAA8B,qBAAqB;AACnD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,gCAAgC,qBAAqB;AACrD,4BAA4B,mBAAmB;AAC/C,+BAA+B,qBAAqB;AACpD,4BAA4B,mBAAmB;AAC/C,gCAAgC,WAAW;AAC3C,gCAAgC,WAAW;AAC3C;AACA;;;;;;;ACjCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,uBAAuB;AACvB,8BAA8B,oBAAoB;AAClD,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,4BAA4B,WAAW;AACvC,+BAA+B,oBAAoB;AACnD,+BAA+B,mBAAmB;AAClD,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,4BAA4B,qBAAqB;AACjD,+BAA+B,oBAAoB;AACnD,+BAA+B,WAAW;AAC1C,8BAA8B,UAAU;AACxC,+BAA+B,UAAU;AACzC,6BAA6B,qBAAqB;AAClD,+BAA+B,UAAU;AACzC,+BAA+B,qBAAqB;AACpD,+BAA+B,UAAU;AACzC,+BAA+B,WAAW;AAC1C,+BAA+B,oBAAoB;AACnD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,4BAA4B,oBAAoB;AAChD,+BAA+B,qBAAqB;AACpD,4BAA4B,oBAAoB;AAChD;AACA;;;;;;;AC/Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACzDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,oBAAoB;AACnD,+BAA+B,SAAS;AACxC,8BAA8B,SAAS;AACvC,4BAA4B,WAAW;AACvC,+BAA+B,mBAAmB;AAClD,8BAA8B,oBAAoB;AAClD,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,4BAA4B,qBAAqB;AACjD,+BAA+B,oBAAoB;AACnD,+BAA+B,WAAW;AAC1C,+BAA+B,SAAS;AACxC,gCAAgC,SAAS;AACzC,8BAA8B,qBAAqB;AACnD,+BAA+B,UAAU;AACzC,8BAA8B,qBAAqB;AACnD,+BAA+B,UAAU;AACzC,8BAA8B,WAAW;AACzC,+BAA+B,mBAAmB;AAClD,8BAA8B,qBAAqB;AACnD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,gCAAgC,qBAAqB;AACrD,4BAA4B,mBAAmB;AAC/C,+BAA+B,qBAAqB;AACpD,4BAA4B,mBAAmB;AAC/C;AACA;;;;;;;ACzCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,oBAAoB;AAClD,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,4BAA4B,WAAW;AACvC,+BAA+B,oBAAoB;AACnD,+BAA+B,mBAAmB;AAClD,+BAA+B,UAAU;AACzC,+BAA+B,UAAU;AACzC,4BAA4B,qBAAqB;AACjD,+BAA+B,oBAAoB;AACnD,+BAA+B,WAAW;AAC1C,8BAA8B,UAAU;AACxC,+BAA+B,UAAU;AACzC,6BAA6B,qBAAqB;AAClD,+BAA+B,UAAU;AACzC,+BAA+B,qBAAqB;AACpD,+BAA+B,UAAU;AACzC,+BAA+B,WAAW;AAC1C,+BAA+B,oBAAoB;AACnD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,+BAA+B,qBAAqB;AACpD,4BAA4B,oBAAoB;AAChD,+BAA+B,qBAAqB;AACpD,4BAA4B,oBAAoB;AAChD;AACA;;;;;;;ACzCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,SAAS;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC7Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,UAAU;AAC7C;AACA,+BAA+B,UAAU;AACzC;AACA;AACA,8BAA8B,SAAS;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,SAAS;AAC3C,+BAA+B,SAAS;AACxC,+BAA+B,UAAU;AACzC,6BAA6B,SAAS;AACtC,6BAA6B,UAAU;AACvC,4BAA4B,SAAS;AACrC,4BAA4B,SAAS;AACrC,8BAA8B,SAAS;AACvC;AACA;;;;;;;AC1Da;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,kBAAkB;AAClB,mCAAmC,UAAU;AAC7C;AACA,+BAA+B,UAAU;AACzC;AACA,gCAAgC,UAAU;AAC1C,gCAAgC,UAAU;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,UAAU;AAC1C;AACA,iCAAiC,UAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC3Ca;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,UAAU,GAAG,iBAAiB,GAAG,UAAU,GAAG,eAAe,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,aAAa,GAAG,gBAAgB;AAC3J,gBAAgB;AAChB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,4BAA4B;AAC5B,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,wBAAwB;AACxB,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/FontData.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/Usage.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/bold-italic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/double-struck.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/fraktur-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/fraktur.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/italic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/largeop.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/monospace.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/normal.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/sans-serif-bold-italic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/sans-serif-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/sans-serif-italic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/sans-serif.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/script-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/script.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/smallop.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/tex-calligraphic-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/tex-calligraphic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/tex-mathit.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/tex-oldstyle-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/tex-oldstyle.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/tex-size3.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/tex-size4.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/chtml/fonts/tex/tex-variant.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/FontData.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/bold-italic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/delimiters.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/double-struck.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/fraktur-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/fraktur.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/italic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/largeop.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/monospace.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/normal.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/sans-serif-bold-italic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/sans-serif-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/sans-serif-italic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/sans-serif.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/script-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/script.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/smallop.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/tex-calligraphic-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/tex-calligraphic.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/tex-mathit.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/tex-oldstyle-bold.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/tex-oldstyle.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/tex-size3.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/tex-size4.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/output/common/fonts/tex/tex-variant.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/lengths.js"], "sourcesContent": ["\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AddCSS = exports.CHTMLFontData = void 0;\nvar FontData_js_1 = require(\"../common/FontData.js\");\nvar Usage_js_1 = require(\"./Usage.js\");\nvar lengths_js_1 = require(\"../../util/lengths.js\");\n__exportStar(require(\"../common/FontData.js\"), exports);\nvar CHTMLFontData = (function (_super) {\n    __extends(CHTMLFontData, _super);\n    function CHTMLFontData() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.charUsage = new Usage_js_1.Usage();\n        _this.delimUsage = new Usage_js_1.Usage();\n        return _this;\n    }\n    CHTMLFontData.charOptions = function (font, n) {\n        return _super.charOptions.call(this, font, n);\n    };\n    CHTMLFontData.prototype.adaptiveCSS = function (adapt) {\n        this.options.adaptiveCSS = adapt;\n    };\n    CHTMLFontData.prototype.clearCache = function () {\n        if (this.options.adaptiveCSS) {\n            this.charUsage.clear();\n            this.delimUsage.clear();\n        }\n    };\n    CHTMLFontData.prototype.createVariant = function (name, inherit, link) {\n        if (inherit === void 0) { inherit = null; }\n        if (link === void 0) { link = null; }\n        _super.prototype.createVariant.call(this, name, inherit, link);\n        var CLASS = this.constructor;\n        this.variant[name].classes = CLASS.defaultVariantClasses[name];\n        this.variant[name].letter = CLASS.defaultVariantLetters[name];\n    };\n    CHTMLFontData.prototype.defineChars = function (name, chars) {\n        var e_1, _a;\n        _super.prototype.defineChars.call(this, name, chars);\n        var letter = this.variant[name].letter;\n        try {\n            for (var _b = __values(Object.keys(chars)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var n = _c.value;\n                var options = CHTMLFontData.charOptions(chars, parseInt(n));\n                if (options.f === undefined) {\n                    options.f = letter;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    Object.defineProperty(CHTMLFontData.prototype, \"styles\", {\n        get: function () {\n            var CLASS = this.constructor;\n            var styles = __assign({}, CLASS.defaultStyles);\n            this.addFontURLs(styles, CLASS.defaultFonts, this.options.fontURL);\n            if (this.options.adaptiveCSS) {\n                this.updateStyles(styles);\n            }\n            else {\n                this.allStyles(styles);\n            }\n            return styles;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CHTMLFontData.prototype.updateStyles = function (styles) {\n        var e_2, _a, e_3, _b;\n        try {\n            for (var _c = __values(this.delimUsage.update()), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var N = _d.value;\n                this.addDelimiterStyles(styles, N, this.delimiters[N]);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        try {\n            for (var _e = __values(this.charUsage.update()), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var _g = __read(_f.value, 2), name_1 = _g[0], N = _g[1];\n                var variant = this.variant[name_1];\n                this.addCharStyles(styles, variant.letter, N, variant.chars[N]);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return styles;\n    };\n    CHTMLFontData.prototype.allStyles = function (styles) {\n        var e_4, _a, e_5, _b, e_6, _c;\n        try {\n            for (var _d = __values(Object.keys(this.delimiters)), _e = _d.next(); !_e.done; _e = _d.next()) {\n                var n = _e.value;\n                var N = parseInt(n);\n                this.addDelimiterStyles(styles, N, this.delimiters[N]);\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        try {\n            for (var _f = __values(Object.keys(this.variant)), _g = _f.next(); !_g.done; _g = _f.next()) {\n                var name_2 = _g.value;\n                var variant = this.variant[name_2];\n                var vletter = variant.letter;\n                try {\n                    for (var _h = (e_6 = void 0, __values(Object.keys(variant.chars))), _j = _h.next(); !_j.done; _j = _h.next()) {\n                        var n = _j.value;\n                        var N = parseInt(n);\n                        var char = variant.chars[N];\n                        if ((char[3] || {}).smp)\n                            continue;\n                        if (char.length < 4) {\n                            char[3] = {};\n                        }\n                        this.addCharStyles(styles, vletter, N, char);\n                    }\n                }\n                catch (e_6_1) { e_6 = { error: e_6_1 }; }\n                finally {\n                    try {\n                        if (_j && !_j.done && (_c = _h.return)) _c.call(_h);\n                    }\n                    finally { if (e_6) throw e_6.error; }\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n    };\n    CHTMLFontData.prototype.addFontURLs = function (styles, fonts, url) {\n        var e_7, _a;\n        try {\n            for (var _b = __values(Object.keys(fonts)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var name_3 = _c.value;\n                var font = __assign({}, fonts[name_3]);\n                font.src = font.src.replace(/%%URL%%/, url);\n                styles[name_3] = font;\n            }\n        }\n        catch (e_7_1) { e_7 = { error: e_7_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_7) throw e_7.error; }\n        }\n    };\n    CHTMLFontData.prototype.addDelimiterStyles = function (styles, n, data) {\n        var c = this.charSelector(n);\n        if (data.c && data.c !== n) {\n            c = this.charSelector(data.c);\n            styles['.mjx-stretched mjx-c' + c + '::before'] = {\n                content: this.charContent(data.c)\n            };\n        }\n        if (!data.stretch)\n            return;\n        if (data.dir === 1) {\n            this.addDelimiterVStyles(styles, c, data);\n        }\n        else {\n            this.addDelimiterHStyles(styles, c, data);\n        }\n    };\n    CHTMLFontData.prototype.addDelimiterVStyles = function (styles, c, data) {\n        var HDW = data.HDW;\n        var _a = __read(data.stretch, 4), beg = _a[0], ext = _a[1], end = _a[2], mid = _a[3];\n        var Hb = this.addDelimiterVPart(styles, c, 'beg', beg, HDW);\n        this.addDelimiterVPart(styles, c, 'ext', ext, HDW);\n        var He = this.addDelimiterVPart(styles, c, 'end', end, HDW);\n        var css = {};\n        if (mid) {\n            var Hm = this.addDelimiterVPart(styles, c, 'mid', mid, HDW);\n            css.height = '50%';\n            styles['mjx-stretchy-v' + c + ' > mjx-mid'] = {\n                'margin-top': this.em(-Hm / 2),\n                'margin-bottom': this.em(-Hm / 2)\n            };\n        }\n        if (Hb) {\n            css['border-top-width'] = this.em0(Hb - .03);\n        }\n        if (He) {\n            css['border-bottom-width'] = this.em0(He - .03);\n            styles['mjx-stretchy-v' + c + ' > mjx-end'] = { 'margin-top': this.em(-He) };\n        }\n        if (Object.keys(css).length) {\n            styles['mjx-stretchy-v' + c + ' > mjx-ext'] = css;\n        }\n    };\n    CHTMLFontData.prototype.addDelimiterVPart = function (styles, c, part, n, HDW) {\n        if (!n)\n            return 0;\n        var data = this.getDelimiterData(n);\n        var dw = (HDW[2] - data[2]) / 2;\n        var css = { content: this.charContent(n) };\n        if (part !== 'ext') {\n            css.padding = this.padding(data, dw);\n        }\n        else {\n            css.width = this.em0(HDW[2]);\n            if (dw) {\n                css['padding-left'] = this.em0(dw);\n            }\n        }\n        styles['mjx-stretchy-v' + c + ' mjx-' + part + ' mjx-c::before'] = css;\n        return data[0] + data[1];\n    };\n    CHTMLFontData.prototype.addDelimiterHStyles = function (styles, c, data) {\n        var _a = __read(data.stretch, 4), beg = _a[0], ext = _a[1], end = _a[2], mid = _a[3];\n        var HDW = data.HDW;\n        this.addDelimiterHPart(styles, c, 'beg', beg, HDW);\n        this.addDelimiterHPart(styles, c, 'ext', ext, HDW);\n        this.addDelimiterHPart(styles, c, 'end', end, HDW);\n        if (mid) {\n            this.addDelimiterHPart(styles, c, 'mid', mid, HDW);\n            styles['mjx-stretchy-h' + c + ' > mjx-ext'] = { width: '50%' };\n        }\n    };\n    CHTMLFontData.prototype.addDelimiterHPart = function (styles, c, part, n, HDW) {\n        if (!n)\n            return;\n        var data = this.getDelimiterData(n);\n        var options = data[3];\n        var css = { content: (options && options.c ? '\"' + options.c + '\"' : this.charContent(n)) };\n        css.padding = this.padding(HDW, 0, -HDW[2]);\n        styles['mjx-stretchy-h' + c + ' mjx-' + part + ' mjx-c::before'] = css;\n    };\n    CHTMLFontData.prototype.addCharStyles = function (styles, vletter, n, data) {\n        var options = data[3];\n        var letter = (options.f !== undefined ? options.f : vletter);\n        var selector = 'mjx-c' + this.charSelector(n) + (letter ? '.TEX-' + letter : '');\n        styles[selector + '::before'] = {\n            padding: this.padding(data, 0, options.ic || 0),\n            content: (options.c != null ? '\"' + options.c + '\"' : this.charContent(n))\n        };\n    };\n    CHTMLFontData.prototype.getDelimiterData = function (n) {\n        return this.getChar('-smallop', n);\n    };\n    CHTMLFontData.prototype.em = function (n) {\n        return (0, lengths_js_1.em)(n);\n    };\n    CHTMLFontData.prototype.em0 = function (n) {\n        return (0, lengths_js_1.em)(Math.max(0, n));\n    };\n    CHTMLFontData.prototype.padding = function (_a, dw, ic) {\n        var _b = __read(_a, 3), h = _b[0], d = _b[1], w = _b[2];\n        if (dw === void 0) { dw = 0; }\n        if (ic === void 0) { ic = 0; }\n        return [h, w + ic, d, dw].map(this.em0).join(' ');\n    };\n    CHTMLFontData.prototype.charContent = function (n) {\n        return '\"' + (n >= 0x20 && n <= 0x7E && n !== 0x22 && n !== 0x27 && n !== 0x5C ?\n            String.fromCharCode(n) : '\\\\' + n.toString(16).toUpperCase()) + '\"';\n    };\n    CHTMLFontData.prototype.charSelector = function (n) {\n        return '.mjx-c' + n.toString(16).toUpperCase();\n    };\n    CHTMLFontData.OPTIONS = __assign(__assign({}, FontData_js_1.FontData.OPTIONS), { fontURL: 'js/output/chtml/fonts/tex-woff-v2' });\n    CHTMLFontData.JAX = 'CHTML';\n    CHTMLFontData.defaultVariantClasses = {};\n    CHTMLFontData.defaultVariantLetters = {};\n    CHTMLFontData.defaultStyles = {\n        'mjx-c::before': {\n            display: 'block',\n            width: 0\n        }\n    };\n    CHTMLFontData.defaultFonts = {\n        '@font-face /* 0 */': {\n            'font-family': 'MJXZERO',\n            src: 'url(\"%%URL%%/MathJax_Zero.woff\") format(\"woff\")'\n        }\n    };\n    return CHTMLFontData;\n}(FontData_js_1.FontData));\nexports.CHTMLFontData = CHTMLFontData;\nfunction AddCSS(font, options) {\n    var e_8, _a;\n    try {\n        for (var _b = __values(Object.keys(options)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var c = _c.value;\n            var n = parseInt(c);\n            Object.assign(FontData_js_1.FontData.charOptions(font, n), options[n]);\n        }\n    }\n    catch (e_8_1) { e_8 = { error: e_8_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_8) throw e_8.error; }\n    }\n    return font;\n}\nexports.AddCSS = AddCSS;\n//# sourceMappingURL=FontData.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Usage = void 0;\nvar Usage = (function () {\n    function Usage() {\n        this.used = new Set();\n        this.needsUpdate = [];\n    }\n    Usage.prototype.add = function (item) {\n        var name = JSON.stringify(item);\n        if (!this.used.has(name)) {\n            this.needsUpdate.push(item);\n        }\n        this.used.add(name);\n    };\n    Usage.prototype.has = function (item) {\n        return this.used.has(JSON.stringify(item));\n    };\n    Usage.prototype.clear = function () {\n        this.used.clear();\n        this.needsUpdate = [];\n    };\n    Usage.prototype.update = function () {\n        var update = this.needsUpdate;\n        this.needsUpdate = [];\n        return update;\n    };\n    return Usage;\n}());\nexports.Usage = Usage;\n//# sourceMappingURL=Usage.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TeXFont = void 0;\nvar FontData_js_1 = require(\"../FontData.js\");\nvar tex_js_1 = require(\"../../common/fonts/tex.js\");\nvar bold_italic_js_1 = require(\"./tex/bold-italic.js\");\nvar bold_js_1 = require(\"./tex/bold.js\");\nvar double_struck_js_1 = require(\"./tex/double-struck.js\");\nvar fraktur_bold_js_1 = require(\"./tex/fraktur-bold.js\");\nvar fraktur_js_1 = require(\"./tex/fraktur.js\");\nvar italic_js_1 = require(\"./tex/italic.js\");\nvar largeop_js_1 = require(\"./tex/largeop.js\");\nvar monospace_js_1 = require(\"./tex/monospace.js\");\nvar normal_js_1 = require(\"./tex/normal.js\");\nvar sans_serif_bold_italic_js_1 = require(\"./tex/sans-serif-bold-italic.js\");\nvar sans_serif_bold_js_1 = require(\"./tex/sans-serif-bold.js\");\nvar sans_serif_italic_js_1 = require(\"./tex/sans-serif-italic.js\");\nvar sans_serif_js_1 = require(\"./tex/sans-serif.js\");\nvar script_bold_js_1 = require(\"./tex/script-bold.js\");\nvar script_js_1 = require(\"./tex/script.js\");\nvar smallop_js_1 = require(\"./tex/smallop.js\");\nvar tex_calligraphic_bold_js_1 = require(\"./tex/tex-calligraphic-bold.js\");\nvar tex_calligraphic_js_1 = require(\"./tex/tex-calligraphic.js\");\nvar tex_mathit_js_1 = require(\"./tex/tex-mathit.js\");\nvar tex_oldstyle_bold_js_1 = require(\"./tex/tex-oldstyle-bold.js\");\nvar tex_oldstyle_js_1 = require(\"./tex/tex-oldstyle.js\");\nvar tex_size3_js_1 = require(\"./tex/tex-size3.js\");\nvar tex_size4_js_1 = require(\"./tex/tex-size4.js\");\nvar tex_variant_js_1 = require(\"./tex/tex-variant.js\");\nvar delimiters_js_1 = require(\"../../common/fonts/tex/delimiters.js\");\nvar TeXFont = (function (_super) {\n    __extends(TeXFont, _super);\n    function TeXFont() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TeXFont.defaultCssFamilyPrefix = 'MJXZERO';\n    TeXFont.defaultVariantClasses = {\n        'normal': 'mjx-n',\n        'bold': 'mjx-b',\n        'italic': 'mjx-i',\n        'bold-italic': 'mjx-b mjx-i',\n        'double-struck': 'mjx-ds mjx-b',\n        'fraktur': 'mjx-fr',\n        'bold-fraktur': 'mjx-fr mjx-b',\n        'script': 'mjx-sc mjx-i',\n        'bold-script': 'mjx-sc mjx-b mjx-i',\n        'sans-serif': 'mjx-ss',\n        'bold-sans-serif': 'mjx-ss mjx-b',\n        'sans-serif-italic': 'mjx-ss mjx-i',\n        'sans-serif-bold-italic': 'mjx-ss mjx-b mjx-i',\n        'monospace': 'mjx-ty',\n        '-smallop': 'mjx-sop',\n        '-largeop': 'mjx-lop',\n        '-size3': 'mjx-s3',\n        '-size4': 'mjx-s4',\n        '-tex-calligraphic': 'mjx-cal mjx-i',\n        '-tex-bold-calligraphic': 'mjx-cal mjx-b',\n        '-tex-mathit': 'mjx-mit mjx-i',\n        '-tex-oldstyle': 'mjx-os',\n        '-tex-bold-oldstyle': 'mjx-os mjx-b',\n        '-tex-variant': 'mjx-var'\n    };\n    TeXFont.defaultVariantLetters = {\n        'normal': '',\n        'bold': 'B',\n        'italic': 'MI',\n        'bold-italic': 'BI',\n        'double-struck': 'A',\n        'fraktur': 'FR',\n        'bold-fraktur': 'FRB',\n        'script': 'SC',\n        'bold-script': 'SCB',\n        'sans-serif': 'SS',\n        'bold-sans-serif': 'SSB',\n        'sans-serif-italic': 'SSI',\n        'sans-serif-bold-italic': 'SSBI',\n        'monospace': 'T',\n        '-smallop': 'S1',\n        '-largeop': 'S2',\n        '-size3': 'S3',\n        '-size4': 'S4',\n        '-tex-calligraphic': 'C',\n        '-tex-bold-calligraphic': 'CB',\n        '-tex-mathit': 'MI',\n        '-tex-oldstyle': 'C',\n        '-tex-bold-oldstyle': 'CB',\n        '-tex-variant': 'A'\n    };\n    TeXFont.defaultDelimiters = delimiters_js_1.delimiters;\n    TeXFont.defaultChars = {\n        'normal': normal_js_1.normal,\n        'bold': bold_js_1.bold,\n        'italic': italic_js_1.italic,\n        'bold-italic': bold_italic_js_1.boldItalic,\n        'double-struck': double_struck_js_1.doubleStruck,\n        'fraktur': fraktur_js_1.fraktur,\n        'bold-fraktur': fraktur_bold_js_1.frakturBold,\n        'script': script_js_1.script,\n        'bold-script': script_bold_js_1.scriptBold,\n        'sans-serif': sans_serif_js_1.sansSerif,\n        'bold-sans-serif': sans_serif_bold_js_1.sansSerifBold,\n        'sans-serif-italic': sans_serif_italic_js_1.sansSerifItalic,\n        'sans-serif-bold-italic': sans_serif_bold_italic_js_1.sansSerifBoldItalic,\n        'monospace': monospace_js_1.monospace,\n        '-smallop': smallop_js_1.smallop,\n        '-largeop': largeop_js_1.largeop,\n        '-size3': tex_size3_js_1.texSize3,\n        '-size4': tex_size4_js_1.texSize4,\n        '-tex-calligraphic': tex_calligraphic_js_1.texCalligraphic,\n        '-tex-bold-calligraphic': tex_calligraphic_bold_js_1.texCalligraphicBold,\n        '-tex-mathit': tex_mathit_js_1.texMathit,\n        '-tex-oldstyle': tex_oldstyle_js_1.texOldstyle,\n        '-tex-bold-oldstyle': tex_oldstyle_bold_js_1.texOldstyleBold,\n        '-tex-variant': tex_variant_js_1.texVariant\n    };\n    TeXFont.defaultStyles = __assign(__assign({}, FontData_js_1.CHTMLFontData.defaultStyles), { '.MJX-TEX': {\n            'font-family': 'MJXZERO, MJXTEX'\n        }, '.TEX-B': {\n            'font-family': 'MJXZERO, MJXTEX-B'\n        }, '.TEX-I': {\n            'font-family': 'MJXZERO, MJXTEX-I'\n        }, '.TEX-MI': {\n            'font-family': 'MJXZERO, MJXTEX-MI'\n        }, '.TEX-BI': {\n            'font-family': 'MJXZERO, MJXTEX-BI'\n        }, '.TEX-S1': {\n            'font-family': 'MJXZERO, MJXTEX-S1'\n        }, '.TEX-S2': {\n            'font-family': 'MJXZERO, MJXTEX-S2'\n        }, '.TEX-S3': {\n            'font-family': 'MJXZERO, MJXTEX-S3'\n        }, '.TEX-S4': {\n            'font-family': 'MJXZERO, MJXTEX-S4'\n        }, '.TEX-A': {\n            'font-family': 'MJXZERO, MJXTEX-A'\n        }, '.TEX-C': {\n            'font-family': 'MJXZERO, MJXTEX-C'\n        }, '.TEX-CB': {\n            'font-family': 'MJXZERO, MJXTEX-CB'\n        }, '.TEX-FR': {\n            'font-family': 'MJXZERO, MJXTEX-FR'\n        }, '.TEX-FRB': {\n            'font-family': 'MJXZERO, MJXTEX-FRB'\n        }, '.TEX-SS': {\n            'font-family': 'MJXZERO, MJXTEX-SS'\n        }, '.TEX-SSB': {\n            'font-family': 'MJXZERO, MJXTEX-SSB'\n        }, '.TEX-SSI': {\n            'font-family': 'MJXZERO, MJXTEX-SSI'\n        }, '.TEX-SC': {\n            'font-family': 'MJXZERO, MJXTEX-SC'\n        }, '.TEX-T': {\n            'font-family': 'MJXZERO, MJXTEX-T'\n        }, '.TEX-V': {\n            'font-family': 'MJXZERO, MJXTEX-V'\n        }, '.TEX-VB': {\n            'font-family': 'MJXZERO, MJXTEX-VB'\n        }, 'mjx-stretchy-v mjx-c, mjx-stretchy-h mjx-c': {\n            'font-family': 'MJXZERO, MJXTEX-S1, MJXTEX-S4, MJXTEX, MJXTEX-A ! important'\n        } });\n    TeXFont.defaultFonts = __assign(__assign({}, FontData_js_1.CHTMLFontData.defaultFonts), { '@font-face /* 1 */': {\n            'font-family': 'MJXTEX',\n            src: 'url(\"%%URL%%/MathJax_Main-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 2 */': {\n            'font-family': 'MJXTEX-B',\n            src: 'url(\"%%URL%%/MathJax_Main-Bold.woff\") format(\"woff\")'\n        }, '@font-face /* 3 */': {\n            'font-family': 'MJXTEX-I',\n            src: 'url(\"%%URL%%/MathJax_Math-Italic.woff\") format(\"woff\")'\n        }, '@font-face /* 4 */': {\n            'font-family': 'MJXTEX-MI',\n            src: 'url(\"%%URL%%/MathJax_Main-Italic.woff\") format(\"woff\")'\n        }, '@font-face /* 5 */': {\n            'font-family': 'MJXTEX-BI',\n            src: 'url(\"%%URL%%/MathJax_Math-BoldItalic.woff\") format(\"woff\")'\n        }, '@font-face /* 6 */': {\n            'font-family': 'MJXTEX-S1',\n            src: 'url(\"%%URL%%/MathJax_Size1-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 7 */': {\n            'font-family': 'MJXTEX-S2',\n            src: 'url(\"%%URL%%/MathJax_Size2-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 8 */': {\n            'font-family': 'MJXTEX-S3',\n            src: 'url(\"%%URL%%/MathJax_Size3-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 9 */': {\n            'font-family': 'MJXTEX-S4',\n            src: 'url(\"%%URL%%/MathJax_Size4-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 10 */': {\n            'font-family': 'MJXTEX-A',\n            src: 'url(\"%%URL%%/MathJax_AMS-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 11 */': {\n            'font-family': 'MJXTEX-C',\n            src: 'url(\"%%URL%%/MathJax_Calligraphic-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 12 */': {\n            'font-family': 'MJXTEX-CB',\n            src: 'url(\"%%URL%%/MathJax_Calligraphic-Bold.woff\") format(\"woff\")'\n        }, '@font-face /* 13 */': {\n            'font-family': 'MJXTEX-FR',\n            src: 'url(\"%%URL%%/MathJax_Fraktur-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 14 */': {\n            'font-family': 'MJXTEX-FRB',\n            src: 'url(\"%%URL%%/MathJax_Fraktur-Bold.woff\") format(\"woff\")'\n        }, '@font-face /* 15 */': {\n            'font-family': 'MJXTEX-SS',\n            src: 'url(\"%%URL%%/MathJax_SansSerif-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 16 */': {\n            'font-family': 'MJXTEX-SSB',\n            src: 'url(\"%%URL%%/MathJax_SansSerif-Bold.woff\") format(\"woff\")'\n        }, '@font-face /* 17 */': {\n            'font-family': 'MJXTEX-SSI',\n            src: 'url(\"%%URL%%/MathJax_SansSerif-Italic.woff\") format(\"woff\")'\n        }, '@font-face /* 18 */': {\n            'font-family': 'MJXTEX-SC',\n            src: 'url(\"%%URL%%/MathJax_Script-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 19 */': {\n            'font-family': 'MJXTEX-T',\n            src: 'url(\"%%URL%%/MathJax_Typewriter-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 20 */': {\n            'font-family': 'MJXTEX-V',\n            src: 'url(\"%%URL%%/MathJax_Vector-Regular.woff\") format(\"woff\")'\n        }, '@font-face /* 21 */': {\n            'font-family': 'MJXTEX-VB',\n            src: 'url(\"%%URL%%/MathJax_Vector-Bold.woff\") format(\"woff\")'\n        } });\n    return TeXFont;\n}((0, tex_js_1.CommonTeXFontMixin)(FontData_js_1.CHTMLFontData)));\nexports.TeXFont = TeXFont;\n//# sourceMappingURL=tex.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.boldItalic = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar bold_italic_js_1 = require(\"../../../common/fonts/tex/bold-italic.js\");\nexports.boldItalic = (0, FontData_js_1.AddCSS)(bold_italic_js_1.boldItalic, {\n    0x131: { f: 'B' },\n    0x237: { f: 'B' },\n    0x2044: { c: '/' },\n    0x2206: { c: '\\\\394' },\n    0x29F8: { c: '/' },\n});\n//# sourceMappingURL=bold-italic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bold = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar bold_js_1 = require(\"../../../common/fonts/tex/bold.js\");\nexports.bold = (0, FontData_js_1.AddCSS)(bold_js_1.bold, {\n    0xB7: { c: '\\\\22C5' },\n    0x131: { f: '' },\n    0x237: { f: '' },\n    0x2B9: { c: '\\\\2032' },\n    0x2002: { c: '' },\n    0x2003: { c: '' },\n    0x2004: { c: '' },\n    0x2005: { c: '' },\n    0x2006: { c: '' },\n    0x2009: { c: '' },\n    0x200A: { c: '' },\n    0x2015: { c: '\\\\2014' },\n    0x2016: { c: '\\\\2225' },\n    0x2017: { c: '_' },\n    0x2022: { c: '\\\\2219' },\n    0x2033: { c: '\\\\2032\\\\2032' },\n    0x2034: { c: '\\\\2032\\\\2032\\\\2032' },\n    0x203E: { c: '\\\\2C9' },\n    0x2044: { c: '/' },\n    0x2057: { c: '\\\\2032\\\\2032\\\\2032\\\\2032' },\n    0x20D7: { c: '\\\\2192', f: 'VB' },\n    0x219A: { c: '\\\\2190\\\\338' },\n    0x219B: { c: '\\\\2192\\\\338' },\n    0x21AE: { c: '\\\\2194\\\\338' },\n    0x21CD: { c: '\\\\21D0\\\\338' },\n    0x21CE: { c: '\\\\21D4\\\\338' },\n    0x21CF: { c: '\\\\21D2\\\\338' },\n    0x2204: { c: '\\\\2203\\\\338' },\n    0x2206: { c: '\\\\394' },\n    0x220C: { c: '\\\\220B\\\\338' },\n    0x2224: { c: '\\\\2223\\\\338' },\n    0x2226: { c: '\\\\2225\\\\338' },\n    0x2241: { c: '\\\\223C\\\\338' },\n    0x2244: { c: '\\\\2243\\\\338' },\n    0x2247: { c: '\\\\2245\\\\338' },\n    0x2249: { c: '\\\\2248\\\\338' },\n    0x2262: { c: '\\\\2261\\\\338' },\n    0x226D: { c: '\\\\224D\\\\338' },\n    0x226E: { c: '<\\\\338' },\n    0x226F: { c: '>\\\\338' },\n    0x2270: { c: '\\\\2264\\\\338' },\n    0x2271: { c: '\\\\2265\\\\338' },\n    0x2280: { c: '\\\\227A\\\\338' },\n    0x2281: { c: '\\\\227B\\\\338' },\n    0x2284: { c: '\\\\2282\\\\338' },\n    0x2285: { c: '\\\\2283\\\\338' },\n    0x2288: { c: '\\\\2286\\\\338' },\n    0x2289: { c: '\\\\2287\\\\338' },\n    0x22AC: { c: '\\\\22A2\\\\338' },\n    0x22AD: { c: '\\\\22A8\\\\338' },\n    0x22E2: { c: '\\\\2291\\\\338' },\n    0x22E3: { c: '\\\\2292\\\\338' },\n    0x2329: { c: '\\\\27E8' },\n    0x232A: { c: '\\\\27E9' },\n    0x25B5: { c: '\\\\25B3' },\n    0x25BF: { c: '\\\\25BD' },\n    0x2758: { c: '\\\\2223' },\n    0x29F8: { c: '/', f: 'BI' },\n    0x2A2F: { c: '\\\\D7' },\n    0x3008: { c: '\\\\27E8' },\n    0x3009: { c: '\\\\27E9' },\n});\n//# sourceMappingURL=bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.doubleStruck = void 0;\nvar double_struck_js_1 = require(\"../../../common/fonts/tex/double-struck.js\");\nObject.defineProperty(exports, \"doubleStruck\", { enumerable: true, get: function () { return double_struck_js_1.doubleStruck; } });\n//# sourceMappingURL=double-struck.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.frakturBold = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar fraktur_bold_js_1 = require(\"../../../common/fonts/tex/fraktur-bold.js\");\nexports.frakturBold = (0, FontData_js_1.AddCSS)(fraktur_bold_js_1.frakturBold, {\n    0x2044: { c: '/' },\n});\n//# sourceMappingURL=fraktur-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.fraktur = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar fraktur_js_1 = require(\"../../../common/fonts/tex/fraktur.js\");\nexports.fraktur = (0, FontData_js_1.AddCSS)(fraktur_js_1.fraktur, {\n    0x2044: { c: '/' },\n});\n//# sourceMappingURL=fraktur.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.italic = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar italic_js_1 = require(\"../../../common/fonts/tex/italic.js\");\nexports.italic = (0, FontData_js_1.AddCSS)(italic_js_1.italic, {\n    0x2F: { f: 'I' },\n    0x3DD: { c: '\\\\E008', f: 'A' },\n    0x2015: { c: '\\\\2014' },\n    0x2017: { c: '_' },\n    0x2044: { c: '/', f: 'I' },\n    0x2206: { c: '\\\\394', f: 'I' },\n    0x29F8: { c: '/', f: 'I' },\n});\n//# sourceMappingURL=italic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.largeop = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar largeop_js_1 = require(\"../../../common/fonts/tex/largeop.js\");\nexports.largeop = (0, FontData_js_1.AddCSS)(largeop_js_1.largeop, {\n    0x2016: { f: 'S1' },\n    0x2044: { c: '/' },\n    0x2191: { f: 'S1' },\n    0x2193: { f: 'S1' },\n    0x21D1: { f: 'S1' },\n    0x21D3: { f: 'S1' },\n    0x2223: { f: 'S1' },\n    0x2225: { f: 'S1' },\n    0x2329: { c: '\\\\27E8' },\n    0x232A: { c: '\\\\27E9' },\n    0x23D0: { f: 'S1' },\n    0x2758: { c: '\\\\2223', f: 'S1' },\n    0x2A0C: { c: '\\\\222C\\\\222C' },\n    0x3008: { c: '\\\\27E8' },\n    0x3009: { c: '\\\\27E9' },\n});\n//# sourceMappingURL=largeop.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.monospace = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar monospace_js_1 = require(\"../../../common/fonts/tex/monospace.js\");\nexports.monospace = (0, FontData_js_1.AddCSS)(monospace_js_1.monospace, {\n    0x2B9: { c: '\\\\2032' },\n    0x391: { c: 'A' },\n    0x392: { c: 'B' },\n    0x395: { c: 'E' },\n    0x396: { c: 'Z' },\n    0x397: { c: 'H' },\n    0x399: { c: 'I' },\n    0x39A: { c: 'K' },\n    0x39C: { c: 'M' },\n    0x39D: { c: 'N' },\n    0x39F: { c: 'O' },\n    0x3A1: { c: 'P' },\n    0x3A4: { c: 'T' },\n    0x3A7: { c: 'X' },\n    0x2017: { c: '_' },\n    0x2033: { c: '\\\\2032\\\\2032' },\n    0x2034: { c: '\\\\2032\\\\2032\\\\2032' },\n    0x2044: { c: '/' },\n    0x2057: { c: '\\\\2032\\\\2032\\\\2032\\\\2032' },\n    0x2206: { c: '\\\\394' },\n});\n//# sourceMappingURL=monospace.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.normal = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar normal_js_1 = require(\"../../../common/fonts/tex/normal.js\");\nexports.normal = (0, FontData_js_1.AddCSS)(normal_js_1.normal, {\n    0xA3: { f: 'MI' },\n    0xA5: { f: 'A' },\n    0xAE: { f: 'A' },\n    0xB7: { c: '\\\\22C5' },\n    0xF0: { f: 'A' },\n    0x2B9: { c: '\\\\2032' },\n    0x391: { c: 'A' },\n    0x392: { c: 'B' },\n    0x395: { c: 'E' },\n    0x396: { c: 'Z' },\n    0x397: { c: 'H' },\n    0x399: { c: 'I' },\n    0x39A: { c: 'K' },\n    0x39C: { c: 'M' },\n    0x39D: { c: 'N' },\n    0x39F: { c: 'O' },\n    0x3A1: { c: 'P' },\n    0x3A4: { c: 'T' },\n    0x3A7: { c: 'X' },\n    0x2000: { c: '' },\n    0x2001: { c: '' },\n    0x2002: { c: '' },\n    0x2003: { c: '' },\n    0x2004: { c: '' },\n    0x2005: { c: '' },\n    0x2006: { c: '' },\n    0x2009: { c: '' },\n    0x200A: { c: '' },\n    0x200B: { c: '' },\n    0x200C: { c: '' },\n    0x2015: { c: '\\\\2014' },\n    0x2016: { c: '\\\\2225' },\n    0x2017: { c: '_' },\n    0x2022: { c: '\\\\2219' },\n    0x2033: { c: '\\\\2032\\\\2032' },\n    0x2034: { c: '\\\\2032\\\\2032\\\\2032' },\n    0x2035: { f: 'A' },\n    0x2036: { c: '\\\\2035\\\\2035', f: 'A' },\n    0x2037: { c: '\\\\2035\\\\2035\\\\2035', f: 'A' },\n    0x203E: { c: '\\\\2C9' },\n    0x2044: { c: '/' },\n    0x2057: { c: '\\\\2032\\\\2032\\\\2032\\\\2032' },\n    0x2060: { c: '' },\n    0x2061: { c: '' },\n    0x2062: { c: '' },\n    0x2063: { c: '' },\n    0x2064: { c: '' },\n    0x20D7: { c: '\\\\2192', f: 'V' },\n    0x2102: { c: 'C', f: 'A' },\n    0x210B: { c: 'H', f: 'SC' },\n    0x210C: { c: 'H', f: 'FR' },\n    0x210D: { c: 'H', f: 'A' },\n    0x210E: { c: 'h', f: 'I' },\n    0x210F: { f: 'A' },\n    0x2110: { c: 'I', f: 'SC' },\n    0x2111: { c: 'I', f: 'FR' },\n    0x2112: { c: 'L', f: 'SC' },\n    0x2115: { c: 'N', f: 'A' },\n    0x2119: { c: 'P', f: 'A' },\n    0x211A: { c: 'Q', f: 'A' },\n    0x211B: { c: 'R', f: 'SC' },\n    0x211C: { c: 'R', f: 'FR' },\n    0x211D: { c: 'R', f: 'A' },\n    0x2124: { c: 'Z', f: 'A' },\n    0x2126: { c: '\\\\3A9' },\n    0x2127: { f: 'A' },\n    0x2128: { c: 'Z', f: 'FR' },\n    0x212C: { c: 'B', f: 'SC' },\n    0x212D: { c: 'C', f: 'FR' },\n    0x2130: { c: 'E', f: 'SC' },\n    0x2131: { c: 'F', f: 'SC' },\n    0x2132: { f: 'A' },\n    0x2133: { c: 'M', f: 'SC' },\n    0x2136: { f: 'A' },\n    0x2137: { f: 'A' },\n    0x2138: { f: 'A' },\n    0x2141: { f: 'A' },\n    0x219A: { f: 'A' },\n    0x219B: { f: 'A' },\n    0x219E: { f: 'A' },\n    0x21A0: { f: 'A' },\n    0x21A2: { f: 'A' },\n    0x21A3: { f: 'A' },\n    0x21AB: { f: 'A' },\n    0x21AC: { f: 'A' },\n    0x21AD: { f: 'A' },\n    0x21AE: { f: 'A' },\n    0x21B0: { f: 'A' },\n    0x21B1: { f: 'A' },\n    0x21B6: { f: 'A' },\n    0x21B7: { f: 'A' },\n    0x21BA: { f: 'A' },\n    0x21BB: { f: 'A' },\n    0x21BE: { f: 'A' },\n    0x21BF: { f: 'A' },\n    0x21C2: { f: 'A' },\n    0x21C3: { f: 'A' },\n    0x21C4: { f: 'A' },\n    0x21C6: { f: 'A' },\n    0x21C7: { f: 'A' },\n    0x21C8: { f: 'A' },\n    0x21C9: { f: 'A' },\n    0x21CA: { f: 'A' },\n    0x21CB: { f: 'A' },\n    0x21CD: { f: 'A' },\n    0x21CE: { f: 'A' },\n    0x21CF: { f: 'A' },\n    0x21DA: { f: 'A' },\n    0x21DB: { f: 'A' },\n    0x21DD: { f: 'A' },\n    0x21E0: { f: 'A' },\n    0x21E2: { f: 'A' },\n    0x2201: { f: 'A' },\n    0x2204: { c: '\\\\2203\\\\338' },\n    0x2206: { c: '\\\\394' },\n    0x220C: { c: '\\\\220B\\\\338' },\n    0x220D: { f: 'A' },\n    0x220F: { f: 'S1' },\n    0x2210: { f: 'S1' },\n    0x2211: { f: 'S1' },\n    0x2214: { f: 'A' },\n    0x2221: { f: 'A' },\n    0x2222: { f: 'A' },\n    0x2224: { f: 'A' },\n    0x2226: { f: 'A' },\n    0x222C: { f: 'S1' },\n    0x222D: { f: 'S1' },\n    0x222E: { f: 'S1' },\n    0x2234: { f: 'A' },\n    0x2235: { f: 'A' },\n    0x223D: { f: 'A' },\n    0x2241: { f: 'A' },\n    0x2242: { f: 'A' },\n    0x2244: { c: '\\\\2243\\\\338' },\n    0x2247: { c: '\\\\2246', f: 'A' },\n    0x2249: { c: '\\\\2248\\\\338' },\n    0x224A: { f: 'A' },\n    0x224E: { f: 'A' },\n    0x224F: { f: 'A' },\n    0x2251: { f: 'A' },\n    0x2252: { f: 'A' },\n    0x2253: { f: 'A' },\n    0x2256: { f: 'A' },\n    0x2257: { f: 'A' },\n    0x225C: { f: 'A' },\n    0x2262: { c: '\\\\2261\\\\338' },\n    0x2266: { f: 'A' },\n    0x2267: { f: 'A' },\n    0x2268: { f: 'A' },\n    0x2269: { f: 'A' },\n    0x226C: { f: 'A' },\n    0x226D: { c: '\\\\224D\\\\338' },\n    0x226E: { f: 'A' },\n    0x226F: { f: 'A' },\n    0x2270: { f: 'A' },\n    0x2271: { f: 'A' },\n    0x2272: { f: 'A' },\n    0x2273: { f: 'A' },\n    0x2274: { c: '\\\\2272\\\\338' },\n    0x2275: { c: '\\\\2273\\\\338' },\n    0x2276: { f: 'A' },\n    0x2277: { f: 'A' },\n    0x2278: { c: '\\\\2276\\\\338' },\n    0x2279: { c: '\\\\2277\\\\338' },\n    0x227C: { f: 'A' },\n    0x227D: { f: 'A' },\n    0x227E: { f: 'A' },\n    0x227F: { f: 'A' },\n    0x2280: { f: 'A' },\n    0x2281: { f: 'A' },\n    0x2284: { c: '\\\\2282\\\\338' },\n    0x2285: { c: '\\\\2283\\\\338' },\n    0x2288: { f: 'A' },\n    0x2289: { f: 'A' },\n    0x228A: { f: 'A' },\n    0x228B: { f: 'A' },\n    0x228F: { f: 'A' },\n    0x2290: { f: 'A' },\n    0x229A: { f: 'A' },\n    0x229B: { f: 'A' },\n    0x229D: { f: 'A' },\n    0x229E: { f: 'A' },\n    0x229F: { f: 'A' },\n    0x22A0: { f: 'A' },\n    0x22A1: { f: 'A' },\n    0x22A9: { f: 'A' },\n    0x22AA: { f: 'A' },\n    0x22AC: { f: 'A' },\n    0x22AD: { f: 'A' },\n    0x22AE: { f: 'A' },\n    0x22AF: { f: 'A' },\n    0x22B2: { f: 'A' },\n    0x22B3: { f: 'A' },\n    0x22B4: { f: 'A' },\n    0x22B5: { f: 'A' },\n    0x22B8: { f: 'A' },\n    0x22BA: { f: 'A' },\n    0x22BB: { f: 'A' },\n    0x22BC: { f: 'A' },\n    0x22C0: { f: 'S1' },\n    0x22C1: { f: 'S1' },\n    0x22C2: { f: 'S1' },\n    0x22C3: { f: 'S1' },\n    0x22C7: { f: 'A' },\n    0x22C9: { f: 'A' },\n    0x22CA: { f: 'A' },\n    0x22CB: { f: 'A' },\n    0x22CC: { f: 'A' },\n    0x22CD: { f: 'A' },\n    0x22CE: { f: 'A' },\n    0x22CF: { f: 'A' },\n    0x22D0: { f: 'A' },\n    0x22D1: { f: 'A' },\n    0x22D2: { f: 'A' },\n    0x22D3: { f: 'A' },\n    0x22D4: { f: 'A' },\n    0x22D6: { f: 'A' },\n    0x22D7: { f: 'A' },\n    0x22D8: { f: 'A' },\n    0x22D9: { f: 'A' },\n    0x22DA: { f: 'A' },\n    0x22DB: { f: 'A' },\n    0x22DE: { f: 'A' },\n    0x22DF: { f: 'A' },\n    0x22E0: { f: 'A' },\n    0x22E1: { f: 'A' },\n    0x22E2: { c: '\\\\2291\\\\338' },\n    0x22E3: { c: '\\\\2292\\\\338' },\n    0x22E6: { f: 'A' },\n    0x22E7: { f: 'A' },\n    0x22E8: { f: 'A' },\n    0x22E9: { f: 'A' },\n    0x22EA: { f: 'A' },\n    0x22EB: { f: 'A' },\n    0x22EC: { f: 'A' },\n    0x22ED: { f: 'A' },\n    0x2305: { c: '\\\\22BC', f: 'A' },\n    0x2306: { c: '\\\\2A5E', f: 'A' },\n    0x231C: { c: '\\\\250C', f: 'A' },\n    0x231D: { c: '\\\\2510', f: 'A' },\n    0x231E: { c: '\\\\2514', f: 'A' },\n    0x231F: { c: '\\\\2518', f: 'A' },\n    0x2329: { c: '\\\\27E8' },\n    0x232A: { c: '\\\\27E9' },\n    0x23D0: { f: 'S1' },\n    0x24C8: { f: 'A' },\n    0x250C: { f: 'A' },\n    0x2510: { f: 'A' },\n    0x2514: { f: 'A' },\n    0x2518: { f: 'A' },\n    0x2571: { f: 'A' },\n    0x2572: { f: 'A' },\n    0x25A0: { f: 'A' },\n    0x25A1: { f: 'A' },\n    0x25AA: { c: '\\\\25A0', f: 'A' },\n    0x25B2: { f: 'A' },\n    0x25B4: { c: '\\\\25B2', f: 'A' },\n    0x25B5: { c: '\\\\25B3' },\n    0x25B6: { f: 'A' },\n    0x25B8: { c: '\\\\25B6', f: 'A' },\n    0x25BC: { f: 'A' },\n    0x25BE: { c: '\\\\25BC', f: 'A' },\n    0x25BF: { c: '\\\\25BD' },\n    0x25C0: { f: 'A' },\n    0x25C2: { c: '\\\\25C0', f: 'A' },\n    0x25CA: { f: 'A' },\n    0x25FB: { c: '\\\\25A1', f: 'A' },\n    0x25FC: { c: '\\\\25A0', f: 'A' },\n    0x2605: { f: 'A' },\n    0x2713: { f: 'A' },\n    0x2720: { f: 'A' },\n    0x2758: { c: '\\\\2223' },\n    0x29EB: { f: 'A' },\n    0x29F8: { c: '/', f: 'I' },\n    0x2A00: { f: 'S1' },\n    0x2A01: { f: 'S1' },\n    0x2A02: { f: 'S1' },\n    0x2A04: { f: 'S1' },\n    0x2A06: { f: 'S1' },\n    0x2A0C: { c: '\\\\222C\\\\222C', f: 'S1' },\n    0x2A2F: { c: '\\\\D7' },\n    0x2A5E: { f: 'A' },\n    0x2A7D: { f: 'A' },\n    0x2A7E: { f: 'A' },\n    0x2A85: { f: 'A' },\n    0x2A86: { f: 'A' },\n    0x2A87: { f: 'A' },\n    0x2A88: { f: 'A' },\n    0x2A89: { f: 'A' },\n    0x2A8A: { f: 'A' },\n    0x2A8B: { f: 'A' },\n    0x2A8C: { f: 'A' },\n    0x2A95: { f: 'A' },\n    0x2A96: { f: 'A' },\n    0x2AB5: { f: 'A' },\n    0x2AB6: { f: 'A' },\n    0x2AB7: { f: 'A' },\n    0x2AB8: { f: 'A' },\n    0x2AB9: { f: 'A' },\n    0x2ABA: { f: 'A' },\n    0x2AC5: { f: 'A' },\n    0x2AC6: { f: 'A' },\n    0x2ACB: { f: 'A' },\n    0x2ACC: { f: 'A' },\n    0x3008: { c: '\\\\27E8' },\n    0x3009: { c: '\\\\27E9' },\n    0xE006: { f: 'A' },\n    0xE007: { f: 'A' },\n    0xE008: { f: 'A' },\n    0xE009: { f: 'A' },\n    0xE00C: { f: 'A' },\n    0xE00D: { f: 'A' },\n    0xE00E: { f: 'A' },\n    0xE00F: { f: 'A' },\n    0xE010: { f: 'A' },\n    0xE011: { f: 'A' },\n    0xE016: { f: 'A' },\n    0xE017: { f: 'A' },\n    0xE018: { f: 'A' },\n    0xE019: { f: 'A' },\n    0xE01A: { f: 'A' },\n    0xE01B: { f: 'A' },\n    0x1D400: { c: 'A', f: 'B' },\n    0x1D401: { c: 'B', f: 'B' },\n    0x1D402: { c: 'C', f: 'B' },\n    0x1D403: { c: 'D', f: 'B' },\n    0x1D404: { c: 'E', f: 'B' },\n    0x1D405: { c: 'F', f: 'B' },\n    0x1D406: { c: 'G', f: 'B' },\n    0x1D407: { c: 'H', f: 'B' },\n    0x1D408: { c: 'I', f: 'B' },\n    0x1D409: { c: 'J', f: 'B' },\n    0x1D40A: { c: 'K', f: 'B' },\n    0x1D40B: { c: 'L', f: 'B' },\n    0x1D40C: { c: 'M', f: 'B' },\n    0x1D40D: { c: 'N', f: 'B' },\n    0x1D40E: { c: 'O', f: 'B' },\n    0x1D40F: { c: 'P', f: 'B' },\n    0x1D410: { c: 'Q', f: 'B' },\n    0x1D411: { c: 'R', f: 'B' },\n    0x1D412: { c: 'S', f: 'B' },\n    0x1D413: { c: 'T', f: 'B' },\n    0x1D414: { c: 'U', f: 'B' },\n    0x1D415: { c: 'V', f: 'B' },\n    0x1D416: { c: 'W', f: 'B' },\n    0x1D417: { c: 'X', f: 'B' },\n    0x1D418: { c: 'Y', f: 'B' },\n    0x1D419: { c: 'Z', f: 'B' },\n    0x1D41A: { c: 'a', f: 'B' },\n    0x1D41B: { c: 'b', f: 'B' },\n    0x1D41C: { c: 'c', f: 'B' },\n    0x1D41D: { c: 'd', f: 'B' },\n    0x1D41E: { c: 'e', f: 'B' },\n    0x1D41F: { c: 'f', f: 'B' },\n    0x1D420: { c: 'g', f: 'B' },\n    0x1D421: { c: 'h', f: 'B' },\n    0x1D422: { c: 'i', f: 'B' },\n    0x1D423: { c: 'j', f: 'B' },\n    0x1D424: { c: 'k', f: 'B' },\n    0x1D425: { c: 'l', f: 'B' },\n    0x1D426: { c: 'm', f: 'B' },\n    0x1D427: { c: 'n', f: 'B' },\n    0x1D428: { c: 'o', f: 'B' },\n    0x1D429: { c: 'p', f: 'B' },\n    0x1D42A: { c: 'q', f: 'B' },\n    0x1D42B: { c: 'r', f: 'B' },\n    0x1D42C: { c: 's', f: 'B' },\n    0x1D42D: { c: 't', f: 'B' },\n    0x1D42E: { c: 'u', f: 'B' },\n    0x1D42F: { c: 'v', f: 'B' },\n    0x1D430: { c: 'w', f: 'B' },\n    0x1D431: { c: 'x', f: 'B' },\n    0x1D432: { c: 'y', f: 'B' },\n    0x1D433: { c: 'z', f: 'B' },\n    0x1D434: { c: 'A', f: 'I' },\n    0x1D435: { c: 'B', f: 'I' },\n    0x1D436: { c: 'C', f: 'I' },\n    0x1D437: { c: 'D', f: 'I' },\n    0x1D438: { c: 'E', f: 'I' },\n    0x1D439: { c: 'F', f: 'I' },\n    0x1D43A: { c: 'G', f: 'I' },\n    0x1D43B: { c: 'H', f: 'I' },\n    0x1D43C: { c: 'I', f: 'I' },\n    0x1D43D: { c: 'J', f: 'I' },\n    0x1D43E: { c: 'K', f: 'I' },\n    0x1D43F: { c: 'L', f: 'I' },\n    0x1D440: { c: 'M', f: 'I' },\n    0x1D441: { c: 'N', f: 'I' },\n    0x1D442: { c: 'O', f: 'I' },\n    0x1D443: { c: 'P', f: 'I' },\n    0x1D444: { c: 'Q', f: 'I' },\n    0x1D445: { c: 'R', f: 'I' },\n    0x1D446: { c: 'S', f: 'I' },\n    0x1D447: { c: 'T', f: 'I' },\n    0x1D448: { c: 'U', f: 'I' },\n    0x1D449: { c: 'V', f: 'I' },\n    0x1D44A: { c: 'W', f: 'I' },\n    0x1D44B: { c: 'X', f: 'I' },\n    0x1D44C: { c: 'Y', f: 'I' },\n    0x1D44D: { c: 'Z', f: 'I' },\n    0x1D44E: { c: 'a', f: 'I' },\n    0x1D44F: { c: 'b', f: 'I' },\n    0x1D450: { c: 'c', f: 'I' },\n    0x1D451: { c: 'd', f: 'I' },\n    0x1D452: { c: 'e', f: 'I' },\n    0x1D453: { c: 'f', f: 'I' },\n    0x1D454: { c: 'g', f: 'I' },\n    0x1D456: { c: 'i', f: 'I' },\n    0x1D457: { c: 'j', f: 'I' },\n    0x1D458: { c: 'k', f: 'I' },\n    0x1D459: { c: 'l', f: 'I' },\n    0x1D45A: { c: 'm', f: 'I' },\n    0x1D45B: { c: 'n', f: 'I' },\n    0x1D45C: { c: 'o', f: 'I' },\n    0x1D45D: { c: 'p', f: 'I' },\n    0x1D45E: { c: 'q', f: 'I' },\n    0x1D45F: { c: 'r', f: 'I' },\n    0x1D460: { c: 's', f: 'I' },\n    0x1D461: { c: 't', f: 'I' },\n    0x1D462: { c: 'u', f: 'I' },\n    0x1D463: { c: 'v', f: 'I' },\n    0x1D464: { c: 'w', f: 'I' },\n    0x1D465: { c: 'x', f: 'I' },\n    0x1D466: { c: 'y', f: 'I' },\n    0x1D467: { c: 'z', f: 'I' },\n    0x1D468: { c: 'A', f: 'BI' },\n    0x1D469: { c: 'B', f: 'BI' },\n    0x1D46A: { c: 'C', f: 'BI' },\n    0x1D46B: { c: 'D', f: 'BI' },\n    0x1D46C: { c: 'E', f: 'BI' },\n    0x1D46D: { c: 'F', f: 'BI' },\n    0x1D46E: { c: 'G', f: 'BI' },\n    0x1D46F: { c: 'H', f: 'BI' },\n    0x1D470: { c: 'I', f: 'BI' },\n    0x1D471: { c: 'J', f: 'BI' },\n    0x1D472: { c: 'K', f: 'BI' },\n    0x1D473: { c: 'L', f: 'BI' },\n    0x1D474: { c: 'M', f: 'BI' },\n    0x1D475: { c: 'N', f: 'BI' },\n    0x1D476: { c: 'O', f: 'BI' },\n    0x1D477: { c: 'P', f: 'BI' },\n    0x1D478: { c: 'Q', f: 'BI' },\n    0x1D479: { c: 'R', f: 'BI' },\n    0x1D47A: { c: 'S', f: 'BI' },\n    0x1D47B: { c: 'T', f: 'BI' },\n    0x1D47C: { c: 'U', f: 'BI' },\n    0x1D47D: { c: 'V', f: 'BI' },\n    0x1D47E: { c: 'W', f: 'BI' },\n    0x1D47F: { c: 'X', f: 'BI' },\n    0x1D480: { c: 'Y', f: 'BI' },\n    0x1D481: { c: 'Z', f: 'BI' },\n    0x1D482: { c: 'a', f: 'BI' },\n    0x1D483: { c: 'b', f: 'BI' },\n    0x1D484: { c: 'c', f: 'BI' },\n    0x1D485: { c: 'd', f: 'BI' },\n    0x1D486: { c: 'e', f: 'BI' },\n    0x1D487: { c: 'f', f: 'BI' },\n    0x1D488: { c: 'g', f: 'BI' },\n    0x1D489: { c: 'h', f: 'BI' },\n    0x1D48A: { c: 'i', f: 'BI' },\n    0x1D48B: { c: 'j', f: 'BI' },\n    0x1D48C: { c: 'k', f: 'BI' },\n    0x1D48D: { c: 'l', f: 'BI' },\n    0x1D48E: { c: 'm', f: 'BI' },\n    0x1D48F: { c: 'n', f: 'BI' },\n    0x1D490: { c: 'o', f: 'BI' },\n    0x1D491: { c: 'p', f: 'BI' },\n    0x1D492: { c: 'q', f: 'BI' },\n    0x1D493: { c: 'r', f: 'BI' },\n    0x1D494: { c: 's', f: 'BI' },\n    0x1D495: { c: 't', f: 'BI' },\n    0x1D496: { c: 'u', f: 'BI' },\n    0x1D497: { c: 'v', f: 'BI' },\n    0x1D498: { c: 'w', f: 'BI' },\n    0x1D499: { c: 'x', f: 'BI' },\n    0x1D49A: { c: 'y', f: 'BI' },\n    0x1D49B: { c: 'z', f: 'BI' },\n    0x1D49C: { c: 'A', f: 'SC' },\n    0x1D49E: { c: 'C', f: 'SC' },\n    0x1D49F: { c: 'D', f: 'SC' },\n    0x1D4A2: { c: 'G', f: 'SC' },\n    0x1D4A5: { c: 'J', f: 'SC' },\n    0x1D4A6: { c: 'K', f: 'SC' },\n    0x1D4A9: { c: 'N', f: 'SC' },\n    0x1D4AA: { c: 'O', f: 'SC' },\n    0x1D4AB: { c: 'P', f: 'SC' },\n    0x1D4AC: { c: 'Q', f: 'SC' },\n    0x1D4AE: { c: 'S', f: 'SC' },\n    0x1D4AF: { c: 'T', f: 'SC' },\n    0x1D4B0: { c: 'U', f: 'SC' },\n    0x1D4B1: { c: 'V', f: 'SC' },\n    0x1D4B2: { c: 'W', f: 'SC' },\n    0x1D4B3: { c: 'X', f: 'SC' },\n    0x1D4B4: { c: 'Y', f: 'SC' },\n    0x1D4B5: { c: 'Z', f: 'SC' },\n    0x1D504: { c: 'A', f: 'FR' },\n    0x1D505: { c: 'B', f: 'FR' },\n    0x1D507: { c: 'D', f: 'FR' },\n    0x1D508: { c: 'E', f: 'FR' },\n    0x1D509: { c: 'F', f: 'FR' },\n    0x1D50A: { c: 'G', f: 'FR' },\n    0x1D50D: { c: 'J', f: 'FR' },\n    0x1D50E: { c: 'K', f: 'FR' },\n    0x1D50F: { c: 'L', f: 'FR' },\n    0x1D510: { c: 'M', f: 'FR' },\n    0x1D511: { c: 'N', f: 'FR' },\n    0x1D512: { c: 'O', f: 'FR' },\n    0x1D513: { c: 'P', f: 'FR' },\n    0x1D514: { c: 'Q', f: 'FR' },\n    0x1D516: { c: 'S', f: 'FR' },\n    0x1D517: { c: 'T', f: 'FR' },\n    0x1D518: { c: 'U', f: 'FR' },\n    0x1D519: { c: 'V', f: 'FR' },\n    0x1D51A: { c: 'W', f: 'FR' },\n    0x1D51B: { c: 'X', f: 'FR' },\n    0x1D51C: { c: 'Y', f: 'FR' },\n    0x1D51E: { c: 'a', f: 'FR' },\n    0x1D51F: { c: 'b', f: 'FR' },\n    0x1D520: { c: 'c', f: 'FR' },\n    0x1D521: { c: 'd', f: 'FR' },\n    0x1D522: { c: 'e', f: 'FR' },\n    0x1D523: { c: 'f', f: 'FR' },\n    0x1D524: { c: 'g', f: 'FR' },\n    0x1D525: { c: 'h', f: 'FR' },\n    0x1D526: { c: 'i', f: 'FR' },\n    0x1D527: { c: 'j', f: 'FR' },\n    0x1D528: { c: 'k', f: 'FR' },\n    0x1D529: { c: 'l', f: 'FR' },\n    0x1D52A: { c: 'm', f: 'FR' },\n    0x1D52B: { c: 'n', f: 'FR' },\n    0x1D52C: { c: 'o', f: 'FR' },\n    0x1D52D: { c: 'p', f: 'FR' },\n    0x1D52E: { c: 'q', f: 'FR' },\n    0x1D52F: { c: 'r', f: 'FR' },\n    0x1D530: { c: 's', f: 'FR' },\n    0x1D531: { c: 't', f: 'FR' },\n    0x1D532: { c: 'u', f: 'FR' },\n    0x1D533: { c: 'v', f: 'FR' },\n    0x1D534: { c: 'w', f: 'FR' },\n    0x1D535: { c: 'x', f: 'FR' },\n    0x1D536: { c: 'y', f: 'FR' },\n    0x1D537: { c: 'z', f: 'FR' },\n    0x1D538: { c: 'A', f: 'A' },\n    0x1D539: { c: 'B', f: 'A' },\n    0x1D53B: { c: 'D', f: 'A' },\n    0x1D53C: { c: 'E', f: 'A' },\n    0x1D53D: { c: 'F', f: 'A' },\n    0x1D53E: { c: 'G', f: 'A' },\n    0x1D540: { c: 'I', f: 'A' },\n    0x1D541: { c: 'J', f: 'A' },\n    0x1D542: { c: 'K', f: 'A' },\n    0x1D543: { c: 'L', f: 'A' },\n    0x1D544: { c: 'M', f: 'A' },\n    0x1D546: { c: 'O', f: 'A' },\n    0x1D54A: { c: 'S', f: 'A' },\n    0x1D54B: { c: 'T', f: 'A' },\n    0x1D54C: { c: 'U', f: 'A' },\n    0x1D54D: { c: 'V', f: 'A' },\n    0x1D54E: { c: 'W', f: 'A' },\n    0x1D54F: { c: 'X', f: 'A' },\n    0x1D550: { c: 'Y', f: 'A' },\n    0x1D56C: { c: 'A', f: 'FRB' },\n    0x1D56D: { c: 'B', f: 'FRB' },\n    0x1D56E: { c: 'C', f: 'FRB' },\n    0x1D56F: { c: 'D', f: 'FRB' },\n    0x1D570: { c: 'E', f: 'FRB' },\n    0x1D571: { c: 'F', f: 'FRB' },\n    0x1D572: { c: 'G', f: 'FRB' },\n    0x1D573: { c: 'H', f: 'FRB' },\n    0x1D574: { c: 'I', f: 'FRB' },\n    0x1D575: { c: 'J', f: 'FRB' },\n    0x1D576: { c: 'K', f: 'FRB' },\n    0x1D577: { c: 'L', f: 'FRB' },\n    0x1D578: { c: 'M', f: 'FRB' },\n    0x1D579: { c: 'N', f: 'FRB' },\n    0x1D57A: { c: 'O', f: 'FRB' },\n    0x1D57B: { c: 'P', f: 'FRB' },\n    0x1D57C: { c: 'Q', f: 'FRB' },\n    0x1D57D: { c: 'R', f: 'FRB' },\n    0x1D57E: { c: 'S', f: 'FRB' },\n    0x1D57F: { c: 'T', f: 'FRB' },\n    0x1D580: { c: 'U', f: 'FRB' },\n    0x1D581: { c: 'V', f: 'FRB' },\n    0x1D582: { c: 'W', f: 'FRB' },\n    0x1D583: { c: 'X', f: 'FRB' },\n    0x1D584: { c: 'Y', f: 'FRB' },\n    0x1D585: { c: 'Z', f: 'FRB' },\n    0x1D586: { c: 'a', f: 'FRB' },\n    0x1D587: { c: 'b', f: 'FRB' },\n    0x1D588: { c: 'c', f: 'FRB' },\n    0x1D589: { c: 'd', f: 'FRB' },\n    0x1D58A: { c: 'e', f: 'FRB' },\n    0x1D58B: { c: 'f', f: 'FRB' },\n    0x1D58C: { c: 'g', f: 'FRB' },\n    0x1D58D: { c: 'h', f: 'FRB' },\n    0x1D58E: { c: 'i', f: 'FRB' },\n    0x1D58F: { c: 'j', f: 'FRB' },\n    0x1D590: { c: 'k', f: 'FRB' },\n    0x1D591: { c: 'l', f: 'FRB' },\n    0x1D592: { c: 'm', f: 'FRB' },\n    0x1D593: { c: 'n', f: 'FRB' },\n    0x1D594: { c: 'o', f: 'FRB' },\n    0x1D595: { c: 'p', f: 'FRB' },\n    0x1D596: { c: 'q', f: 'FRB' },\n    0x1D597: { c: 'r', f: 'FRB' },\n    0x1D598: { c: 's', f: 'FRB' },\n    0x1D599: { c: 't', f: 'FRB' },\n    0x1D59A: { c: 'u', f: 'FRB' },\n    0x1D59B: { c: 'v', f: 'FRB' },\n    0x1D59C: { c: 'w', f: 'FRB' },\n    0x1D59D: { c: 'x', f: 'FRB' },\n    0x1D59E: { c: 'y', f: 'FRB' },\n    0x1D59F: { c: 'z', f: 'FRB' },\n    0x1D5A0: { c: 'A', f: 'SS' },\n    0x1D5A1: { c: 'B', f: 'SS' },\n    0x1D5A2: { c: 'C', f: 'SS' },\n    0x1D5A3: { c: 'D', f: 'SS' },\n    0x1D5A4: { c: 'E', f: 'SS' },\n    0x1D5A5: { c: 'F', f: 'SS' },\n    0x1D5A6: { c: 'G', f: 'SS' },\n    0x1D5A7: { c: 'H', f: 'SS' },\n    0x1D5A8: { c: 'I', f: 'SS' },\n    0x1D5A9: { c: 'J', f: 'SS' },\n    0x1D5AA: { c: 'K', f: 'SS' },\n    0x1D5AB: { c: 'L', f: 'SS' },\n    0x1D5AC: { c: 'M', f: 'SS' },\n    0x1D5AD: { c: 'N', f: 'SS' },\n    0x1D5AE: { c: 'O', f: 'SS' },\n    0x1D5AF: { c: 'P', f: 'SS' },\n    0x1D5B0: { c: 'Q', f: 'SS' },\n    0x1D5B1: { c: 'R', f: 'SS' },\n    0x1D5B2: { c: 'S', f: 'SS' },\n    0x1D5B3: { c: 'T', f: 'SS' },\n    0x1D5B4: { c: 'U', f: 'SS' },\n    0x1D5B5: { c: 'V', f: 'SS' },\n    0x1D5B6: { c: 'W', f: 'SS' },\n    0x1D5B7: { c: 'X', f: 'SS' },\n    0x1D5B8: { c: 'Y', f: 'SS' },\n    0x1D5B9: { c: 'Z', f: 'SS' },\n    0x1D5BA: { c: 'a', f: 'SS' },\n    0x1D5BB: { c: 'b', f: 'SS' },\n    0x1D5BC: { c: 'c', f: 'SS' },\n    0x1D5BD: { c: 'd', f: 'SS' },\n    0x1D5BE: { c: 'e', f: 'SS' },\n    0x1D5BF: { c: 'f', f: 'SS' },\n    0x1D5C0: { c: 'g', f: 'SS' },\n    0x1D5C1: { c: 'h', f: 'SS' },\n    0x1D5C2: { c: 'i', f: 'SS' },\n    0x1D5C3: { c: 'j', f: 'SS' },\n    0x1D5C4: { c: 'k', f: 'SS' },\n    0x1D5C5: { c: 'l', f: 'SS' },\n    0x1D5C6: { c: 'm', f: 'SS' },\n    0x1D5C7: { c: 'n', f: 'SS' },\n    0x1D5C8: { c: 'o', f: 'SS' },\n    0x1D5C9: { c: 'p', f: 'SS' },\n    0x1D5CA: { c: 'q', f: 'SS' },\n    0x1D5CB: { c: 'r', f: 'SS' },\n    0x1D5CC: { c: 's', f: 'SS' },\n    0x1D5CD: { c: 't', f: 'SS' },\n    0x1D5CE: { c: 'u', f: 'SS' },\n    0x1D5CF: { c: 'v', f: 'SS' },\n    0x1D5D0: { c: 'w', f: 'SS' },\n    0x1D5D1: { c: 'x', f: 'SS' },\n    0x1D5D2: { c: 'y', f: 'SS' },\n    0x1D5D3: { c: 'z', f: 'SS' },\n    0x1D5D4: { c: 'A', f: 'SSB' },\n    0x1D5D5: { c: 'B', f: 'SSB' },\n    0x1D5D6: { c: 'C', f: 'SSB' },\n    0x1D5D7: { c: 'D', f: 'SSB' },\n    0x1D5D8: { c: 'E', f: 'SSB' },\n    0x1D5D9: { c: 'F', f: 'SSB' },\n    0x1D5DA: { c: 'G', f: 'SSB' },\n    0x1D5DB: { c: 'H', f: 'SSB' },\n    0x1D5DC: { c: 'I', f: 'SSB' },\n    0x1D5DD: { c: 'J', f: 'SSB' },\n    0x1D5DE: { c: 'K', f: 'SSB' },\n    0x1D5DF: { c: 'L', f: 'SSB' },\n    0x1D5E0: { c: 'M', f: 'SSB' },\n    0x1D5E1: { c: 'N', f: 'SSB' },\n    0x1D5E2: { c: 'O', f: 'SSB' },\n    0x1D5E3: { c: 'P', f: 'SSB' },\n    0x1D5E4: { c: 'Q', f: 'SSB' },\n    0x1D5E5: { c: 'R', f: 'SSB' },\n    0x1D5E6: { c: 'S', f: 'SSB' },\n    0x1D5E7: { c: 'T', f: 'SSB' },\n    0x1D5E8: { c: 'U', f: 'SSB' },\n    0x1D5E9: { c: 'V', f: 'SSB' },\n    0x1D5EA: { c: 'W', f: 'SSB' },\n    0x1D5EB: { c: 'X', f: 'SSB' },\n    0x1D5EC: { c: 'Y', f: 'SSB' },\n    0x1D5ED: { c: 'Z', f: 'SSB' },\n    0x1D5EE: { c: 'a', f: 'SSB' },\n    0x1D5EF: { c: 'b', f: 'SSB' },\n    0x1D5F0: { c: 'c', f: 'SSB' },\n    0x1D5F1: { c: 'd', f: 'SSB' },\n    0x1D5F2: { c: 'e', f: 'SSB' },\n    0x1D5F3: { c: 'f', f: 'SSB' },\n    0x1D5F4: { c: 'g', f: 'SSB' },\n    0x1D5F5: { c: 'h', f: 'SSB' },\n    0x1D5F6: { c: 'i', f: 'SSB' },\n    0x1D5F7: { c: 'j', f: 'SSB' },\n    0x1D5F8: { c: 'k', f: 'SSB' },\n    0x1D5F9: { c: 'l', f: 'SSB' },\n    0x1D5FA: { c: 'm', f: 'SSB' },\n    0x1D5FB: { c: 'n', f: 'SSB' },\n    0x1D5FC: { c: 'o', f: 'SSB' },\n    0x1D5FD: { c: 'p', f: 'SSB' },\n    0x1D5FE: { c: 'q', f: 'SSB' },\n    0x1D5FF: { c: 'r', f: 'SSB' },\n    0x1D600: { c: 's', f: 'SSB' },\n    0x1D601: { c: 't', f: 'SSB' },\n    0x1D602: { c: 'u', f: 'SSB' },\n    0x1D603: { c: 'v', f: 'SSB' },\n    0x1D604: { c: 'w', f: 'SSB' },\n    0x1D605: { c: 'x', f: 'SSB' },\n    0x1D606: { c: 'y', f: 'SSB' },\n    0x1D607: { c: 'z', f: 'SSB' },\n    0x1D608: { c: 'A', f: 'SSI' },\n    0x1D609: { c: 'B', f: 'SSI' },\n    0x1D60A: { c: 'C', f: 'SSI' },\n    0x1D60B: { c: 'D', f: 'SSI' },\n    0x1D60C: { c: 'E', f: 'SSI' },\n    0x1D60D: { c: 'F', f: 'SSI' },\n    0x1D60E: { c: 'G', f: 'SSI' },\n    0x1D60F: { c: 'H', f: 'SSI' },\n    0x1D610: { c: 'I', f: 'SSI' },\n    0x1D611: { c: 'J', f: 'SSI' },\n    0x1D612: { c: 'K', f: 'SSI' },\n    0x1D613: { c: 'L', f: 'SSI' },\n    0x1D614: { c: 'M', f: 'SSI' },\n    0x1D615: { c: 'N', f: 'SSI' },\n    0x1D616: { c: 'O', f: 'SSI' },\n    0x1D617: { c: 'P', f: 'SSI' },\n    0x1D618: { c: 'Q', f: 'SSI' },\n    0x1D619: { c: 'R', f: 'SSI' },\n    0x1D61A: { c: 'S', f: 'SSI' },\n    0x1D61B: { c: 'T', f: 'SSI' },\n    0x1D61C: { c: 'U', f: 'SSI' },\n    0x1D61D: { c: 'V', f: 'SSI' },\n    0x1D61E: { c: 'W', f: 'SSI' },\n    0x1D61F: { c: 'X', f: 'SSI' },\n    0x1D620: { c: 'Y', f: 'SSI' },\n    0x1D621: { c: 'Z', f: 'SSI' },\n    0x1D622: { c: 'a', f: 'SSI' },\n    0x1D623: { c: 'b', f: 'SSI' },\n    0x1D624: { c: 'c', f: 'SSI' },\n    0x1D625: { c: 'd', f: 'SSI' },\n    0x1D626: { c: 'e', f: 'SSI' },\n    0x1D627: { c: 'f', f: 'SSI' },\n    0x1D628: { c: 'g', f: 'SSI' },\n    0x1D629: { c: 'h', f: 'SSI' },\n    0x1D62A: { c: 'i', f: 'SSI' },\n    0x1D62B: { c: 'j', f: 'SSI' },\n    0x1D62C: { c: 'k', f: 'SSI' },\n    0x1D62D: { c: 'l', f: 'SSI' },\n    0x1D62E: { c: 'm', f: 'SSI' },\n    0x1D62F: { c: 'n', f: 'SSI' },\n    0x1D630: { c: 'o', f: 'SSI' },\n    0x1D631: { c: 'p', f: 'SSI' },\n    0x1D632: { c: 'q', f: 'SSI' },\n    0x1D633: { c: 'r', f: 'SSI' },\n    0x1D634: { c: 's', f: 'SSI' },\n    0x1D635: { c: 't', f: 'SSI' },\n    0x1D636: { c: 'u', f: 'SSI' },\n    0x1D637: { c: 'v', f: 'SSI' },\n    0x1D638: { c: 'w', f: 'SSI' },\n    0x1D639: { c: 'x', f: 'SSI' },\n    0x1D63A: { c: 'y', f: 'SSI' },\n    0x1D63B: { c: 'z', f: 'SSI' },\n    0x1D670: { c: 'A', f: 'T' },\n    0x1D671: { c: 'B', f: 'T' },\n    0x1D672: { c: 'C', f: 'T' },\n    0x1D673: { c: 'D', f: 'T' },\n    0x1D674: { c: 'E', f: 'T' },\n    0x1D675: { c: 'F', f: 'T' },\n    0x1D676: { c: 'G', f: 'T' },\n    0x1D677: { c: 'H', f: 'T' },\n    0x1D678: { c: 'I', f: 'T' },\n    0x1D679: { c: 'J', f: 'T' },\n    0x1D67A: { c: 'K', f: 'T' },\n    0x1D67B: { c: 'L', f: 'T' },\n    0x1D67C: { c: 'M', f: 'T' },\n    0x1D67D: { c: 'N', f: 'T' },\n    0x1D67E: { c: 'O', f: 'T' },\n    0x1D67F: { c: 'P', f: 'T' },\n    0x1D680: { c: 'Q', f: 'T' },\n    0x1D681: { c: 'R', f: 'T' },\n    0x1D682: { c: 'S', f: 'T' },\n    0x1D683: { c: 'T', f: 'T' },\n    0x1D684: { c: 'U', f: 'T' },\n    0x1D685: { c: 'V', f: 'T' },\n    0x1D686: { c: 'W', f: 'T' },\n    0x1D687: { c: 'X', f: 'T' },\n    0x1D688: { c: 'Y', f: 'T' },\n    0x1D689: { c: 'Z', f: 'T' },\n    0x1D68A: { c: 'a', f: 'T' },\n    0x1D68B: { c: 'b', f: 'T' },\n    0x1D68C: { c: 'c', f: 'T' },\n    0x1D68D: { c: 'd', f: 'T' },\n    0x1D68E: { c: 'e', f: 'T' },\n    0x1D68F: { c: 'f', f: 'T' },\n    0x1D690: { c: 'g', f: 'T' },\n    0x1D691: { c: 'h', f: 'T' },\n    0x1D692: { c: 'i', f: 'T' },\n    0x1D693: { c: 'j', f: 'T' },\n    0x1D694: { c: 'k', f: 'T' },\n    0x1D695: { c: 'l', f: 'T' },\n    0x1D696: { c: 'm', f: 'T' },\n    0x1D697: { c: 'n', f: 'T' },\n    0x1D698: { c: 'o', f: 'T' },\n    0x1D699: { c: 'p', f: 'T' },\n    0x1D69A: { c: 'q', f: 'T' },\n    0x1D69B: { c: 'r', f: 'T' },\n    0x1D69C: { c: 's', f: 'T' },\n    0x1D69D: { c: 't', f: 'T' },\n    0x1D69E: { c: 'u', f: 'T' },\n    0x1D69F: { c: 'v', f: 'T' },\n    0x1D6A0: { c: 'w', f: 'T' },\n    0x1D6A1: { c: 'x', f: 'T' },\n    0x1D6A2: { c: 'y', f: 'T' },\n    0x1D6A3: { c: 'z', f: 'T' },\n    0x1D6A8: { c: 'A', f: 'B' },\n    0x1D6A9: { c: 'B', f: 'B' },\n    0x1D6AA: { c: '\\\\393', f: 'B' },\n    0x1D6AB: { c: '\\\\394', f: 'B' },\n    0x1D6AC: { c: 'E', f: 'B' },\n    0x1D6AD: { c: 'Z', f: 'B' },\n    0x1D6AE: { c: 'H', f: 'B' },\n    0x1D6AF: { c: '\\\\398', f: 'B' },\n    0x1D6B0: { c: 'I', f: 'B' },\n    0x1D6B1: { c: 'K', f: 'B' },\n    0x1D6B2: { c: '\\\\39B', f: 'B' },\n    0x1D6B3: { c: 'M', f: 'B' },\n    0x1D6B4: { c: 'N', f: 'B' },\n    0x1D6B5: { c: '\\\\39E', f: 'B' },\n    0x1D6B6: { c: 'O', f: 'B' },\n    0x1D6B7: { c: '\\\\3A0', f: 'B' },\n    0x1D6B8: { c: 'P', f: 'B' },\n    0x1D6BA: { c: '\\\\3A3', f: 'B' },\n    0x1D6BB: { c: 'T', f: 'B' },\n    0x1D6BC: { c: '\\\\3A5', f: 'B' },\n    0x1D6BD: { c: '\\\\3A6', f: 'B' },\n    0x1D6BE: { c: 'X', f: 'B' },\n    0x1D6BF: { c: '\\\\3A8', f: 'B' },\n    0x1D6C0: { c: '\\\\3A9', f: 'B' },\n    0x1D6C1: { c: '\\\\2207', f: 'B' },\n    0x1D6E2: { c: 'A', f: 'I' },\n    0x1D6E3: { c: 'B', f: 'I' },\n    0x1D6E4: { c: '\\\\393', f: 'I' },\n    0x1D6E5: { c: '\\\\394', f: 'I' },\n    0x1D6E6: { c: 'E', f: 'I' },\n    0x1D6E7: { c: 'Z', f: 'I' },\n    0x1D6E8: { c: 'H', f: 'I' },\n    0x1D6E9: { c: '\\\\398', f: 'I' },\n    0x1D6EA: { c: 'I', f: 'I' },\n    0x1D6EB: { c: 'K', f: 'I' },\n    0x1D6EC: { c: '\\\\39B', f: 'I' },\n    0x1D6ED: { c: 'M', f: 'I' },\n    0x1D6EE: { c: 'N', f: 'I' },\n    0x1D6EF: { c: '\\\\39E', f: 'I' },\n    0x1D6F0: { c: 'O', f: 'I' },\n    0x1D6F1: { c: '\\\\3A0', f: 'I' },\n    0x1D6F2: { c: 'P', f: 'I' },\n    0x1D6F4: { c: '\\\\3A3', f: 'I' },\n    0x1D6F5: { c: 'T', f: 'I' },\n    0x1D6F6: { c: '\\\\3A5', f: 'I' },\n    0x1D6F7: { c: '\\\\3A6', f: 'I' },\n    0x1D6F8: { c: 'X', f: 'I' },\n    0x1D6F9: { c: '\\\\3A8', f: 'I' },\n    0x1D6FA: { c: '\\\\3A9', f: 'I' },\n    0x1D6FC: { c: '\\\\3B1', f: 'I' },\n    0x1D6FD: { c: '\\\\3B2', f: 'I' },\n    0x1D6FE: { c: '\\\\3B3', f: 'I' },\n    0x1D6FF: { c: '\\\\3B4', f: 'I' },\n    0x1D700: { c: '\\\\3B5', f: 'I' },\n    0x1D701: { c: '\\\\3B6', f: 'I' },\n    0x1D702: { c: '\\\\3B7', f: 'I' },\n    0x1D703: { c: '\\\\3B8', f: 'I' },\n    0x1D704: { c: '\\\\3B9', f: 'I' },\n    0x1D705: { c: '\\\\3BA', f: 'I' },\n    0x1D706: { c: '\\\\3BB', f: 'I' },\n    0x1D707: { c: '\\\\3BC', f: 'I' },\n    0x1D708: { c: '\\\\3BD', f: 'I' },\n    0x1D709: { c: '\\\\3BE', f: 'I' },\n    0x1D70A: { c: '\\\\3BF', f: 'I' },\n    0x1D70B: { c: '\\\\3C0', f: 'I' },\n    0x1D70C: { c: '\\\\3C1', f: 'I' },\n    0x1D70D: { c: '\\\\3C2', f: 'I' },\n    0x1D70E: { c: '\\\\3C3', f: 'I' },\n    0x1D70F: { c: '\\\\3C4', f: 'I' },\n    0x1D710: { c: '\\\\3C5', f: 'I' },\n    0x1D711: { c: '\\\\3C6', f: 'I' },\n    0x1D712: { c: '\\\\3C7', f: 'I' },\n    0x1D713: { c: '\\\\3C8', f: 'I' },\n    0x1D714: { c: '\\\\3C9', f: 'I' },\n    0x1D715: { c: '\\\\2202' },\n    0x1D716: { c: '\\\\3F5', f: 'I' },\n    0x1D717: { c: '\\\\3D1', f: 'I' },\n    0x1D718: { c: '\\\\E009', f: 'A' },\n    0x1D719: { c: '\\\\3D5', f: 'I' },\n    0x1D71A: { c: '\\\\3F1', f: 'I' },\n    0x1D71B: { c: '\\\\3D6', f: 'I' },\n    0x1D71C: { c: 'A', f: 'BI' },\n    0x1D71D: { c: 'B', f: 'BI' },\n    0x1D71E: { c: '\\\\393', f: 'BI' },\n    0x1D71F: { c: '\\\\394', f: 'BI' },\n    0x1D720: { c: 'E', f: 'BI' },\n    0x1D721: { c: 'Z', f: 'BI' },\n    0x1D722: { c: 'H', f: 'BI' },\n    0x1D723: { c: '\\\\398', f: 'BI' },\n    0x1D724: { c: 'I', f: 'BI' },\n    0x1D725: { c: 'K', f: 'BI' },\n    0x1D726: { c: '\\\\39B', f: 'BI' },\n    0x1D727: { c: 'M', f: 'BI' },\n    0x1D728: { c: 'N', f: 'BI' },\n    0x1D729: { c: '\\\\39E', f: 'BI' },\n    0x1D72A: { c: 'O', f: 'BI' },\n    0x1D72B: { c: '\\\\3A0', f: 'BI' },\n    0x1D72C: { c: 'P', f: 'BI' },\n    0x1D72E: { c: '\\\\3A3', f: 'BI' },\n    0x1D72F: { c: 'T', f: 'BI' },\n    0x1D730: { c: '\\\\3A5', f: 'BI' },\n    0x1D731: { c: '\\\\3A6', f: 'BI' },\n    0x1D732: { c: 'X', f: 'BI' },\n    0x1D733: { c: '\\\\3A8', f: 'BI' },\n    0x1D734: { c: '\\\\3A9', f: 'BI' },\n    0x1D736: { c: '\\\\3B1', f: 'BI' },\n    0x1D737: { c: '\\\\3B2', f: 'BI' },\n    0x1D738: { c: '\\\\3B3', f: 'BI' },\n    0x1D739: { c: '\\\\3B4', f: 'BI' },\n    0x1D73A: { c: '\\\\3B5', f: 'BI' },\n    0x1D73B: { c: '\\\\3B6', f: 'BI' },\n    0x1D73C: { c: '\\\\3B7', f: 'BI' },\n    0x1D73D: { c: '\\\\3B8', f: 'BI' },\n    0x1D73E: { c: '\\\\3B9', f: 'BI' },\n    0x1D73F: { c: '\\\\3BA', f: 'BI' },\n    0x1D740: { c: '\\\\3BB', f: 'BI' },\n    0x1D741: { c: '\\\\3BC', f: 'BI' },\n    0x1D742: { c: '\\\\3BD', f: 'BI' },\n    0x1D743: { c: '\\\\3BE', f: 'BI' },\n    0x1D744: { c: '\\\\3BF', f: 'BI' },\n    0x1D745: { c: '\\\\3C0', f: 'BI' },\n    0x1D746: { c: '\\\\3C1', f: 'BI' },\n    0x1D747: { c: '\\\\3C2', f: 'BI' },\n    0x1D748: { c: '\\\\3C3', f: 'BI' },\n    0x1D749: { c: '\\\\3C4', f: 'BI' },\n    0x1D74A: { c: '\\\\3C5', f: 'BI' },\n    0x1D74B: { c: '\\\\3C6', f: 'BI' },\n    0x1D74C: { c: '\\\\3C7', f: 'BI' },\n    0x1D74D: { c: '\\\\3C8', f: 'BI' },\n    0x1D74E: { c: '\\\\3C9', f: 'BI' },\n    0x1D74F: { c: '\\\\2202', f: 'B' },\n    0x1D750: { c: '\\\\3F5', f: 'BI' },\n    0x1D751: { c: '\\\\3D1', f: 'BI' },\n    0x1D752: { c: '\\\\E009', f: 'A' },\n    0x1D753: { c: '\\\\3D5', f: 'BI' },\n    0x1D754: { c: '\\\\3F1', f: 'BI' },\n    0x1D755: { c: '\\\\3D6', f: 'BI' },\n    0x1D756: { c: 'A', f: 'SSB' },\n    0x1D757: { c: 'B', f: 'SSB' },\n    0x1D758: { c: '\\\\393', f: 'SSB' },\n    0x1D759: { c: '\\\\394', f: 'SSB' },\n    0x1D75A: { c: 'E', f: 'SSB' },\n    0x1D75B: { c: 'Z', f: 'SSB' },\n    0x1D75C: { c: 'H', f: 'SSB' },\n    0x1D75D: { c: '\\\\398', f: 'SSB' },\n    0x1D75E: { c: 'I', f: 'SSB' },\n    0x1D75F: { c: 'K', f: 'SSB' },\n    0x1D760: { c: '\\\\39B', f: 'SSB' },\n    0x1D761: { c: 'M', f: 'SSB' },\n    0x1D762: { c: 'N', f: 'SSB' },\n    0x1D763: { c: '\\\\39E', f: 'SSB' },\n    0x1D764: { c: 'O', f: 'SSB' },\n    0x1D765: { c: '\\\\3A0', f: 'SSB' },\n    0x1D766: { c: 'P', f: 'SSB' },\n    0x1D768: { c: '\\\\3A3', f: 'SSB' },\n    0x1D769: { c: 'T', f: 'SSB' },\n    0x1D76A: { c: '\\\\3A5', f: 'SSB' },\n    0x1D76B: { c: '\\\\3A6', f: 'SSB' },\n    0x1D76C: { c: 'X', f: 'SSB' },\n    0x1D76D: { c: '\\\\3A8', f: 'SSB' },\n    0x1D76E: { c: '\\\\3A9', f: 'SSB' },\n    0x1D7CE: { c: '0', f: 'B' },\n    0x1D7CF: { c: '1', f: 'B' },\n    0x1D7D0: { c: '2', f: 'B' },\n    0x1D7D1: { c: '3', f: 'B' },\n    0x1D7D2: { c: '4', f: 'B' },\n    0x1D7D3: { c: '5', f: 'B' },\n    0x1D7D4: { c: '6', f: 'B' },\n    0x1D7D5: { c: '7', f: 'B' },\n    0x1D7D6: { c: '8', f: 'B' },\n    0x1D7D7: { c: '9', f: 'B' },\n    0x1D7E2: { c: '0', f: 'SS' },\n    0x1D7E3: { c: '1', f: 'SS' },\n    0x1D7E4: { c: '2', f: 'SS' },\n    0x1D7E5: { c: '3', f: 'SS' },\n    0x1D7E6: { c: '4', f: 'SS' },\n    0x1D7E7: { c: '5', f: 'SS' },\n    0x1D7E8: { c: '6', f: 'SS' },\n    0x1D7E9: { c: '7', f: 'SS' },\n    0x1D7EA: { c: '8', f: 'SS' },\n    0x1D7EB: { c: '9', f: 'SS' },\n    0x1D7EC: { c: '0', f: 'SSB' },\n    0x1D7ED: { c: '1', f: 'SSB' },\n    0x1D7EE: { c: '2', f: 'SSB' },\n    0x1D7EF: { c: '3', f: 'SSB' },\n    0x1D7F0: { c: '4', f: 'SSB' },\n    0x1D7F1: { c: '5', f: 'SSB' },\n    0x1D7F2: { c: '6', f: 'SSB' },\n    0x1D7F3: { c: '7', f: 'SSB' },\n    0x1D7F4: { c: '8', f: 'SSB' },\n    0x1D7F5: { c: '9', f: 'SSB' },\n    0x1D7F6: { c: '0', f: 'T' },\n    0x1D7F7: { c: '1', f: 'T' },\n    0x1D7F8: { c: '2', f: 'T' },\n    0x1D7F9: { c: '3', f: 'T' },\n    0x1D7FA: { c: '4', f: 'T' },\n    0x1D7FB: { c: '5', f: 'T' },\n    0x1D7FC: { c: '6', f: 'T' },\n    0x1D7FD: { c: '7', f: 'T' },\n    0x1D7FE: { c: '8', f: 'T' },\n    0x1D7FF: { c: '9', f: 'T' },\n});\n//# sourceMappingURL=normal.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sansSerifBoldItalic = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar sans_serif_bold_italic_js_1 = require(\"../../../common/fonts/tex/sans-serif-bold-italic.js\");\nexports.sansSerifBoldItalic = (0, FontData_js_1.AddCSS)(sans_serif_bold_italic_js_1.sansSerifBoldItalic, {\n    0x131: { f: 'SSB' },\n    0x237: { f: 'SSB' },\n});\n//# sourceMappingURL=sans-serif-bold-italic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sansSerifBold = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar sans_serif_bold_js_1 = require(\"../../../common/fonts/tex/sans-serif-bold.js\");\nexports.sansSerifBold = (0, FontData_js_1.AddCSS)(sans_serif_bold_js_1.sansSerifBold, {\n    0x2015: { c: '\\\\2014' },\n    0x2017: { c: '_' },\n    0x2044: { c: '/' },\n    0x2206: { c: '\\\\394' },\n});\n//# sourceMappingURL=sans-serif-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sansSerifItalic = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar sans_serif_italic_js_1 = require(\"../../../common/fonts/tex/sans-serif-italic.js\");\nexports.sansSerifItalic = (0, FontData_js_1.AddCSS)(sans_serif_italic_js_1.sansSerifItalic, {\n    0x391: { c: 'A' },\n    0x392: { c: 'B' },\n    0x395: { c: 'E' },\n    0x396: { c: 'Z' },\n    0x397: { c: 'H' },\n    0x399: { c: 'I' },\n    0x39A: { c: 'K' },\n    0x39C: { c: 'M' },\n    0x39D: { c: 'N' },\n    0x39F: { c: 'O' },\n    0x3A1: { c: 'P' },\n    0x3A4: { c: 'T' },\n    0x3A7: { c: 'X' },\n    0x2015: { c: '\\\\2014' },\n    0x2017: { c: '_' },\n    0x2044: { c: '/' },\n    0x2206: { c: '\\\\394' },\n});\n//# sourceMappingURL=sans-serif-italic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sansSerif = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar sans_serif_js_1 = require(\"../../../common/fonts/tex/sans-serif.js\");\nexports.sansSerif = (0, FontData_js_1.AddCSS)(sans_serif_js_1.sansSerif, {\n    0x391: { c: 'A' },\n    0x392: { c: 'B' },\n    0x395: { c: 'E' },\n    0x396: { c: 'Z' },\n    0x397: { c: 'H' },\n    0x399: { c: 'I' },\n    0x39A: { c: 'K' },\n    0x39C: { c: 'M' },\n    0x39D: { c: 'N' },\n    0x39F: { c: 'O' },\n    0x3A1: { c: 'P' },\n    0x3A4: { c: 'T' },\n    0x3A7: { c: 'X' },\n    0x2015: { c: '\\\\2014' },\n    0x2017: { c: '_' },\n    0x2044: { c: '/' },\n    0x2206: { c: '\\\\394' },\n});\n//# sourceMappingURL=sans-serif.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.scriptBold = void 0;\nvar script_bold_js_1 = require(\"../../../common/fonts/tex/script-bold.js\");\nObject.defineProperty(exports, \"scriptBold\", { enumerable: true, get: function () { return script_bold_js_1.scriptBold; } });\n//# sourceMappingURL=script-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.script = void 0;\nvar script_js_1 = require(\"../../../common/fonts/tex/script.js\");\nObject.defineProperty(exports, \"script\", { enumerable: true, get: function () { return script_js_1.script; } });\n//# sourceMappingURL=script.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.smallop = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar smallop_js_1 = require(\"../../../common/fonts/tex/smallop.js\");\nexports.smallop = (0, FontData_js_1.AddCSS)(smallop_js_1.smallop, {\n    0x2044: { c: '/' },\n    0x2329: { c: '\\\\27E8' },\n    0x232A: { c: '\\\\27E9' },\n    0x2758: { c: '\\\\2223' },\n    0x2A0C: { c: '\\\\222C\\\\222C' },\n    0x3008: { c: '\\\\27E8' },\n    0x3009: { c: '\\\\27E9' },\n});\n//# sourceMappingURL=smallop.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texCalligraphicBold = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar tex_calligraphic_bold_js_1 = require(\"../../../common/fonts/tex/tex-calligraphic-bold.js\");\nexports.texCalligraphicBold = (0, FontData_js_1.AddCSS)(tex_calligraphic_bold_js_1.texCalligraphicBold, {\n    0x131: { f: 'B' },\n    0x237: { f: 'B' },\n});\n//# sourceMappingURL=tex-calligraphic-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texCalligraphic = void 0;\nvar tex_calligraphic_js_1 = require(\"../../../common/fonts/tex/tex-calligraphic.js\");\nObject.defineProperty(exports, \"texCalligraphic\", { enumerable: true, get: function () { return tex_calligraphic_js_1.texCalligraphic; } });\n//# sourceMappingURL=tex-calligraphic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texMathit = void 0;\nvar tex_mathit_js_1 = require(\"../../../common/fonts/tex/tex-mathit.js\");\nObject.defineProperty(exports, \"texMathit\", { enumerable: true, get: function () { return tex_mathit_js_1.texMathit; } });\n//# sourceMappingURL=tex-mathit.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texOldstyleBold = void 0;\nvar tex_oldstyle_bold_js_1 = require(\"../../../common/fonts/tex/tex-oldstyle-bold.js\");\nObject.defineProperty(exports, \"texOldstyleBold\", { enumerable: true, get: function () { return tex_oldstyle_bold_js_1.texOldstyleBold; } });\n//# sourceMappingURL=tex-oldstyle-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texOldstyle = void 0;\nvar tex_oldstyle_js_1 = require(\"../../../common/fonts/tex/tex-oldstyle.js\");\nObject.defineProperty(exports, \"texOldstyle\", { enumerable: true, get: function () { return tex_oldstyle_js_1.texOldstyle; } });\n//# sourceMappingURL=tex-oldstyle.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texSize3 = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar tex_size3_js_1 = require(\"../../../common/fonts/tex/tex-size3.js\");\nexports.texSize3 = (0, FontData_js_1.AddCSS)(tex_size3_js_1.texSize3, {\n    0x2044: { c: '/' },\n    0x2329: { c: '\\\\27E8' },\n    0x232A: { c: '\\\\27E9' },\n    0x3008: { c: '\\\\27E8' },\n    0x3009: { c: '\\\\27E9' },\n});\n//# sourceMappingURL=tex-size3.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texSize4 = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar tex_size4_js_1 = require(\"../../../common/fonts/tex/tex-size4.js\");\nexports.texSize4 = (0, FontData_js_1.AddCSS)(tex_size4_js_1.texSize4, {\n    0x2044: { c: '/' },\n    0x2329: { c: '\\\\27E8' },\n    0x232A: { c: '\\\\27E9' },\n    0x3008: { c: '\\\\27E8' },\n    0x3009: { c: '\\\\27E9' },\n    0xE155: { c: '\\\\E153\\\\E152' },\n    0xE156: { c: '\\\\E151\\\\E150' },\n});\n//# sourceMappingURL=tex-size4.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texVariant = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nvar tex_variant_js_1 = require(\"../../../common/fonts/tex/tex-variant.js\");\nexports.texVariant = (0, FontData_js_1.AddCSS)(tex_variant_js_1.texVariant, {\n    0x3F0: { c: '\\\\E009' },\n    0x210F: { f: '' },\n    0x2224: { c: '\\\\E006' },\n    0x2226: { c: '\\\\E007' },\n    0x2268: { c: '\\\\E00C' },\n    0x2269: { c: '\\\\E00D' },\n    0x2270: { c: '\\\\E011' },\n    0x2271: { c: '\\\\E00E' },\n    0x2288: { c: '\\\\E016' },\n    0x2289: { c: '\\\\E018' },\n    0x228A: { c: '\\\\E01A' },\n    0x228B: { c: '\\\\E01B' },\n    0x2A87: { c: '\\\\E010' },\n    0x2A88: { c: '\\\\E00F' },\n    0x2ACB: { c: '\\\\E017' },\n    0x2ACC: { c: '\\\\E019' },\n});\n//# sourceMappingURL=tex-variant.js.map", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FontData = exports.NOSTRETCH = exports.H = exports.V = void 0;\nvar Options_js_1 = require(\"../../util/Options.js\");\nexports.V = 1;\nexports.H = 2;\nexports.NOSTRETCH = { dir: 0 };\nvar FontData = (function () {\n    function FontData(options) {\n        var e_1, _a, e_2, _b;\n        if (options === void 0) { options = null; }\n        this.variant = {};\n        this.delimiters = {};\n        this.cssFontMap = {};\n        this.remapChars = {};\n        this.skewIcFactor = .75;\n        var CLASS = this.constructor;\n        this.options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, CLASS.OPTIONS), options);\n        this.params = __assign({}, CLASS.defaultParams);\n        this.sizeVariants = __spreadArray([], __read(CLASS.defaultSizeVariants), false);\n        this.stretchVariants = __spreadArray([], __read(CLASS.defaultStretchVariants), false);\n        this.cssFontMap = __assign({}, CLASS.defaultCssFonts);\n        try {\n            for (var _c = __values(Object.keys(this.cssFontMap)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var name_1 = _d.value;\n                if (this.cssFontMap[name_1][0] === 'unknown') {\n                    this.cssFontMap[name_1][0] = this.options.unknownFamily;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        this.cssFamilyPrefix = CLASS.defaultCssFamilyPrefix;\n        this.createVariants(CLASS.defaultVariants);\n        this.defineDelimiters(CLASS.defaultDelimiters);\n        try {\n            for (var _e = __values(Object.keys(CLASS.defaultChars)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var name_2 = _f.value;\n                this.defineChars(name_2, CLASS.defaultChars[name_2]);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        this.defineRemap('accent', CLASS.defaultAccentMap);\n        this.defineRemap('mo', CLASS.defaultMoMap);\n        this.defineRemap('mn', CLASS.defaultMnMap);\n    }\n    FontData.charOptions = function (font, n) {\n        var char = font[n];\n        if (char.length === 3) {\n            char[3] = {};\n        }\n        return char[3];\n    };\n    Object.defineProperty(FontData.prototype, \"styles\", {\n        get: function () {\n            return this._styles;\n        },\n        set: function (style) {\n            this._styles = style;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    FontData.prototype.createVariant = function (name, inherit, link) {\n        if (inherit === void 0) { inherit = null; }\n        if (link === void 0) { link = null; }\n        var variant = {\n            linked: [],\n            chars: (inherit ? Object.create(this.variant[inherit].chars) : {})\n        };\n        if (link && this.variant[link]) {\n            Object.assign(variant.chars, this.variant[link].chars);\n            this.variant[link].linked.push(variant.chars);\n            variant.chars = Object.create(variant.chars);\n        }\n        this.remapSmpChars(variant.chars, name);\n        this.variant[name] = variant;\n    };\n    FontData.prototype.remapSmpChars = function (chars, name) {\n        var e_3, _a, e_4, _b;\n        var CLASS = this.constructor;\n        if (CLASS.VariantSmp[name]) {\n            var SmpRemap = CLASS.SmpRemap;\n            var SmpGreek = [null, null, CLASS.SmpRemapGreekU, CLASS.SmpRemapGreekL];\n            try {\n                for (var _c = __values(CLASS.SmpRanges), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var _e = __read(_d.value, 3), i = _e[0], lo = _e[1], hi = _e[2];\n                    var base = CLASS.VariantSmp[name][i];\n                    if (!base)\n                        continue;\n                    for (var n = lo; n <= hi; n++) {\n                        if (n === 0x3A2)\n                            continue;\n                        var smp = base + n - lo;\n                        chars[n] = this.smpChar(SmpRemap[smp] || smp);\n                    }\n                    if (SmpGreek[i]) {\n                        try {\n                            for (var _f = (e_4 = void 0, __values(Object.keys(SmpGreek[i]).map(function (x) { return parseInt(x); }))), _g = _f.next(); !_g.done; _g = _f.next()) {\n                                var n = _g.value;\n                                chars[n] = this.smpChar(base + SmpGreek[i][n]);\n                            }\n                        }\n                        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                        finally {\n                            try {\n                                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                            }\n                            finally { if (e_4) throw e_4.error; }\n                        }\n                    }\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n        }\n        if (name === 'bold') {\n            chars[0x3DC] = this.smpChar(0x1D7CA);\n            chars[0x3DD] = this.smpChar(0x1D7CB);\n        }\n    };\n    FontData.prototype.smpChar = function (n) {\n        return [, , , { smp: n }];\n    };\n    FontData.prototype.createVariants = function (variants) {\n        var e_5, _a;\n        try {\n            for (var variants_1 = __values(variants), variants_1_1 = variants_1.next(); !variants_1_1.done; variants_1_1 = variants_1.next()) {\n                var variant = variants_1_1.value;\n                this.createVariant(variant[0], variant[1], variant[2]);\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (variants_1_1 && !variants_1_1.done && (_a = variants_1.return)) _a.call(variants_1);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n    };\n    FontData.prototype.defineChars = function (name, chars) {\n        var e_6, _a;\n        var variant = this.variant[name];\n        Object.assign(variant.chars, chars);\n        try {\n            for (var _b = __values(variant.linked), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var link = _c.value;\n                Object.assign(link, chars);\n            }\n        }\n        catch (e_6_1) { e_6 = { error: e_6_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_6) throw e_6.error; }\n        }\n    };\n    FontData.prototype.defineDelimiters = function (delims) {\n        Object.assign(this.delimiters, delims);\n    };\n    FontData.prototype.defineRemap = function (name, remap) {\n        if (!this.remapChars.hasOwnProperty(name)) {\n            this.remapChars[name] = {};\n        }\n        Object.assign(this.remapChars[name], remap);\n    };\n    FontData.prototype.getDelimiter = function (n) {\n        return this.delimiters[n];\n    };\n    FontData.prototype.getSizeVariant = function (n, i) {\n        if (this.delimiters[n].variants) {\n            i = this.delimiters[n].variants[i];\n        }\n        return this.sizeVariants[i];\n    };\n    FontData.prototype.getStretchVariant = function (n, i) {\n        return this.stretchVariants[this.delimiters[n].stretchv ? this.delimiters[n].stretchv[i] : 0];\n    };\n    FontData.prototype.getChar = function (name, n) {\n        return this.variant[name].chars[n];\n    };\n    FontData.prototype.getVariant = function (name) {\n        return this.variant[name];\n    };\n    FontData.prototype.getCssFont = function (variant) {\n        return this.cssFontMap[variant] || ['serif', false, false];\n    };\n    FontData.prototype.getFamily = function (family) {\n        return (this.cssFamilyPrefix ? this.cssFamilyPrefix + ', ' + family : family);\n    };\n    FontData.prototype.getRemappedChar = function (name, c) {\n        var map = this.remapChars[name] || {};\n        return map[c];\n    };\n    FontData.OPTIONS = {\n        unknownFamily: 'serif'\n    };\n    FontData.JAX = 'common';\n    FontData.NAME = '';\n    FontData.defaultVariants = [\n        ['normal'],\n        ['bold', 'normal'],\n        ['italic', 'normal'],\n        ['bold-italic', 'italic', 'bold'],\n        ['double-struck', 'bold'],\n        ['fraktur', 'normal'],\n        ['bold-fraktur', 'bold', 'fraktur'],\n        ['script', 'italic'],\n        ['bold-script', 'bold-italic', 'script'],\n        ['sans-serif', 'normal'],\n        ['bold-sans-serif', 'bold', 'sans-serif'],\n        ['sans-serif-italic', 'italic', 'sans-serif'],\n        ['sans-serif-bold-italic', 'bold-italic', 'bold-sans-serif'],\n        ['monospace', 'normal']\n    ];\n    FontData.defaultCssFonts = {\n        normal: ['unknown', false, false],\n        bold: ['unknown', false, true],\n        italic: ['unknown', true, false],\n        'bold-italic': ['unknown', true, true],\n        'double-struck': ['unknown', false, true],\n        fraktur: ['unknown', false, false],\n        'bold-fraktur': ['unknown', false, true],\n        script: ['cursive', false, false],\n        'bold-script': ['cursive', false, true],\n        'sans-serif': ['sans-serif', false, false],\n        'bold-sans-serif': ['sans-serif', false, true],\n        'sans-serif-italic': ['sans-serif', true, false],\n        'sans-serif-bold-italic': ['sans-serif', true, true],\n        monospace: ['monospace', false, false]\n    };\n    FontData.defaultCssFamilyPrefix = '';\n    FontData.VariantSmp = {\n        bold: [0x1D400, 0x1D41A, 0x1D6A8, 0x1D6C2, 0x1D7CE],\n        italic: [0x1D434, 0x1D44E, 0x1D6E2, 0x1D6FC],\n        'bold-italic': [0x1D468, 0x1D482, 0x1D71C, 0x1D736],\n        script: [0x1D49C, 0x1D4B6],\n        'bold-script': [0x1D4D0, 0x1D4EA],\n        fraktur: [0x1D504, 0x1D51E],\n        'double-struck': [0x1D538, 0x1D552, , , 0x1D7D8],\n        'bold-fraktur': [0x1D56C, 0x1D586],\n        'sans-serif': [0x1D5A0, 0x1D5BA, , , 0x1D7E2],\n        'bold-sans-serif': [0x1D5D4, 0x1D5EE, 0x1D756, 0x1D770, 0x1D7EC],\n        'sans-serif-italic': [0x1D608, 0x1D622],\n        'sans-serif-bold-italic': [0x1D63C, 0x1D656, 0x1D790, 0x1D7AA],\n        'monospace': [0x1D670, 0x1D68A, , , 0x1D7F6]\n    };\n    FontData.SmpRanges = [\n        [0, 0x41, 0x5A],\n        [1, 0x61, 0x7A],\n        [2, 0x391, 0x3A9],\n        [3, 0x3B1, 0x3C9],\n        [4, 0x30, 0x39]\n    ];\n    FontData.SmpRemap = {\n        0x1D455: 0x210E,\n        0x1D49D: 0x212C,\n        0x1D4A0: 0x2130,\n        0x1D4A1: 0x2131,\n        0x1D4A3: 0x210B,\n        0x1D4A4: 0x2110,\n        0x1D4A7: 0x2112,\n        0x1D4A8: 0x2133,\n        0x1D4AD: 0x211B,\n        0x1D4BA: 0x212F,\n        0x1D4BC: 0x210A,\n        0x1D4C4: 0x2134,\n        0x1D506: 0x212D,\n        0x1D50B: 0x210C,\n        0x1D50C: 0x2111,\n        0x1D515: 0x211C,\n        0x1D51D: 0x2128,\n        0x1D53A: 0x2102,\n        0x1D53F: 0x210D,\n        0x1D545: 0x2115,\n        0x1D547: 0x2119,\n        0x1D548: 0x211A,\n        0x1D549: 0x211D,\n        0x1D551: 0x2124,\n    };\n    FontData.SmpRemapGreekU = {\n        0x2207: 0x19,\n        0x03F4: 0x11\n    };\n    FontData.SmpRemapGreekL = {\n        0x3D1: 0x1B,\n        0x3D5: 0x1D,\n        0x3D6: 0x1F,\n        0x3F0: 0x1C,\n        0x3F1: 0x1E,\n        0x3F5: 0x1A,\n        0x2202: 0x19\n    };\n    FontData.defaultAccentMap = {\n        0x0300: '\\u02CB',\n        0x0301: '\\u02CA',\n        0x0302: '\\u02C6',\n        0x0303: '\\u02DC',\n        0x0304: '\\u02C9',\n        0x0306: '\\u02D8',\n        0x0307: '\\u02D9',\n        0x0308: '\\u00A8',\n        0x030A: '\\u02DA',\n        0x030C: '\\u02C7',\n        0x2192: '\\u20D7',\n        0x2032: '\\'',\n        0x2033: '\\'\\'',\n        0x2034: '\\'\\'\\'',\n        0x2035: '`',\n        0x2036: '``',\n        0x2037: '```',\n        0x2057: '\\'\\'\\'\\'',\n        0x20D0: '\\u21BC',\n        0x20D1: '\\u21C0',\n        0x20D6: '\\u2190',\n        0x20E1: '\\u2194',\n        0x20F0: '*',\n        0x20DB: '...',\n        0x20DC: '....',\n        0x20EC: '\\u21C1',\n        0x20ED: '\\u21BD',\n        0x20EE: '\\u2190',\n        0x20EF: '\\u2192'\n    };\n    FontData.defaultMoMap = {\n        0x002D: '\\u2212'\n    };\n    FontData.defaultMnMap = {\n        0x002D: '\\u2212'\n    };\n    FontData.defaultParams = {\n        x_height: .442,\n        quad: 1,\n        num1: .676,\n        num2: .394,\n        num3: .444,\n        denom1: .686,\n        denom2: .345,\n        sup1: .413,\n        sup2: .363,\n        sup3: .289,\n        sub1: .15,\n        sub2: .247,\n        sup_drop: .386,\n        sub_drop: .05,\n        delim1: 2.39,\n        delim2: 1.0,\n        axis_height: .25,\n        rule_thickness: .06,\n        big_op_spacing1: .111,\n        big_op_spacing2: .167,\n        big_op_spacing3: .2,\n        big_op_spacing4: .6,\n        big_op_spacing5: .1,\n        surd_height: .075,\n        scriptspace: .05,\n        nulldelimiterspace: .12,\n        delimiterfactor: 901,\n        delimitershortfall: .3,\n        min_rule_thickness: 1.25,\n        separation_factor: 1.75,\n        extra_ic: .033\n    };\n    FontData.defaultDelimiters = {};\n    FontData.defaultChars = {};\n    FontData.defaultSizeVariants = [];\n    FontData.defaultStretchVariants = [];\n    return FontData;\n}());\nexports.FontData = FontData;\n//# sourceMappingURL=FontData.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CommonTeXFontMixin = void 0;\nfunction CommonTeXFontMixin(Base) {\n    var _a;\n    return _a = (function (_super) {\n            __extends(class_1, _super);\n            function class_1() {\n                return _super !== null && _super.apply(this, arguments) || this;\n            }\n            class_1.prototype.getDelimiterData = function (n) {\n                return this.getChar('-smallop', n) || this.getChar('-size4', n);\n            };\n            return class_1;\n        }(Base)),\n        _a.NAME = 'TeX',\n        _a.defaultVariants = __spreadArray(__spreadArray([], __read(Base.defaultVariants), false), [\n            ['-smallop', 'normal'],\n            ['-largeop', 'normal'],\n            ['-size3', 'normal'],\n            ['-size4', 'normal'],\n            ['-tex-calligraphic', 'italic'],\n            ['-tex-bold-calligraphic', 'bold-italic'],\n            ['-tex-oldstyle', 'normal'],\n            ['-tex-bold-oldstyle', 'bold'],\n            ['-tex-mathit', 'italic'],\n            ['-tex-variant', 'normal']\n        ], false),\n        _a.defaultCssFonts = __assign(__assign({}, Base.defaultCssFonts), { '-smallop': ['serif', false, false], '-largeop': ['serif', false, false], '-size3': ['serif', false, false], '-size4': ['serif', false, false], '-tex-calligraphic': ['cursive', true, false], '-tex-bold-calligraphic': ['cursive', true, true], '-tex-oldstyle': ['serif', false, false], '-tex-bold-oldstyle': ['serif', false, true], '-tex-mathit': ['serif', true, false] }),\n        _a.defaultSizeVariants = ['normal', '-smallop', '-largeop', '-size3', '-size4', '-tex-variant'],\n        _a.defaultStretchVariants = ['-size4'],\n        _a;\n}\nexports.CommonTeXFontMixin = CommonTeXFontMixin;\n//# sourceMappingURL=tex.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.boldItalic = void 0;\nexports.boldItalic = {\n    0x2F: [.711, .21, .894],\n    0x131: [.452, .008, .394, { sk: .0319 }],\n    0x237: [.451, .201, .439, { sk: .0958 }],\n    0x2044: [.711, .21, .894],\n    0x2206: [.711, 0, .958, { sk: .192 }],\n    0x29F8: [.711, .21, .894],\n};\n//# sourceMappingURL=bold-italic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bold = void 0;\nexports.bold = {\n    0x21: [.705, 0, .35],\n    0x22: [.694, -0.329, .603],\n    0x23: [.694, .193, .958],\n    0x24: [.75, .056, .575],\n    0x25: [.75, .056, .958],\n    0x26: [.705, .011, .894],\n    0x27: [.694, -0.329, .319],\n    0x28: [.75, .249, .447],\n    0x29: [.75, .249, .447],\n    0x2A: [.75, -0.306, .575],\n    0x2B: [.633, .131, .894],\n    0x2C: [.171, .194, .319],\n    0x2D: [.278, -0.166, .383],\n    0x2E: [.171, 0, .319],\n    0x2F: [.75, .25, .575],\n    0x3A: [.444, 0, .319],\n    0x3B: [.444, .194, .319],\n    0x3C: [.587, .085, .894],\n    0x3D: [.393, -0.109, .894],\n    0x3E: [.587, .085, .894],\n    0x3F: [.7, 0, .543],\n    0x40: [.699, .006, .894],\n    0x5B: [.75, .25, .319],\n    0x5C: [.75, .25, .575],\n    0x5D: [.75, .25, .319],\n    0x5E: [.694, -0.52, .575],\n    0x5F: [-0.01, .061, .575],\n    0x60: [.706, -0.503, .575],\n    0x7B: [.75, .25, .575],\n    0x7C: [.75, .249, .319],\n    0x7D: [.75, .25, .575],\n    0x7E: [.344, -0.202, .575],\n    0xA8: [.695, -0.535, .575],\n    0xAC: [.371, -0.061, .767],\n    0xAF: [.607, -0.54, .575],\n    0xB0: [.702, -0.536, .575],\n    0xB1: [.728, .035, .894],\n    0xB4: [.706, -0.503, .575],\n    0xB7: [.336, -0.166, .319],\n    0xD7: [.53, .028, .894],\n    0xF7: [.597, .096, .894],\n    0x131: [.442, 0, .278, { sk: .0278 }],\n    0x237: [.442, .205, .306, { sk: .0833 }],\n    0x2B9: [.563, -0.033, .344],\n    0x2C6: [.694, -0.52, .575],\n    0x2C7: [.66, -0.515, .575],\n    0x2C9: [.607, -0.54, .575],\n    0x2CA: [.706, -0.503, .575],\n    0x2CB: [.706, -0.503, .575],\n    0x2D8: [.694, -0.5, .575],\n    0x2D9: [.695, -0.525, .575],\n    0x2DA: [.702, -0.536, .575],\n    0x2DC: [.694, -0.552, .575],\n    0x300: [.706, -0.503, 0],\n    0x301: [.706, -0.503, 0],\n    0x302: [.694, -0.52, 0],\n    0x303: [.694, -0.552, 0],\n    0x304: [.607, -0.54, 0],\n    0x306: [.694, -0.5, 0],\n    0x307: [.695, -0.525, 0],\n    0x308: [.695, -0.535, 0],\n    0x30A: [.702, -0.536, 0],\n    0x30B: [.714, -0.511, 0],\n    0x30C: [.66, -0.515, 0],\n    0x338: [.711, .21, 0],\n    0x2002: [0, 0, .5],\n    0x2003: [0, 0, .999],\n    0x2004: [0, 0, .333],\n    0x2005: [0, 0, .25],\n    0x2006: [0, 0, .167],\n    0x2009: [0, 0, .167],\n    0x200A: [0, 0, .083],\n    0x2013: [.3, -0.249, .575],\n    0x2014: [.3, -0.249, 1.15],\n    0x2015: [.3, -0.249, 1.15],\n    0x2016: [.75, .248, .575],\n    0x2017: [-0.01, .061, .575],\n    0x2018: [.694, -0.329, .319],\n    0x2019: [.694, -0.329, .319],\n    0x201C: [.694, -0.329, .603],\n    0x201D: [.694, -0.329, .603],\n    0x2020: [.702, .211, .511],\n    0x2021: [.702, .202, .511],\n    0x2022: [.474, -0.028, .575],\n    0x2026: [.171, 0, 1.295],\n    0x2032: [.563, -0.033, .344],\n    0x2033: [.563, 0, .688],\n    0x2034: [.563, 0, 1.032],\n    0x203E: [.607, -0.54, .575],\n    0x2044: [.75, .25, .575],\n    0x2057: [.563, 0, 1.376],\n    0x20D7: [.723, -0.513, .575],\n    0x210F: [.694, .008, .668, { sk: -0.0319 }],\n    0x2113: [.702, .019, .474, { sk: .128 }],\n    0x2118: [.461, .21, .74],\n    0x2135: [.694, 0, .703],\n    0x2190: [.518, .017, 1.15],\n    0x2191: [.694, .193, .575],\n    0x2192: [.518, .017, 1.15],\n    0x2193: [.694, .194, .575],\n    0x2194: [.518, .017, 1.15],\n    0x2195: [.767, .267, .575],\n    0x2196: [.724, .194, 1.15],\n    0x2197: [.724, .193, 1.15],\n    0x2198: [.694, .224, 1.15],\n    0x2199: [.694, .224, 1.15],\n    0x219A: [.711, .21, 1.15],\n    0x219B: [.711, .21, 1.15],\n    0x21A6: [.518, .017, 1.15],\n    0x21A9: [.518, .017, 1.282],\n    0x21AA: [.518, .017, 1.282],\n    0x21AE: [.711, .21, 1.15],\n    0x21BC: [.518, -0.22, 1.15],\n    0x21BD: [.281, .017, 1.15],\n    0x21C0: [.518, -0.22, 1.15],\n    0x21C1: [.281, .017, 1.15],\n    0x21CC: [.718, .017, 1.15],\n    0x21CD: [.711, .21, 1.15],\n    0x21CE: [.711, .21, 1.15],\n    0x21CF: [.711, .21, 1.15],\n    0x21D0: [.547, .046, 1.15],\n    0x21D1: [.694, .193, .703],\n    0x21D2: [.547, .046, 1.15],\n    0x21D3: [.694, .194, .703],\n    0x21D4: [.547, .046, 1.15],\n    0x21D5: [.767, .267, .703],\n    0x2200: [.694, .016, .639],\n    0x2203: [.694, 0, .639],\n    0x2204: [.711, .21, .639],\n    0x2205: [.767, .073, .575],\n    0x2206: [.698, 0, .958],\n    0x2208: [.587, .086, .767],\n    0x2209: [.711, .21, .767],\n    0x220B: [.587, .086, .767],\n    0x220C: [.711, .21, .767],\n    0x2212: [.281, -0.221, .894],\n    0x2213: [.537, .227, .894],\n    0x2215: [.75, .25, .575],\n    0x2216: [.75, .25, .575],\n    0x2217: [.472, -0.028, .575],\n    0x2218: [.474, -0.028, .575],\n    0x2219: [.474, -0.028, .575],\n    0x221A: [.82, .18, .958, { ic: .03 }],\n    0x221D: [.451, .008, .894],\n    0x221E: [.452, .008, 1.15],\n    0x2220: [.714, 0, .722],\n    0x2223: [.75, .249, .319],\n    0x2224: [.75, .249, .319],\n    0x2225: [.75, .248, .575],\n    0x2226: [.75, .248, .575],\n    0x2227: [.604, .017, .767],\n    0x2228: [.604, .016, .767],\n    0x2229: [.603, .016, .767],\n    0x222A: [.604, .016, .767],\n    0x222B: [.711, .211, .569, { ic: .063 }],\n    0x223C: [.391, -0.109, .894],\n    0x2240: [.583, .082, .319],\n    0x2241: [.711, .21, .894],\n    0x2243: [.502, 0, .894],\n    0x2244: [.711, .21, .894],\n    0x2245: [.638, .027, .894],\n    0x2247: [.711, .21, .894],\n    0x2248: [.524, -0.032, .894],\n    0x2249: [.711, .21, .894],\n    0x224D: [.533, .032, .894],\n    0x2250: [.721, -0.109, .894],\n    0x2260: [.711, .21, .894],\n    0x2261: [.505, 0, .894],\n    0x2262: [.711, .21, .894],\n    0x2264: [.697, .199, .894],\n    0x2265: [.697, .199, .894],\n    0x226A: [.617, .116, 1.15],\n    0x226B: [.618, .116, 1.15],\n    0x226D: [.711, .21, .894],\n    0x226E: [.711, .21, .894],\n    0x226F: [.711, .21, .894],\n    0x2270: [.711, .21, .894],\n    0x2271: [.711, .21, .894],\n    0x227A: [.585, .086, .894],\n    0x227B: [.586, .086, .894],\n    0x2280: [.711, .21, .894],\n    0x2281: [.711, .21, .894],\n    0x2282: [.587, .085, .894],\n    0x2283: [.587, .086, .894],\n    0x2284: [.711, .21, .894],\n    0x2285: [.711, .21, .894],\n    0x2286: [.697, .199, .894],\n    0x2287: [.697, .199, .894],\n    0x2288: [.711, .21, .894],\n    0x2289: [.711, .21, .894],\n    0x228E: [.604, .016, .767],\n    0x2291: [.697, .199, .894],\n    0x2292: [.697, .199, .894],\n    0x2293: [.604, 0, .767],\n    0x2294: [.604, 0, .767],\n    0x2295: [.632, .132, .894],\n    0x2296: [.632, .132, .894],\n    0x2297: [.632, .132, .894],\n    0x2298: [.632, .132, .894],\n    0x2299: [.632, .132, .894],\n    0x22A2: [.693, 0, .703],\n    0x22A3: [.693, 0, .703],\n    0x22A4: [.694, 0, .894],\n    0x22A5: [.693, 0, .894],\n    0x22A8: [.75, .249, .974],\n    0x22AC: [.711, .21, .703],\n    0x22AD: [.75, .249, .974],\n    0x22C4: [.523, .021, .575],\n    0x22C5: [.336, -0.166, .319],\n    0x22C6: [.502, 0, .575],\n    0x22C8: [.54, .039, 1],\n    0x22E2: [.711, .21, .894],\n    0x22E3: [.711, .21, .894],\n    0x22EE: [.951, .029, .319],\n    0x22EF: [.336, -0.166, 1.295],\n    0x22F1: [.871, -0.101, 1.323],\n    0x2308: [.75, .248, .511],\n    0x2309: [.75, .248, .511],\n    0x230A: [.749, .248, .511],\n    0x230B: [.749, .248, .511],\n    0x2322: [.405, -0.108, 1.15],\n    0x2323: [.392, -0.126, 1.15],\n    0x2329: [.75, .249, .447],\n    0x232A: [.75, .249, .447],\n    0x25B3: [.711, 0, 1.022],\n    0x25B5: [.711, 0, 1.022],\n    0x25B9: [.54, .039, .575],\n    0x25BD: [.5, .21, 1.022],\n    0x25BF: [.5, .21, 1.022],\n    0x25C3: [.539, .038, .575],\n    0x25EF: [.711, .211, 1.15],\n    0x2660: [.719, .129, .894],\n    0x2661: [.711, .024, .894],\n    0x2662: [.719, .154, .894],\n    0x2663: [.719, .129, .894],\n    0x266D: [.75, .017, .447],\n    0x266E: [.741, .223, .447],\n    0x266F: [.724, .224, .447],\n    0x2758: [.75, .249, .319],\n    0x27E8: [.75, .249, .447],\n    0x27E9: [.75, .249, .447],\n    0x27F5: [.518, .017, 1.805],\n    0x27F6: [.518, .017, 1.833],\n    0x27F7: [.518, .017, 2.126],\n    0x27F8: [.547, .046, 1.868],\n    0x27F9: [.547, .046, 1.87],\n    0x27FA: [.547, .046, 2.126],\n    0x27FC: [.518, .017, 1.833],\n    0x29F8: [.711, .21, .894],\n    0x2A2F: [.53, .028, .894],\n    0x2A3F: [.686, 0, .9],\n    0x2AAF: [.696, .199, .894],\n    0x2AB0: [.697, .199, .894],\n    0x3008: [.75, .249, .447],\n    0x3009: [.75, .249, .447],\n};\n//# sourceMappingURL=bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.delimiters = exports.VSIZES = exports.HDW3 = exports.HDW2 = exports.HDW1 = void 0;\nvar FontData_js_1 = require(\"../../FontData.js\");\nexports.HDW1 = [.75, .25, .875];\nexports.HDW2 = [.85, .349, .667];\nexports.HDW3 = [.583, .082, .5];\nexports.VSIZES = [1, 1.2, 1.8, 2.4, 3];\nvar DELIM2F = { c: 0x2F, dir: FontData_js_1.V, sizes: exports.VSIZES };\nvar DELIMAF = { c: 0xAF, dir: FontData_js_1.H, sizes: [.5], stretch: [0, 0xAF], HDW: [.59, -0.544, .5] };\nvar DELIM2C6 = { c: 0x2C6, dir: FontData_js_1.H, sizes: [.5, .556, 1, 1.444, 1.889] };\nvar DELIM2DC = { c: 0x2DC, dir: FontData_js_1.H, sizes: [.5, .556, 1, 1.444, 1.889] };\nvar DELIM2013 = { c: 0x2013, dir: FontData_js_1.H, sizes: [.5], stretch: [0, 0x2013], HDW: [.285, -0.248, .5] };\nvar DELIM2190 = { c: 0x2190, dir: FontData_js_1.H, sizes: [1], stretch: [0x2190, 0x2212], HDW: exports.HDW3 };\nvar DELIM2192 = { c: 0x2192, dir: FontData_js_1.H, sizes: [1], stretch: [0, 0x2212, 0x2192], HDW: exports.HDW3 };\nvar DELIM2194 = { c: 0x2194, dir: FontData_js_1.H, sizes: [1], stretch: [0x2190, 0x2212, 0x2192], HDW: exports.HDW3 };\nvar DELIM21A4 = { c: 0x21A4, dir: FontData_js_1.H, stretch: [0x2190, 0x2212, 0x2223], HDW: exports.HDW3, min: 1.278 };\nvar DELIM21A6 = { c: 0x21A6, dir: FontData_js_1.H, sizes: [1], stretch: [0x2223, 0x2212, 0x2192], HDW: exports.HDW3 };\nvar DELIM21D0 = { c: 0x21D0, dir: FontData_js_1.H, sizes: [1], stretch: [0x21D0, 0x3D], HDW: exports.HDW3 };\nvar DELIM21D2 = { c: 0x21D2, dir: FontData_js_1.H, sizes: [1], stretch: [0, 0x3D, 0x21D2], HDW: exports.HDW3 };\nvar DELIM21D4 = { c: 0x21D4, dir: FontData_js_1.H, sizes: [1], stretch: [0x21D0, 0x3D, 0x21D2], HDW: exports.HDW3 };\nvar DELIM2212 = { c: 0x2212, dir: FontData_js_1.H, sizes: [.778], stretch: [0, 0x2212], HDW: exports.HDW3 };\nvar DELIM2223 = { c: 0x2223, dir: FontData_js_1.V, sizes: [1], stretch: [0, 0x2223], HDW: [.627, .015, .333] };\nvar DELIM23DC = { c: 0x23DC, dir: FontData_js_1.H, sizes: [.778, 1], schar: [0x2322, 0x2322], variants: [5, 0],\n    stretch: [0xE150, 0xE154, 0xE151], HDW: [.32, .2, .5] };\nvar DELIM23DD = { c: 0x23DD, dir: FontData_js_1.H, sizes: [.778, 1], schar: [0x2323, 0x2323], variants: [5, 0],\n    stretch: [0xE152, 0xE154, 0xE153], HDW: [.32, .2, .5] };\nvar DELIM23DE = { c: 0x23DE, dir: FontData_js_1.H, stretch: [0xE150, 0xE154, 0xE151, 0xE155], HDW: [.32, .2, .5], min: 1.8 };\nvar DELIM23DF = { c: 0x23DF, dir: FontData_js_1.H, stretch: [0xE152, 0xE154, 0xE153, 0xE156], HDW: [.32, .2, .5], min: 1.8 };\nvar DELIM27E8 = { c: 0x27E8, dir: FontData_js_1.V, sizes: exports.VSIZES };\nvar DELIM27E9 = { c: 0x27E9, dir: FontData_js_1.V, sizes: exports.VSIZES };\nvar DELIM2906 = { c: 0x2906, dir: FontData_js_1.H, stretch: [0x21D0, 0x3D, 0x2223], HDW: exports.HDW3, min: 1.278 };\nvar DELIM2907 = { c: 0x2907, dir: FontData_js_1.H, stretch: [0x22A8, 0x3D, 0x21D2], HDW: exports.HDW3, min: 1.278 };\nexports.delimiters = {\n    0x28: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0x239B, 0x239C, 0x239D], HDW: [.85, .349, .875] },\n    0x29: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0x239E, 0x239F, 0x23A0], HDW: [.85, .349, .875] },\n    0x2D: DELIM2212,\n    0x2F: DELIM2F,\n    0x3D: { dir: FontData_js_1.H, sizes: [.778], stretch: [0, 0x3D], HDW: exports.HDW3 },\n    0x5B: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0x23A1, 0x23A2, 0x23A3], HDW: exports.HDW2 },\n    0x5C: { dir: FontData_js_1.V, sizes: exports.VSIZES },\n    0x5D: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0x23A4, 0x23A5, 0x23A6], HDW: exports.HDW2 },\n    0x5E: DELIM2C6,\n    0x5F: DELIM2013,\n    0x7B: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0x23A7, 0x23AA, 0x23A9, 0x23A8], HDW: [.85, .349, .889] },\n    0x7C: { dir: FontData_js_1.V, sizes: [1], stretch: [0, 0x2223], HDW: [.75, .25, .333] },\n    0x7D: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0x23AB, 0x23AA, 0x23AD, 0x23AC], HDW: [.85, .349, .889] },\n    0x7E: DELIM2DC,\n    0xAF: DELIMAF,\n    0x2C6: DELIM2C6,\n    0x2C9: DELIMAF,\n    0x2DC: DELIM2DC,\n    0x302: DELIM2C6,\n    0x303: DELIM2DC,\n    0x332: DELIM2013,\n    0x2013: DELIM2013,\n    0x2014: DELIM2013,\n    0x2015: DELIM2013,\n    0x2016: { dir: FontData_js_1.V, sizes: [.602, 1], schar: [0, 0x2225], variants: [1, 0], stretch: [0, 0x2225], HDW: [.602, 0, .556] },\n    0x2017: DELIM2013,\n    0x203E: DELIMAF,\n    0x20D7: DELIM2192,\n    0x2190: DELIM2190,\n    0x2191: { dir: FontData_js_1.V, sizes: [.888], stretch: [0x2191, 0x23D0], HDW: [.6, 0, .667] },\n    0x2192: DELIM2192,\n    0x2193: { dir: FontData_js_1.V, sizes: [.888], stretch: [0, 0x23D0, 0x2193], HDW: [.6, 0, .667] },\n    0x2194: DELIM2194,\n    0x2195: { dir: FontData_js_1.V, sizes: [1.044], stretch: [0x2191, 0x23D0, 0x2193], HDW: exports.HDW1 },\n    0x219E: { dir: FontData_js_1.H, sizes: [1], stretch: [0x219E, 0x2212], HDW: exports.HDW3 },\n    0x21A0: { dir: FontData_js_1.H, sizes: [1], stretch: [0, 0x2212, 0x21A0], HDW: exports.HDW3 },\n    0x21A4: DELIM21A4,\n    0x21A5: { dir: FontData_js_1.V, stretch: [0x2191, 0x23D0, 0x22A5], HDW: exports.HDW1, min: 1.555 },\n    0x21A6: DELIM21A6,\n    0x21A7: { dir: FontData_js_1.V, stretch: [0x22A4, 0x23D0, 0x2193], HDW: exports.HDW1, min: 1.555 },\n    0x21B0: { dir: FontData_js_1.V, sizes: [.722], stretch: [0x21B0, 0x23D0], HDW: exports.HDW1 },\n    0x21B1: { dir: FontData_js_1.V, sizes: [.722], stretch: [0x21B1, 0x23D0], HDW: exports.HDW1 },\n    0x21BC: { dir: FontData_js_1.H, sizes: [1], stretch: [0x21BC, 0x2212], HDW: exports.HDW3 },\n    0x21BD: { dir: FontData_js_1.H, sizes: [1], stretch: [0x21BD, 0x2212], HDW: exports.HDW3 },\n    0x21BE: { dir: FontData_js_1.V, sizes: [.888], stretch: [0x21BE, 0x23D0], HDW: exports.HDW1 },\n    0x21BF: { dir: FontData_js_1.V, sizes: [.888], stretch: [0x21BF, 0x23D0], HDW: exports.HDW1 },\n    0x21C0: { dir: FontData_js_1.H, sizes: [1], stretch: [0, 0x2212, 0x21C0], HDW: exports.HDW3 },\n    0x21C1: { dir: FontData_js_1.H, sizes: [1], stretch: [0, 0x2212, 0x21C1], HDW: exports.HDW3 },\n    0x21C2: { dir: FontData_js_1.V, sizes: [.888], stretch: [0, 0x23D0, 0x21C2], HDW: exports.HDW1 },\n    0x21C3: { dir: FontData_js_1.V, sizes: [.888], stretch: [0, 0x23D0, 0x21C3], HDW: exports.HDW1 },\n    0x21D0: DELIM21D0,\n    0x21D1: { dir: FontData_js_1.V, sizes: [.888], stretch: [0x21D1, 0x2016], HDW: [.599, 0, .778] },\n    0x21D2: DELIM21D2,\n    0x21D3: { dir: FontData_js_1.V, sizes: [.888], stretch: [0, 0x2016, 0x21D3], HDW: [.6, 0, .778] },\n    0x21D4: DELIM21D4,\n    0x21D5: { dir: FontData_js_1.V, sizes: [1.044], stretch: [0x21D1, 0x2016, 0x21D3], HDW: [.75, .25, .778] },\n    0x21DA: { dir: FontData_js_1.H, sizes: [1], stretch: [0x21DA, 0x2261], HDW: [.464, -0.036, .5] },\n    0x21DB: { dir: FontData_js_1.H, sizes: [1], stretch: [0, 0x2261, 0x21DB], HDW: [.464, -0.036, .5] },\n    0x2212: DELIM2212,\n    0x2215: DELIM2F,\n    0x221A: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0xE001, 0xE000, 0x23B7], fullExt: [.65, 2.3], HDW: [.85, .35, 1.056] },\n    0x2223: DELIM2223,\n    0x2225: { dir: FontData_js_1.V, sizes: [1], stretch: [0, 0x2225], HDW: [.627, .015, .556] },\n    0x2308: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0x23A1, 0x23A2], HDW: exports.HDW2 },\n    0x2309: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0x23A4, 0x23A5], HDW: exports.HDW2 },\n    0x230A: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0, 0x23A2, 0x23A3], HDW: exports.HDW2 },\n    0x230B: { dir: FontData_js_1.V, sizes: exports.VSIZES, stretch: [0, 0x23A5, 0x23A6], HDW: exports.HDW2 },\n    0x2312: DELIM23DC,\n    0x2322: DELIM23DC,\n    0x2323: DELIM23DD,\n    0x2329: DELIM27E8,\n    0x232A: DELIM27E9,\n    0x23AA: { dir: FontData_js_1.V, sizes: [.32], stretch: [0x23AA, 0x23AA, 0x23AA], HDW: [.29, .015, .889] },\n    0x23AF: DELIM2013,\n    0x23B0: { dir: FontData_js_1.V, sizes: [.989], stretch: [0x23A7, 0x23AA, 0x23AD], HDW: [.75, .25, .889] },\n    0x23B1: { dir: FontData_js_1.V, sizes: [.989], stretch: [0x23AB, 0x23AA, 0x23A9], HDW: [.75, .25, .889] },\n    0x23B4: { dir: FontData_js_1.H, stretch: [0x250C, 0x2212, 0x2510], HDW: exports.HDW3, min: 1 },\n    0x23B5: { dir: FontData_js_1.H, stretch: [0x2514, 0x2212, 0x2518], HDW: exports.HDW3, min: 1 },\n    0x23D0: { dir: FontData_js_1.V, sizes: [.602, 1], schar: [0, 0x2223], variants: [1, 0], stretch: [0, 0x2223], HDW: [.602, 0, .333] },\n    0x23DC: DELIM23DC,\n    0x23DD: DELIM23DD,\n    0x23DE: DELIM23DE,\n    0x23DF: DELIM23DF,\n    0x23E0: { dir: FontData_js_1.H, stretch: [0x2CA, 0x2C9, 0x2CB], HDW: [.59, -0.544, .5], min: 1 },\n    0x23E1: { dir: FontData_js_1.H, stretch: [0x2CB, 0x2C9, 0x2CA], HDW: [.59, -0.544, .5], min: 1 },\n    0x2500: DELIM2013,\n    0x2758: DELIM2223,\n    0x27E8: DELIM27E8,\n    0x27E9: DELIM27E9,\n    0x27EE: { dir: FontData_js_1.V, sizes: [.989], stretch: [0x23A7, 0x23AA, 0x23A9], HDW: [.75, .25, .889] },\n    0x27EF: { dir: FontData_js_1.V, sizes: [.989], stretch: [0x23AB, 0x23AA, 0x23AD], HDW: [.75, .25, .889] },\n    0x27F5: DELIM2190,\n    0x27F6: DELIM2192,\n    0x27F7: DELIM2194,\n    0x27F8: DELIM21D0,\n    0x27F9: DELIM21D2,\n    0x27FA: DELIM21D4,\n    0x27FB: DELIM21A4,\n    0x27FC: DELIM21A6,\n    0x27FD: DELIM2906,\n    0x27FE: DELIM2907,\n    0x2906: DELIM2906,\n    0x2907: DELIM2907,\n    0x294E: { dir: FontData_js_1.H, stretch: [0x21BC, 0x2212, 0x21C0], HDW: exports.HDW3, min: 2 },\n    0x294F: { dir: FontData_js_1.V, stretch: [0x21BE, 0x23D0, 0x21C2], HDW: exports.HDW1, min: 1.776 },\n    0x2950: { dir: FontData_js_1.H, stretch: [0x21BD, 0x2212, 0x21C1], HDW: exports.HDW3, min: 2 },\n    0x2951: { dir: FontData_js_1.V, stretch: [0x21BF, 0x23D0, 0x21C3], HDW: exports.HDW1, min: .5 },\n    0x295A: { dir: FontData_js_1.H, stretch: [0x21BC, 0x2212, 0x2223], HDW: exports.HDW3, min: 1.278 },\n    0x295B: { dir: FontData_js_1.H, stretch: [0x2223, 0x2212, 0x21C0], HDW: exports.HDW3, min: 1.278 },\n    0x295C: { dir: FontData_js_1.V, stretch: [0x21BE, 0x23D0, 0x22A5], HDW: exports.HDW1, min: 1.556 },\n    0x295D: { dir: FontData_js_1.V, stretch: [0x22A4, 0x23D0, 0x21C2], HDW: exports.HDW1, min: 1.556 },\n    0x295E: { dir: FontData_js_1.H, stretch: [0x21BD, 0x2212, 0x2223], HDW: exports.HDW3, min: 1.278 },\n    0x295F: { dir: FontData_js_1.H, stretch: [0x2223, 0x2212, 0x21C1], HDW: exports.HDW3, min: 1.278 },\n    0x2960: { dir: FontData_js_1.V, stretch: [0x21BF, 0x23D0, 0x22A5], HDW: exports.HDW1, min: 1.776 },\n    0x2961: { dir: FontData_js_1.V, stretch: [0x22A4, 0x23D0, 0x21C3], HDW: exports.HDW1, min: 1.776 },\n    0x3008: DELIM27E8,\n    0x3009: DELIM27E9,\n    0xFE37: DELIM23DE,\n    0xFE38: DELIM23DF,\n};\n//# sourceMappingURL=delimiters.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.doubleStruck = void 0;\nexports.doubleStruck = {};\n//# sourceMappingURL=double-struck.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.frakturBold = void 0;\nexports.frakturBold = {\n    0x21: [.689, .012, .349],\n    0x22: [.695, -0.432, .254],\n    0x26: [.696, .016, .871],\n    0x27: [.695, -0.436, .25],\n    0x28: [.737, .186, .459],\n    0x29: [.735, .187, .459],\n    0x2A: [.692, -0.449, .328],\n    0x2B: [.598, .082, .893],\n    0x2C: [.107, .191, .328],\n    0x2D: [.275, -0.236, .893],\n    0x2E: [.102, .015, .328],\n    0x2F: [.721, .182, .593],\n    0x30: [.501, .012, .593],\n    0x31: [.489, 0, .593],\n    0x32: [.491, 0, .593],\n    0x33: [.487, .193, .593],\n    0x34: [.495, .196, .593],\n    0x35: [.481, .19, .593],\n    0x36: [.704, .012, .593],\n    0x37: [.479, .197, .593],\n    0x38: [.714, .005, .593],\n    0x39: [.487, .195, .593],\n    0x3A: [.457, .012, .255],\n    0x3B: [.458, .19, .255],\n    0x3D: [.343, -0.168, .582],\n    0x3F: [.697, .014, .428],\n    0x5B: [.74, .13, .257],\n    0x5D: [.738, .132, .257],\n    0x5E: [.734, -0.452, .59],\n    0x2018: [.708, -0.411, .254],\n    0x2019: [.692, -0.394, .254],\n    0x2044: [.721, .182, .593],\n    0xE301: [.63, .027, .587],\n    0xE302: [.693, .212, .394, { ic: .014 }],\n    0xE303: [.681, .219, .387],\n    0xE304: [.473, .212, .593],\n    0xE305: [.684, .027, .393],\n    0xE308: [.679, .22, .981],\n    0xE309: [.717, .137, .727],\n};\n//# sourceMappingURL=fraktur-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.fraktur = void 0;\nexports.fraktur = {\n    0x21: [.689, .012, .296],\n    0x22: [.695, -0.432, .215],\n    0x26: [.698, .011, .738],\n    0x27: [.695, -0.436, .212],\n    0x28: [.737, .186, .389],\n    0x29: [.735, .187, .389],\n    0x2A: [.692, -0.449, .278],\n    0x2B: [.598, .082, .756],\n    0x2C: [.107, .191, .278],\n    0x2D: [.275, -0.236, .756],\n    0x2E: [.102, .015, .278],\n    0x2F: [.721, .182, .502],\n    0x30: [.492, .013, .502],\n    0x31: [.468, 0, .502],\n    0x32: [.474, 0, .502],\n    0x33: [.473, .182, .502],\n    0x34: [.476, .191, .502],\n    0x35: [.458, .184, .502],\n    0x36: [.7, .013, .502],\n    0x37: [.468, .181, .502],\n    0x38: [.705, .01, .502],\n    0x39: [.469, .182, .502],\n    0x3A: [.457, .012, .216],\n    0x3B: [.458, .189, .216],\n    0x3D: [.368, -0.132, .756],\n    0x3F: [.693, .011, .362],\n    0x5B: [.74, .13, .278],\n    0x5D: [.738, .131, .278],\n    0x5E: [.734, -0.452, .5],\n    0x2018: [.708, -0.41, .215],\n    0x2019: [.692, -0.395, .215],\n    0x2044: [.721, .182, .502],\n    0xE300: [.683, .032, .497],\n    0xE301: [.616, .03, .498],\n    0xE302: [.68, .215, .333],\n    0xE303: [.679, .224, .329],\n    0xE304: [.471, .214, .503],\n    0xE305: [.686, .02, .333],\n    0xE306: [.577, .021, .334, { ic: .013 }],\n    0xE307: [.475, .022, .501, { ic: .013 }],\n};\n//# sourceMappingURL=fraktur.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.italic = void 0;\nexports.italic = {\n    0x21: [.716, 0, .307, { ic: .073 }],\n    0x22: [.694, -0.379, .514, { ic: .024 }],\n    0x23: [.694, .194, .818, { ic: .01 }],\n    0x25: [.75, .056, .818, { ic: .029 }],\n    0x26: [.716, .022, .767, { ic: .035 }],\n    0x27: [.694, -0.379, .307, { ic: .07 }],\n    0x28: [.75, .25, .409, { ic: .108 }],\n    0x29: [.75, .25, .409],\n    0x2A: [.75, -0.32, .511, { ic: .073 }],\n    0x2B: [.557, .057, .767],\n    0x2C: [.121, .194, .307],\n    0x2D: [.251, -0.18, .358],\n    0x2E: [.121, 0, .307],\n    0x2F: [.716, .215, .778],\n    0x30: [.665, .021, .511, { ic: .051 }],\n    0x31: [.666, 0, .511],\n    0x32: [.666, .022, .511, { ic: .04 }],\n    0x33: [.666, .022, .511, { ic: .051 }],\n    0x34: [.666, .194, .511],\n    0x35: [.666, .022, .511, { ic: .056 }],\n    0x36: [.665, .022, .511, { ic: .054 }],\n    0x37: [.666, .022, .511, { ic: .123 }],\n    0x38: [.666, .021, .511, { ic: .042 }],\n    0x39: [.666, .022, .511, { ic: .042 }],\n    0x3A: [.431, 0, .307],\n    0x3B: [.431, .194, .307],\n    0x3D: [.367, -0.133, .767],\n    0x3F: [.716, 0, .511, { ic: .04 }],\n    0x40: [.705, .011, .767, { ic: .022 }],\n    0x5B: [.75, .25, .307, { ic: .139 }],\n    0x5D: [.75, .25, .307, { ic: .052 }],\n    0x5E: [.694, -0.527, .511, { ic: .017 }],\n    0x5F: [-0.025, .062, .511, { ic: .043 }],\n    0x7E: [.318, -0.208, .511, { ic: .06 }],\n    0x131: [.441, .01, .307, { ic: .033 }],\n    0x237: [.442, .204, .332],\n    0x300: [.697, -0.5, 0],\n    0x301: [.697, -0.5, 0, { ic: .039 }],\n    0x302: [.694, -0.527, 0, { ic: .017 }],\n    0x303: [.668, -0.558, 0, { ic: .06 }],\n    0x304: [.589, -0.544, 0, { ic: .054 }],\n    0x306: [.694, -0.515, 0, { ic: .062 }],\n    0x307: [.669, -0.548, 0],\n    0x308: [.669, -0.554, 0, { ic: .045 }],\n    0x30A: [.716, -0.542, 0],\n    0x30B: [.697, -0.503, 0, { ic: .065 }],\n    0x30C: [.638, -0.502, 0, { ic: .029 }],\n    0x3DD: [.605, .085, .778],\n    0x2013: [.285, -0.248, .511, { ic: .043 }],\n    0x2014: [.285, -0.248, 1.022, { ic: .016 }],\n    0x2015: [.285, -0.248, 1.022, { ic: .016 }],\n    0x2017: [-0.025, .062, .511, { ic: .043 }],\n    0x2018: [.694, -0.379, .307, { ic: .055 }],\n    0x2019: [.694, -0.379, .307, { ic: .07 }],\n    0x201C: [.694, -0.379, .514, { ic: .092 }],\n    0x201D: [.694, -0.379, .514, { ic: .024 }],\n    0x2044: [.716, .215, .778],\n    0x210F: [.695, .013, .54, { ic: .022 }],\n    0x2206: [.716, 0, .833, { sk: .167 }],\n    0x29F8: [.716, .215, .778],\n};\n//# sourceMappingURL=italic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.largeop = void 0;\nexports.largeop = {\n    0x28: [1.15, .649, .597],\n    0x29: [1.15, .649, .597],\n    0x2F: [1.15, .649, .811],\n    0x5B: [1.15, .649, .472],\n    0x5C: [1.15, .649, .811],\n    0x5D: [1.15, .649, .472],\n    0x7B: [1.15, .649, .667],\n    0x7D: [1.15, .649, .667],\n    0x2C6: [.772, -0.565, 1],\n    0x2DC: [.75, -0.611, 1],\n    0x302: [.772, -0.565, 0],\n    0x303: [.75, -0.611, 0],\n    0x2016: [.602, 0, .778],\n    0x2044: [1.15, .649, .811],\n    0x2191: [.6, 0, .667],\n    0x2193: [.6, 0, .667],\n    0x21D1: [.599, 0, .778],\n    0x21D3: [.6, 0, .778],\n    0x220F: [.95, .45, 1.278],\n    0x2210: [.95, .45, 1.278],\n    0x2211: [.95, .45, 1.444],\n    0x221A: [1.15, .65, 1, { ic: .02 }],\n    0x2223: [.627, .015, .333],\n    0x2225: [.627, .015, .556],\n    0x222B: [1.36, .862, .556, { ic: .388 }],\n    0x222C: [1.36, .862, 1.084, { ic: .388 }],\n    0x222D: [1.36, .862, 1.592, { ic: .388 }],\n    0x222E: [1.36, .862, .556, { ic: .388 }],\n    0x22C0: [.95, .45, 1.111],\n    0x22C1: [.95, .45, 1.111],\n    0x22C2: [.949, .45, 1.111],\n    0x22C3: [.95, .449, 1.111],\n    0x2308: [1.15, .649, .528],\n    0x2309: [1.15, .649, .528],\n    0x230A: [1.15, .649, .528],\n    0x230B: [1.15, .649, .528],\n    0x2329: [1.15, .649, .611],\n    0x232A: [1.15, .649, .611],\n    0x23D0: [.602, 0, .667],\n    0x2758: [.627, .015, .333],\n    0x27E8: [1.15, .649, .611],\n    0x27E9: [1.15, .649, .611],\n    0x2A00: [.949, .449, 1.511],\n    0x2A01: [.949, .449, 1.511],\n    0x2A02: [.949, .449, 1.511],\n    0x2A04: [.95, .449, 1.111],\n    0x2A06: [.95, .45, 1.111],\n    0x2A0C: [1.36, .862, 2.168, { ic: .388 }],\n    0x3008: [1.15, .649, .611],\n    0x3009: [1.15, .649, .611],\n};\n//# sourceMappingURL=largeop.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.monospace = void 0;\nexports.monospace = {\n    0x20: [0, 0, .525],\n    0x21: [.622, 0, .525],\n    0x22: [.623, -0.333, .525],\n    0x23: [.611, 0, .525],\n    0x24: [.694, .082, .525],\n    0x25: [.694, .083, .525],\n    0x26: [.622, .011, .525],\n    0x27: [.611, -0.287, .525],\n    0x28: [.694, .082, .525],\n    0x29: [.694, .082, .525],\n    0x2A: [.52, -0.09, .525],\n    0x2B: [.531, -0.081, .525],\n    0x2C: [.14, .139, .525],\n    0x2D: [.341, -0.271, .525],\n    0x2E: [.14, 0, .525],\n    0x2F: [.694, .083, .525],\n    0x3A: [.431, 0, .525],\n    0x3B: [.431, .139, .525],\n    0x3C: [.557, -0.055, .525],\n    0x3D: [.417, -0.195, .525],\n    0x3E: [.557, -0.055, .525],\n    0x3F: [.617, 0, .525],\n    0x40: [.617, .006, .525],\n    0x5B: [.694, .082, .525],\n    0x5C: [.694, .083, .525],\n    0x5D: [.694, .082, .525],\n    0x5E: [.611, -0.46, .525],\n    0x5F: [-0.025, .095, .525],\n    0x60: [.681, -0.357, .525],\n    0x7B: [.694, .083, .525],\n    0x7C: [.694, .082, .525],\n    0x7D: [.694, .083, .525],\n    0x7E: [.611, -0.466, .525],\n    0x7F: [.612, -0.519, .525],\n    0xA0: [0, 0, .525],\n    0x131: [.431, 0, .525],\n    0x237: [.431, .228, .525],\n    0x2B9: [.623, -0.334, .525],\n    0x300: [.611, -0.485, 0],\n    0x301: [.611, -0.485, 0],\n    0x302: [.611, -0.46, 0],\n    0x303: [.611, -0.466, 0],\n    0x304: [.577, -0.5, 0],\n    0x306: [.611, -0.504, 0],\n    0x308: [.612, -0.519, 0],\n    0x30A: [.619, -0.499, 0],\n    0x30C: [.577, -0.449, 0],\n    0x391: [.623, 0, .525],\n    0x392: [.611, 0, .525],\n    0x393: [.611, 0, .525],\n    0x394: [.623, 0, .525],\n    0x395: [.611, 0, .525],\n    0x396: [.611, 0, .525],\n    0x397: [.611, 0, .525],\n    0x398: [.621, .01, .525],\n    0x399: [.611, 0, .525],\n    0x39A: [.611, 0, .525],\n    0x39B: [.623, 0, .525],\n    0x39C: [.611, 0, .525],\n    0x39D: [.611, 0, .525],\n    0x39E: [.611, 0, .525],\n    0x39F: [.621, .01, .525],\n    0x3A0: [.611, 0, .525],\n    0x3A1: [.611, 0, .525],\n    0x3A3: [.611, 0, .525],\n    0x3A4: [.611, 0, .525],\n    0x3A5: [.622, 0, .525],\n    0x3A6: [.611, 0, .525],\n    0x3A7: [.611, 0, .525],\n    0x3A8: [.611, 0, .525],\n    0x3A9: [.622, 0, .525],\n    0x2017: [-0.025, .095, .525],\n    0x2032: [.623, -0.334, .525],\n    0x2033: [.623, 0, 1.05],\n    0x2034: [.623, 0, 1.575],\n    0x2044: [.694, .083, .525],\n    0x2057: [.623, 0, 2.1],\n    0x2206: [.623, 0, .525],\n};\n//# sourceMappingURL=monospace.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.normal = void 0;\nexports.normal = {\n    0x20: [0, 0, .25],\n    0x21: [.716, 0, .278],\n    0x22: [.694, -0.379, .5],\n    0x23: [.694, .194, .833],\n    0x24: [.75, .056, .5],\n    0x25: [.75, .056, .833],\n    0x26: [.716, .022, .778],\n    0x27: [.694, -0.379, .278],\n    0x28: [.75, .25, .389],\n    0x29: [.75, .25, .389],\n    0x2A: [.75, -0.32, .5],\n    0x2B: [.583, .082, .778],\n    0x2C: [.121, .194, .278],\n    0x2D: [.252, -0.179, .333],\n    0x2E: [.12, 0, .278],\n    0x2F: [.75, .25, .5],\n    0x30: [.666, .022, .5],\n    0x31: [.666, 0, .5],\n    0x32: [.666, 0, .5],\n    0x33: [.665, .022, .5],\n    0x34: [.677, 0, .5],\n    0x35: [.666, .022, .5],\n    0x36: [.666, .022, .5],\n    0x37: [.676, .022, .5],\n    0x38: [.666, .022, .5],\n    0x39: [.666, .022, .5],\n    0x3A: [.43, 0, .278],\n    0x3B: [.43, .194, .278],\n    0x3C: [.54, .04, .778],\n    0x3D: [.583, .082, .778],\n    0x3E: [.54, .04, .778],\n    0x3F: [.705, 0, .472],\n    0x40: [.705, .011, .778],\n    0x41: [.716, 0, .75],\n    0x42: [.683, 0, .708],\n    0x43: [.705, .021, .722],\n    0x44: [.683, 0, .764],\n    0x45: [.68, 0, .681],\n    0x46: [.68, 0, .653],\n    0x47: [.705, .022, .785],\n    0x48: [.683, 0, .75],\n    0x49: [.683, 0, .361],\n    0x4A: [.683, .022, .514],\n    0x4B: [.683, 0, .778],\n    0x4C: [.683, 0, .625],\n    0x4D: [.683, 0, .917],\n    0x4E: [.683, 0, .75],\n    0x4F: [.705, .022, .778],\n    0x50: [.683, 0, .681],\n    0x51: [.705, .193, .778],\n    0x52: [.683, .022, .736],\n    0x53: [.705, .022, .556],\n    0x54: [.677, 0, .722],\n    0x55: [.683, .022, .75],\n    0x56: [.683, .022, .75],\n    0x57: [.683, .022, 1.028],\n    0x58: [.683, 0, .75],\n    0x59: [.683, 0, .75],\n    0x5A: [.683, 0, .611],\n    0x5B: [.75, .25, .278],\n    0x5C: [.75, .25, .5],\n    0x5D: [.75, .25, .278],\n    0x5E: [.694, -0.531, .5],\n    0x5F: [-0.025, .062, .5],\n    0x60: [.699, -0.505, .5],\n    0x61: [.448, .011, .5],\n    0x62: [.694, .011, .556],\n    0x63: [.448, .011, .444],\n    0x64: [.694, .011, .556],\n    0x65: [.448, .011, .444],\n    0x66: [.705, 0, .306, { ic: .066 }],\n    0x67: [.453, .206, .5],\n    0x68: [.694, 0, .556],\n    0x69: [.669, 0, .278],\n    0x6A: [.669, .205, .306],\n    0x6B: [.694, 0, .528],\n    0x6C: [.694, 0, .278],\n    0x6D: [.442, 0, .833],\n    0x6E: [.442, 0, .556],\n    0x6F: [.448, .01, .5],\n    0x70: [.442, .194, .556],\n    0x71: [.442, .194, .528],\n    0x72: [.442, 0, .392],\n    0x73: [.448, .011, .394],\n    0x74: [.615, .01, .389],\n    0x75: [.442, .011, .556],\n    0x76: [.431, .011, .528],\n    0x77: [.431, .011, .722],\n    0x78: [.431, 0, .528],\n    0x79: [.431, .204, .528],\n    0x7A: [.431, 0, .444],\n    0x7B: [.75, .25, .5],\n    0x7C: [.75, .249, .278],\n    0x7D: [.75, .25, .5],\n    0x7E: [.318, -0.215, .5],\n    0xA0: [0, 0, .25],\n    0xA3: [.714, .011, .769],\n    0xA5: [.683, 0, .75],\n    0xA8: [.669, -0.554, .5],\n    0xAC: [.356, -0.089, .667],\n    0xAE: [.709, .175, .947],\n    0xAF: [.59, -0.544, .5],\n    0xB0: [.715, -0.542, .5],\n    0xB1: [.666, 0, .778],\n    0xB4: [.699, -0.505, .5],\n    0xB7: [.31, -0.19, .278],\n    0xD7: [.491, -0.009, .778],\n    0xF0: [.749, .021, .556],\n    0xF7: [.537, .036, .778],\n    0x131: [.442, 0, .278, { sk: .0278 }],\n    0x237: [.442, .205, .306, { sk: .0833 }],\n    0x2B9: [.56, -0.043, .275],\n    0x2C6: [.694, -0.531, .5],\n    0x2C7: [.644, -0.513, .5],\n    0x2C9: [.59, -0.544, .5],\n    0x2CA: [.699, -0.505, .5],\n    0x2CB: [.699, -0.505, .5],\n    0x2D8: [.694, -0.515, .5],\n    0x2D9: [.669, -0.549, .5],\n    0x2DA: [.715, -0.542, .5],\n    0x2DC: [.668, -0.565, .5],\n    0x300: [.699, -0.505, 0],\n    0x301: [.699, -0.505, 0],\n    0x302: [.694, -0.531, 0],\n    0x303: [.668, -0.565, 0],\n    0x304: [.59, -0.544, 0],\n    0x306: [.694, -0.515, 0],\n    0x307: [.669, -0.549, 0],\n    0x308: [.669, -0.554, 0],\n    0x30A: [.715, -0.542, 0],\n    0x30B: [.701, -0.51, 0],\n    0x30C: [.644, -0.513, 0],\n    0x338: [.716, .215, 0],\n    0x391: [.716, 0, .75],\n    0x392: [.683, 0, .708],\n    0x393: [.68, 0, .625],\n    0x394: [.716, 0, .833],\n    0x395: [.68, 0, .681],\n    0x396: [.683, 0, .611],\n    0x397: [.683, 0, .75],\n    0x398: [.705, .022, .778],\n    0x399: [.683, 0, .361],\n    0x39A: [.683, 0, .778],\n    0x39B: [.716, 0, .694],\n    0x39C: [.683, 0, .917],\n    0x39D: [.683, 0, .75],\n    0x39E: [.677, 0, .667],\n    0x39F: [.705, .022, .778],\n    0x3A0: [.68, 0, .75],\n    0x3A1: [.683, 0, .681],\n    0x3A3: [.683, 0, .722],\n    0x3A4: [.677, 0, .722],\n    0x3A5: [.705, 0, .778],\n    0x3A6: [.683, 0, .722],\n    0x3A7: [.683, 0, .75],\n    0x3A8: [.683, 0, .778],\n    0x3A9: [.704, 0, .722],\n    0x2000: [0, 0, .5],\n    0x2001: [0, 0, 1],\n    0x2002: [0, 0, .5],\n    0x2003: [0, 0, 1],\n    0x2004: [0, 0, .333],\n    0x2005: [0, 0, .25],\n    0x2006: [0, 0, .167],\n    0x2009: [0, 0, .167],\n    0x200A: [0, 0, .1],\n    0x200B: [0, 0, 0],\n    0x200C: [0, 0, 0],\n    0x2013: [.285, -0.248, .5],\n    0x2014: [.285, -0.248, 1],\n    0x2015: [.285, -0.248, 1],\n    0x2016: [.75, .25, .5],\n    0x2017: [-0.025, .062, .5],\n    0x2018: [.694, -0.379, .278],\n    0x2019: [.694, -0.379, .278],\n    0x201C: [.694, -0.379, .5],\n    0x201D: [.694, -0.379, .5],\n    0x2020: [.705, .216, .444],\n    0x2021: [.705, .205, .444],\n    0x2022: [.444, -0.055, .5],\n    0x2026: [.12, 0, 1.172],\n    0x2032: [.56, -0.043, .275],\n    0x2033: [.56, 0, .55],\n    0x2034: [.56, 0, .825],\n    0x2035: [.56, -0.043, .275],\n    0x2036: [.56, 0, .55],\n    0x2037: [.56, 0, .825],\n    0x203E: [.59, -0.544, .5],\n    0x2044: [.75, .25, .5],\n    0x2057: [.56, 0, 1.1],\n    0x2060: [0, 0, 0],\n    0x2061: [0, 0, 0],\n    0x2062: [0, 0, 0],\n    0x2063: [0, 0, 0],\n    0x2064: [0, 0, 0],\n    0x20D7: [.714, -0.516, .5],\n    0x2102: [.702, .019, .722],\n    0x210B: [.717, .036, .969, { ic: .272, sk: .333 }],\n    0x210C: [.666, .133, .72],\n    0x210D: [.683, 0, .778],\n    0x210E: [.694, .011, .576, { sk: -0.0278 }],\n    0x210F: [.695, .013, .54, { ic: .022 }],\n    0x2110: [.717, .017, .809, { ic: .137, sk: .333 }],\n    0x2111: [.686, .026, .554],\n    0x2112: [.717, .017, .874, { ic: .161, sk: .306 }],\n    0x2113: [.705, .02, .417, { sk: .111 }],\n    0x2115: [.683, .02, .722],\n    0x2118: [.453, .216, .636, { sk: .111 }],\n    0x2119: [.683, 0, .611],\n    0x211A: [.701, .181, .778],\n    0x211B: [.717, .017, .85, { ic: .037, sk: .194 }],\n    0x211C: [.686, .026, .828],\n    0x211D: [.683, 0, .722],\n    0x2124: [.683, 0, .667],\n    0x2126: [.704, 0, .722],\n    0x2127: [.684, .022, .722],\n    0x2128: [.729, .139, .602],\n    0x212C: [.708, .028, .908, { ic: .02, sk: .194 }],\n    0x212D: [.685, .024, .613],\n    0x2130: [.707, .008, .562, { ic: .156, sk: .139 }],\n    0x2131: [.735, .036, .895, { ic: .095, sk: .222 }],\n    0x2132: [.695, 0, .556],\n    0x2133: [.721, .05, 1.08, { ic: .136, sk: .444 }],\n    0x2135: [.694, 0, .611],\n    0x2136: [.763, .021, .667, { ic: .02 }],\n    0x2137: [.764, .043, .444],\n    0x2138: [.764, .043, .667],\n    0x2141: [.705, .023, .639],\n    0x2190: [.511, .011, 1],\n    0x2191: [.694, .193, .5],\n    0x2192: [.511, .011, 1],\n    0x2193: [.694, .194, .5],\n    0x2194: [.511, .011, 1],\n    0x2195: [.772, .272, .5],\n    0x2196: [.72, .195, 1],\n    0x2197: [.72, .195, 1],\n    0x2198: [.695, .22, 1],\n    0x2199: [.695, .22, 1],\n    0x219A: [.437, -0.06, 1],\n    0x219B: [.437, -0.06, 1],\n    0x219E: [.417, -0.083, 1],\n    0x21A0: [.417, -0.083, 1],\n    0x21A2: [.417, -0.083, 1.111],\n    0x21A3: [.417, -0.083, 1.111],\n    0x21A6: [.511, .011, 1],\n    0x21A9: [.511, .011, 1.126],\n    0x21AA: [.511, .011, 1.126],\n    0x21AB: [.575, .041, 1],\n    0x21AC: [.575, .041, 1],\n    0x21AD: [.417, -0.083, 1.389],\n    0x21AE: [.437, -0.06, 1],\n    0x21B0: [.722, 0, .5],\n    0x21B1: [.722, 0, .5],\n    0x21B6: [.461, 0, 1],\n    0x21B7: [.46, 0, 1],\n    0x21BA: [.65, .083, .778],\n    0x21BB: [.65, .083, .778],\n    0x21BC: [.511, -0.23, 1],\n    0x21BD: [.27, .011, 1],\n    0x21BE: [.694, .194, .417],\n    0x21BF: [.694, .194, .417],\n    0x21C0: [.511, -0.23, 1],\n    0x21C1: [.27, .011, 1],\n    0x21C2: [.694, .194, .417],\n    0x21C3: [.694, .194, .417],\n    0x21C4: [.667, 0, 1],\n    0x21C6: [.667, 0, 1],\n    0x21C7: [.583, .083, 1],\n    0x21C8: [.694, .193, .833],\n    0x21C9: [.583, .083, 1],\n    0x21CA: [.694, .194, .833],\n    0x21CB: [.514, .014, 1],\n    0x21CC: [.671, .011, 1],\n    0x21CD: [.534, .035, 1],\n    0x21CE: [.534, .037, 1],\n    0x21CF: [.534, .035, 1],\n    0x21D0: [.525, .024, 1],\n    0x21D1: [.694, .194, .611],\n    0x21D2: [.525, .024, 1],\n    0x21D3: [.694, .194, .611],\n    0x21D4: [.526, .025, 1],\n    0x21D5: [.772, .272, .611],\n    0x21DA: [.611, .111, 1],\n    0x21DB: [.611, .111, 1],\n    0x21DD: [.417, -0.083, 1],\n    0x21E0: [.437, -0.064, 1.334],\n    0x21E2: [.437, -0.064, 1.334],\n    0x2200: [.694, .022, .556],\n    0x2201: [.846, .021, .5],\n    0x2202: [.715, .022, .531, { ic: .035, sk: .0833 }],\n    0x2203: [.694, 0, .556],\n    0x2204: [.716, .215, .556],\n    0x2205: [.772, .078, .5],\n    0x2206: [.716, 0, .833],\n    0x2207: [.683, .033, .833],\n    0x2208: [.54, .04, .667],\n    0x2209: [.716, .215, .667],\n    0x220B: [.54, .04, .667],\n    0x220C: [.716, .215, .667],\n    0x220D: [.44, 0, .429, { ic: .027 }],\n    0x220F: [.75, .25, .944],\n    0x2210: [.75, .25, .944],\n    0x2211: [.75, .25, 1.056],\n    0x2212: [.583, .082, .778],\n    0x2213: [.5, .166, .778],\n    0x2214: [.766, .093, .778],\n    0x2215: [.75, .25, .5],\n    0x2216: [.75, .25, .5],\n    0x2217: [.465, -0.035, .5],\n    0x2218: [.444, -0.055, .5],\n    0x2219: [.444, -0.055, .5],\n    0x221A: [.8, .2, .833, { ic: .02 }],\n    0x221D: [.442, .011, .778],\n    0x221E: [.442, .011, 1],\n    0x2220: [.694, 0, .722],\n    0x2221: [.714, .02, .722],\n    0x2222: [.551, .051, .722],\n    0x2223: [.75, .249, .278],\n    0x2224: [.75, .252, .278, { ic: .019 }],\n    0x2225: [.75, .25, .5],\n    0x2226: [.75, .25, .5, { ic: .018 }],\n    0x2227: [.598, .022, .667],\n    0x2228: [.598, .022, .667],\n    0x2229: [.598, .022, .667],\n    0x222A: [.598, .022, .667],\n    0x222B: [.716, .216, .417, { ic: .055 }],\n    0x222C: [.805, .306, .819, { ic: .138 }],\n    0x222D: [.805, .306, 1.166, { ic: .138 }],\n    0x222E: [.805, .306, .472, { ic: .138 }],\n    0x2234: [.471, .082, .667],\n    0x2235: [.471, .082, .667],\n    0x223C: [.367, -0.133, .778],\n    0x223D: [.367, -0.133, .778],\n    0x2240: [.583, .083, .278],\n    0x2241: [.467, -0.032, .778],\n    0x2242: [.463, -0.034, .778],\n    0x2243: [.464, -0.036, .778],\n    0x2244: [.716, .215, .778],\n    0x2245: [.589, -0.022, .778],\n    0x2247: [.652, .155, .778],\n    0x2248: [.483, -0.055, .778],\n    0x2249: [.716, .215, .778],\n    0x224A: [.579, .039, .778],\n    0x224D: [.484, -0.016, .778],\n    0x224E: [.492, -0.008, .778],\n    0x224F: [.492, -0.133, .778],\n    0x2250: [.67, -0.133, .778],\n    0x2251: [.609, .108, .778],\n    0x2252: [.601, .101, .778],\n    0x2253: [.601, .102, .778],\n    0x2256: [.367, -0.133, .778],\n    0x2257: [.721, -0.133, .778],\n    0x225C: [.859, -0.133, .778],\n    0x2260: [.716, .215, .778],\n    0x2261: [.464, -0.036, .778],\n    0x2262: [.716, .215, .778],\n    0x2264: [.636, .138, .778],\n    0x2265: [.636, .138, .778],\n    0x2266: [.753, .175, .778],\n    0x2267: [.753, .175, .778],\n    0x2268: [.752, .286, .778],\n    0x2269: [.752, .286, .778],\n    0x226A: [.568, .067, 1],\n    0x226B: [.567, .067, 1],\n    0x226C: [.75, .25, .5],\n    0x226D: [.716, .215, .778],\n    0x226E: [.708, .209, .778],\n    0x226F: [.708, .209, .778],\n    0x2270: [.801, .303, .778],\n    0x2271: [.801, .303, .778],\n    0x2272: [.732, .228, .778],\n    0x2273: [.732, .228, .778],\n    0x2274: [.732, .228, .778],\n    0x2275: [.732, .228, .778],\n    0x2276: [.681, .253, .778],\n    0x2277: [.681, .253, .778],\n    0x2278: [.716, .253, .778],\n    0x2279: [.716, .253, .778],\n    0x227A: [.539, .041, .778],\n    0x227B: [.539, .041, .778],\n    0x227C: [.58, .153, .778],\n    0x227D: [.58, .154, .778],\n    0x227E: [.732, .228, .778],\n    0x227F: [.732, .228, .778],\n    0x2280: [.705, .208, .778],\n    0x2281: [.705, .208, .778],\n    0x2282: [.54, .04, .778],\n    0x2283: [.54, .04, .778],\n    0x2284: [.716, .215, .778],\n    0x2285: [.716, .215, .778],\n    0x2286: [.636, .138, .778],\n    0x2287: [.636, .138, .778],\n    0x2288: [.801, .303, .778],\n    0x2289: [.801, .303, .778],\n    0x228A: [.635, .241, .778],\n    0x228B: [.635, .241, .778],\n    0x228E: [.598, .022, .667],\n    0x228F: [.539, .041, .778],\n    0x2290: [.539, .041, .778],\n    0x2291: [.636, .138, .778],\n    0x2292: [.636, .138, .778],\n    0x2293: [.598, 0, .667],\n    0x2294: [.598, 0, .667],\n    0x2295: [.583, .083, .778],\n    0x2296: [.583, .083, .778],\n    0x2297: [.583, .083, .778],\n    0x2298: [.583, .083, .778],\n    0x2299: [.583, .083, .778],\n    0x229A: [.582, .082, .778],\n    0x229B: [.582, .082, .778],\n    0x229D: [.582, .082, .778],\n    0x229E: [.689, 0, .778],\n    0x229F: [.689, 0, .778],\n    0x22A0: [.689, 0, .778],\n    0x22A1: [.689, 0, .778],\n    0x22A2: [.694, 0, .611],\n    0x22A3: [.694, 0, .611],\n    0x22A4: [.668, 0, .778],\n    0x22A5: [.668, 0, .778],\n    0x22A8: [.75, .249, .867],\n    0x22A9: [.694, 0, .722],\n    0x22AA: [.694, 0, .889],\n    0x22AC: [.695, 0, .611],\n    0x22AD: [.695, 0, .611],\n    0x22AE: [.695, 0, .722],\n    0x22AF: [.695, 0, .722],\n    0x22B2: [.539, .041, .778],\n    0x22B3: [.539, .041, .778],\n    0x22B4: [.636, .138, .778],\n    0x22B5: [.636, .138, .778],\n    0x22B8: [.408, -0.092, 1.111],\n    0x22BA: [.431, .212, .556],\n    0x22BB: [.716, 0, .611],\n    0x22BC: [.716, 0, .611],\n    0x22C0: [.75, .249, .833],\n    0x22C1: [.75, .249, .833],\n    0x22C2: [.75, .249, .833],\n    0x22C3: [.75, .249, .833],\n    0x22C4: [.488, -0.012, .5],\n    0x22C5: [.31, -0.19, .278],\n    0x22C6: [.486, -0.016, .5],\n    0x22C7: [.545, .044, .778],\n    0x22C8: [.505, .005, .9],\n    0x22C9: [.492, -0.008, .778],\n    0x22CA: [.492, -0.008, .778],\n    0x22CB: [.694, .022, .778],\n    0x22CC: [.694, .022, .778],\n    0x22CD: [.464, -0.036, .778],\n    0x22CE: [.578, .021, .76],\n    0x22CF: [.578, .022, .76],\n    0x22D0: [.54, .04, .778],\n    0x22D1: [.54, .04, .778],\n    0x22D2: [.598, .022, .667],\n    0x22D3: [.598, .022, .667],\n    0x22D4: [.736, .022, .667],\n    0x22D6: [.541, .041, .778],\n    0x22D7: [.541, .041, .778],\n    0x22D8: [.568, .067, 1.333],\n    0x22D9: [.568, .067, 1.333],\n    0x22DA: [.886, .386, .778],\n    0x22DB: [.886, .386, .778],\n    0x22DE: [.734, 0, .778],\n    0x22DF: [.734, 0, .778],\n    0x22E0: [.801, .303, .778],\n    0x22E1: [.801, .303, .778],\n    0x22E2: [.716, .215, .778],\n    0x22E3: [.716, .215, .778],\n    0x22E6: [.73, .359, .778],\n    0x22E7: [.73, .359, .778],\n    0x22E8: [.73, .359, .778],\n    0x22E9: [.73, .359, .778],\n    0x22EA: [.706, .208, .778],\n    0x22EB: [.706, .208, .778],\n    0x22EC: [.802, .303, .778],\n    0x22ED: [.801, .303, .778],\n    0x22EE: [1.3, .03, .278],\n    0x22EF: [.31, -0.19, 1.172],\n    0x22F1: [1.52, -0.1, 1.282],\n    0x2305: [.716, 0, .611],\n    0x2306: [.813, .097, .611],\n    0x2308: [.75, .25, .444],\n    0x2309: [.75, .25, .444],\n    0x230A: [.75, .25, .444],\n    0x230B: [.75, .25, .444],\n    0x231C: [.694, -0.306, .5],\n    0x231D: [.694, -0.306, .5],\n    0x231E: [.366, .022, .5],\n    0x231F: [.366, .022, .5],\n    0x2322: [.388, -0.122, 1],\n    0x2323: [.378, -0.134, 1],\n    0x2329: [.75, .25, .389],\n    0x232A: [.75, .25, .389],\n    0x23B0: [.744, .244, .412],\n    0x23B1: [.744, .244, .412],\n    0x23D0: [.602, 0, .667],\n    0x24C8: [.709, .175, .902],\n    0x250C: [.694, -0.306, .5],\n    0x2510: [.694, -0.306, .5],\n    0x2514: [.366, .022, .5],\n    0x2518: [.366, .022, .5],\n    0x2571: [.694, .195, .889],\n    0x2572: [.694, .195, .889],\n    0x25A0: [.689, 0, .778],\n    0x25A1: [.689, 0, .778],\n    0x25AA: [.689, 0, .778],\n    0x25B2: [.575, .02, .722],\n    0x25B3: [.716, 0, .889],\n    0x25B4: [.575, .02, .722],\n    0x25B5: [.716, 0, .889],\n    0x25B6: [.539, .041, .778],\n    0x25B8: [.539, .041, .778],\n    0x25B9: [.505, .005, .5],\n    0x25BC: [.576, .019, .722],\n    0x25BD: [.5, .215, .889],\n    0x25BE: [.576, .019, .722],\n    0x25BF: [.5, .215, .889],\n    0x25C0: [.539, .041, .778],\n    0x25C2: [.539, .041, .778],\n    0x25C3: [.505, .005, .5],\n    0x25CA: [.716, .132, .667],\n    0x25EF: [.715, .215, 1],\n    0x25FB: [.689, 0, .778],\n    0x25FC: [.689, 0, .778],\n    0x2605: [.694, .111, .944],\n    0x2660: [.727, .13, .778],\n    0x2661: [.716, .033, .778],\n    0x2662: [.727, .162, .778],\n    0x2663: [.726, .13, .778],\n    0x266D: [.75, .022, .389],\n    0x266E: [.734, .223, .389],\n    0x266F: [.723, .223, .389],\n    0x2713: [.706, .034, .833],\n    0x2720: [.716, .022, .833],\n    0x2758: [.75, .249, .278],\n    0x27E8: [.75, .25, .389],\n    0x27E9: [.75, .25, .389],\n    0x27EE: [.744, .244, .412],\n    0x27EF: [.744, .244, .412],\n    0x27F5: [.511, .011, 1.609],\n    0x27F6: [.511, .011, 1.638],\n    0x27F7: [.511, .011, 1.859],\n    0x27F8: [.525, .024, 1.609],\n    0x27F9: [.525, .024, 1.638],\n    0x27FA: [.525, .024, 1.858],\n    0x27FC: [.511, .011, 1.638],\n    0x29EB: [.716, .132, .667],\n    0x29F8: [.716, .215, .778],\n    0x2A00: [.75, .25, 1.111],\n    0x2A01: [.75, .25, 1.111],\n    0x2A02: [.75, .25, 1.111],\n    0x2A04: [.75, .249, .833],\n    0x2A06: [.75, .249, .833],\n    0x2A0C: [.805, .306, 1.638, { ic: .138 }],\n    0x2A2F: [.491, -0.009, .778],\n    0x2A3F: [.683, 0, .75],\n    0x2A5E: [.813, .097, .611],\n    0x2A7D: [.636, .138, .778],\n    0x2A7E: [.636, .138, .778],\n    0x2A85: [.762, .29, .778],\n    0x2A86: [.762, .29, .778],\n    0x2A87: [.635, .241, .778],\n    0x2A88: [.635, .241, .778],\n    0x2A89: [.761, .387, .778],\n    0x2A8A: [.761, .387, .778],\n    0x2A8B: [1.003, .463, .778],\n    0x2A8C: [1.003, .463, .778],\n    0x2A95: [.636, .138, .778],\n    0x2A96: [.636, .138, .778],\n    0x2AAF: [.636, .138, .778],\n    0x2AB0: [.636, .138, .778],\n    0x2AB5: [.752, .286, .778],\n    0x2AB6: [.752, .286, .778],\n    0x2AB7: [.761, .294, .778],\n    0x2AB8: [.761, .294, .778],\n    0x2AB9: [.761, .337, .778],\n    0x2ABA: [.761, .337, .778],\n    0x2AC5: [.753, .215, .778],\n    0x2AC6: [.753, .215, .778],\n    0x2ACB: [.783, .385, .778],\n    0x2ACC: [.783, .385, .778],\n    0x3008: [.75, .25, .389],\n    0x3009: [.75, .25, .389],\n    0xE006: [.43, .023, .222, { ic: .018 }],\n    0xE007: [.431, .024, .389, { ic: .018 }],\n    0xE008: [.605, .085, .778],\n    0xE009: [.434, .006, .667, { ic: .067 }],\n    0xE00C: [.752, .284, .778],\n    0xE00D: [.752, .284, .778],\n    0xE00E: [.919, .421, .778],\n    0xE00F: [.801, .303, .778],\n    0xE010: [.801, .303, .778],\n    0xE011: [.919, .421, .778],\n    0xE016: [.828, .33, .778],\n    0xE017: [.752, .332, .778],\n    0xE018: [.828, .33, .778],\n    0xE019: [.752, .333, .778],\n    0xE01A: [.634, .255, .778],\n    0xE01B: [.634, .254, .778],\n    0x1D400: [.698, 0, .869],\n    0x1D401: [.686, 0, .818],\n    0x1D402: [.697, .011, .831],\n    0x1D403: [.686, 0, .882],\n    0x1D404: [.68, 0, .756],\n    0x1D405: [.68, 0, .724],\n    0x1D406: [.697, .01, .904],\n    0x1D407: [.686, 0, .9],\n    0x1D408: [.686, 0, .436],\n    0x1D409: [.686, .011, .594],\n    0x1D40A: [.686, 0, .901],\n    0x1D40B: [.686, 0, .692],\n    0x1D40C: [.686, 0, 1.092],\n    0x1D40D: [.686, 0, .9],\n    0x1D40E: [.696, .01, .864],\n    0x1D40F: [.686, 0, .786],\n    0x1D410: [.696, .193, .864],\n    0x1D411: [.686, .011, .862],\n    0x1D412: [.697, .011, .639],\n    0x1D413: [.675, 0, .8],\n    0x1D414: [.686, .011, .885],\n    0x1D415: [.686, .007, .869],\n    0x1D416: [.686, .007, 1.189],\n    0x1D417: [.686, 0, .869],\n    0x1D418: [.686, 0, .869],\n    0x1D419: [.686, 0, .703],\n    0x1D41A: [.453, .006, .559],\n    0x1D41B: [.694, .006, .639],\n    0x1D41C: [.453, .006, .511],\n    0x1D41D: [.694, .006, .639],\n    0x1D41E: [.452, .006, .527],\n    0x1D41F: [.7, 0, .351, { ic: .101 }],\n    0x1D420: [.455, .201, .575],\n    0x1D421: [.694, 0, .639],\n    0x1D422: [.695, 0, .319],\n    0x1D423: [.695, .2, .351],\n    0x1D424: [.694, 0, .607],\n    0x1D425: [.694, 0, .319],\n    0x1D426: [.45, 0, .958],\n    0x1D427: [.45, 0, .639],\n    0x1D428: [.452, .005, .575],\n    0x1D429: [.45, .194, .639],\n    0x1D42A: [.45, .194, .607],\n    0x1D42B: [.45, 0, .474],\n    0x1D42C: [.453, .006, .454],\n    0x1D42D: [.635, .005, .447],\n    0x1D42E: [.45, .006, .639],\n    0x1D42F: [.444, 0, .607],\n    0x1D430: [.444, 0, .831],\n    0x1D431: [.444, 0, .607],\n    0x1D432: [.444, .2, .607],\n    0x1D433: [.444, 0, .511],\n    0x1D434: [.716, 0, .75, { sk: .139 }],\n    0x1D435: [.683, 0, .759, { sk: .0833 }],\n    0x1D436: [.705, .022, .715, { ic: .045, sk: .0833 }],\n    0x1D437: [.683, 0, .828, { sk: .0556 }],\n    0x1D438: [.68, 0, .738, { ic: .026, sk: .0833 }],\n    0x1D439: [.68, 0, .643, { ic: .106, sk: .0833 }],\n    0x1D43A: [.705, .022, .786, { sk: .0833 }],\n    0x1D43B: [.683, 0, .831, { ic: .057, sk: .0556 }],\n    0x1D43C: [.683, 0, .44, { ic: .064, sk: .111 }],\n    0x1D43D: [.683, .022, .555, { ic: .078, sk: .167 }],\n    0x1D43E: [.683, 0, .849, { ic: .04, sk: .0556 }],\n    0x1D43F: [.683, 0, .681, { sk: .0278 }],\n    0x1D440: [.683, 0, .97, { ic: .081, sk: .0833 }],\n    0x1D441: [.683, 0, .803, { ic: .085, sk: .0833 }],\n    0x1D442: [.704, .022, .763, { sk: .0833 }],\n    0x1D443: [.683, 0, .642, { ic: .109, sk: .0833 }],\n    0x1D444: [.704, .194, .791, { sk: .0833 }],\n    0x1D445: [.683, .021, .759, { sk: .0833 }],\n    0x1D446: [.705, .022, .613, { ic: .032, sk: .0833 }],\n    0x1D447: [.677, 0, .584, { ic: .12, sk: .0833 }],\n    0x1D448: [.683, .022, .683, { ic: .084, sk: .0278 }],\n    0x1D449: [.683, .022, .583, { ic: .186 }],\n    0x1D44A: [.683, .022, .944, { ic: .104 }],\n    0x1D44B: [.683, 0, .828, { ic: .024, sk: .0833 }],\n    0x1D44C: [.683, 0, .581, { ic: .182 }],\n    0x1D44D: [.683, 0, .683, { ic: .04, sk: .0833 }],\n    0x1D44E: [.441, .01, .529],\n    0x1D44F: [.694, .011, .429],\n    0x1D450: [.442, .011, .433, { sk: .0556 }],\n    0x1D451: [.694, .01, .52, { sk: .167 }],\n    0x1D452: [.442, .011, .466, { sk: .0556 }],\n    0x1D453: [.705, .205, .49, { ic: .06, sk: .167 }],\n    0x1D454: [.442, .205, .477, { sk: .0278 }],\n    0x1D456: [.661, .011, .345],\n    0x1D457: [.661, .204, .412],\n    0x1D458: [.694, .011, .521],\n    0x1D459: [.694, .011, .298, { sk: .0833 }],\n    0x1D45A: [.442, .011, .878],\n    0x1D45B: [.442, .011, .6],\n    0x1D45C: [.441, .011, .485, { sk: .0556 }],\n    0x1D45D: [.442, .194, .503, { sk: .0833 }],\n    0x1D45E: [.442, .194, .446, { ic: .014, sk: .0833 }],\n    0x1D45F: [.442, .011, .451, { sk: .0556 }],\n    0x1D460: [.442, .01, .469, { sk: .0556 }],\n    0x1D461: [.626, .011, .361, { sk: .0833 }],\n    0x1D462: [.442, .011, .572, { sk: .0278 }],\n    0x1D463: [.443, .011, .485, { sk: .0278 }],\n    0x1D464: [.443, .011, .716, { sk: .0833 }],\n    0x1D465: [.442, .011, .572, { sk: .0278 }],\n    0x1D466: [.442, .205, .49, { sk: .0556 }],\n    0x1D467: [.442, .011, .465, { sk: .0556 }],\n    0x1D468: [.711, 0, .869, { sk: .16 }],\n    0x1D469: [.686, 0, .866, { sk: .0958 }],\n    0x1D46A: [.703, .017, .817, { ic: .038, sk: .0958 }],\n    0x1D46B: [.686, 0, .938, { sk: .0639 }],\n    0x1D46C: [.68, 0, .81, { ic: .015, sk: .0958 }],\n    0x1D46D: [.68, 0, .689, { ic: .12, sk: .0958 }],\n    0x1D46E: [.703, .016, .887, { sk: .0958 }],\n    0x1D46F: [.686, 0, .982, { ic: .045, sk: .0639 }],\n    0x1D470: [.686, 0, .511, { ic: .062, sk: .128 }],\n    0x1D471: [.686, .017, .631, { ic: .063, sk: .192 }],\n    0x1D472: [.686, 0, .971, { ic: .032, sk: .0639 }],\n    0x1D473: [.686, 0, .756, { sk: .0319 }],\n    0x1D474: [.686, 0, 1.142, { ic: .077, sk: .0958 }],\n    0x1D475: [.686, 0, .95, { ic: .077, sk: .0958 }],\n    0x1D476: [.703, .017, .837, { sk: .0958 }],\n    0x1D477: [.686, 0, .723, { ic: .124, sk: .0958 }],\n    0x1D478: [.703, .194, .869, { sk: .0958 }],\n    0x1D479: [.686, .017, .872, { sk: .0958 }],\n    0x1D47A: [.703, .017, .693, { ic: .021, sk: .0958 }],\n    0x1D47B: [.675, 0, .637, { ic: .135, sk: .0958 }],\n    0x1D47C: [.686, .016, .8, { ic: .077, sk: .0319 }],\n    0x1D47D: [.686, .016, .678, { ic: .208 }],\n    0x1D47E: [.686, .017, 1.093, { ic: .114 }],\n    0x1D47F: [.686, 0, .947, { sk: .0958 }],\n    0x1D480: [.686, 0, .675, { ic: .201 }],\n    0x1D481: [.686, 0, .773, { ic: .032, sk: .0958 }],\n    0x1D482: [.452, .008, .633],\n    0x1D483: [.694, .008, .521],\n    0x1D484: [.451, .008, .513, { sk: .0639 }],\n    0x1D485: [.694, .008, .61, { sk: .192 }],\n    0x1D486: [.452, .008, .554, { sk: .0639 }],\n    0x1D487: [.701, .201, .568, { ic: .056, sk: .192 }],\n    0x1D488: [.452, .202, .545, { sk: .0319 }],\n    0x1D489: [.694, .008, .668, { sk: -0.0319 }],\n    0x1D48A: [.694, .008, .405],\n    0x1D48B: [.694, .202, .471],\n    0x1D48C: [.694, .008, .604],\n    0x1D48D: [.694, .008, .348, { sk: .0958 }],\n    0x1D48E: [.452, .008, 1.032],\n    0x1D48F: [.452, .008, .713],\n    0x1D490: [.452, .008, .585, { sk: .0639 }],\n    0x1D491: [.452, .194, .601, { sk: .0958 }],\n    0x1D492: [.452, .194, .542, { sk: .0958 }],\n    0x1D493: [.452, .008, .529, { sk: .0639 }],\n    0x1D494: [.451, .008, .531, { sk: .0639 }],\n    0x1D495: [.643, .007, .415, { sk: .0958 }],\n    0x1D496: [.452, .008, .681, { sk: .0319 }],\n    0x1D497: [.453, .008, .567, { sk: .0319 }],\n    0x1D498: [.453, .008, .831, { sk: .0958 }],\n    0x1D499: [.452, .008, .659, { sk: .0319 }],\n    0x1D49A: [.452, .202, .59, { sk: .0639 }],\n    0x1D49B: [.452, .008, .555, { sk: .0639 }],\n    0x1D49C: [.717, .008, .803, { ic: .213, sk: .389 }],\n    0x1D49E: [.728, .026, .666, { ic: .153, sk: .278 }],\n    0x1D49F: [.708, .031, .774, { ic: .081, sk: .111 }],\n    0x1D4A2: [.717, .037, .61, { ic: .128, sk: .25 }],\n    0x1D4A5: [.717, .314, 1.052, { ic: .081, sk: .417 }],\n    0x1D4A6: [.717, .037, .914, { ic: .29, sk: .361 }],\n    0x1D4A9: [.726, .036, .902, { ic: .306, sk: .389 }],\n    0x1D4AA: [.707, .008, .738, { ic: .067, sk: .167 }],\n    0x1D4AB: [.716, .037, 1.013, { ic: .018, sk: .222 }],\n    0x1D4AC: [.717, .017, .883, { sk: .278 }],\n    0x1D4AE: [.708, .036, .868, { ic: .148, sk: .333 }],\n    0x1D4AF: [.735, .037, .747, { ic: .249, sk: .222 }],\n    0x1D4B0: [.717, .017, .8, { ic: .16, sk: .25 }],\n    0x1D4B1: [.717, .017, .622, { ic: .228, sk: .222 }],\n    0x1D4B2: [.717, .017, .805, { ic: .221, sk: .25 }],\n    0x1D4B3: [.717, .017, .944, { ic: .187, sk: .278 }],\n    0x1D4B4: [.716, .017, .71, { ic: .249, sk: .194 }],\n    0x1D4B5: [.717, .016, .821, { ic: .211, sk: .306 }],\n    0x1D504: [.696, .026, .718],\n    0x1D505: [.691, .027, .884],\n    0x1D507: [.685, .027, .832],\n    0x1D508: [.685, .024, .663],\n    0x1D509: [.686, .153, .611],\n    0x1D50A: [.69, .026, .785],\n    0x1D50D: [.686, .139, .552],\n    0x1D50E: [.68, .027, .668, { ic: .014 }],\n    0x1D50F: [.686, .026, .666],\n    0x1D510: [.692, .027, 1.05],\n    0x1D511: [.686, .025, .832],\n    0x1D512: [.729, .027, .827],\n    0x1D513: [.692, .218, .828],\n    0x1D514: [.729, .069, .827],\n    0x1D516: [.692, .027, .829],\n    0x1D517: [.701, .027, .669],\n    0x1D518: [.697, .027, .646, { ic: .019 }],\n    0x1D519: [.686, .026, .831],\n    0x1D51A: [.686, .027, 1.046],\n    0x1D51B: [.688, .027, .719],\n    0x1D51C: [.686, .218, .833],\n    0x1D51E: [.47, .035, .5],\n    0x1D51F: [.685, .031, .513],\n    0x1D520: [.466, .029, .389],\n    0x1D521: [.609, .033, .499],\n    0x1D522: [.467, .03, .401],\n    0x1D523: [.681, .221, .326],\n    0x1D524: [.47, .209, .504],\n    0x1D525: [.688, .205, .521],\n    0x1D526: [.673, .02, .279],\n    0x1D527: [.672, .208, .281],\n    0x1D528: [.689, .025, .389],\n    0x1D529: [.685, .02, .28],\n    0x1D52A: [.475, .026, .767],\n    0x1D52B: [.475, .022, .527],\n    0x1D52C: [.48, .028, .489],\n    0x1D52D: [.541, .212, .5],\n    0x1D52E: [.479, .219, .489],\n    0x1D52F: [.474, .021, .389],\n    0x1D530: [.478, .029, .443],\n    0x1D531: [.64, .02, .333, { ic: .015 }],\n    0x1D532: [.474, .023, .517],\n    0x1D533: [.53, .028, .512],\n    0x1D534: [.532, .028, .774],\n    0x1D535: [.472, .188, .389],\n    0x1D536: [.528, .218, .499],\n    0x1D537: [.471, .214, .391],\n    0x1D538: [.701, 0, .722],\n    0x1D539: [.683, 0, .667],\n    0x1D53B: [.683, 0, .722],\n    0x1D53C: [.683, 0, .667],\n    0x1D53D: [.683, 0, .611],\n    0x1D53E: [.702, .019, .778],\n    0x1D540: [.683, 0, .389],\n    0x1D541: [.683, .077, .5],\n    0x1D542: [.683, 0, .778],\n    0x1D543: [.683, 0, .667],\n    0x1D544: [.683, 0, .944],\n    0x1D546: [.701, .019, .778],\n    0x1D54A: [.702, .012, .556],\n    0x1D54B: [.683, 0, .667],\n    0x1D54C: [.683, .019, .722],\n    0x1D54D: [.683, .02, .722],\n    0x1D54E: [.683, .019, 1],\n    0x1D54F: [.683, 0, .722],\n    0x1D550: [.683, 0, .722],\n    0x1D56C: [.686, .031, .847],\n    0x1D56D: [.684, .031, 1.044],\n    0x1D56E: [.676, .032, .723],\n    0x1D56F: [.683, .029, .982],\n    0x1D570: [.686, .029, .783],\n    0x1D571: [.684, .146, .722],\n    0x1D572: [.687, .029, .927],\n    0x1D573: [.683, .126, .851],\n    0x1D574: [.681, .025, .655],\n    0x1D575: [.68, .141, .652],\n    0x1D576: [.681, .026, .789, { ic: .017 }],\n    0x1D577: [.683, .028, .786],\n    0x1D578: [.683, .032, 1.239],\n    0x1D579: [.679, .03, .983],\n    0x1D57A: [.726, .03, .976],\n    0x1D57B: [.688, .223, .977],\n    0x1D57C: [.726, .083, .976],\n    0x1D57D: [.688, .028, .978],\n    0x1D57E: [.685, .031, .978],\n    0x1D57F: [.686, .03, .79, { ic: .012 }],\n    0x1D580: [.688, .039, .851, { ic: .02 }],\n    0x1D581: [.685, .029, .982],\n    0x1D582: [.683, .03, 1.235],\n    0x1D583: [.681, .035, .849],\n    0x1D584: [.688, .214, .984],\n    0x1D585: [.677, .148, .711],\n    0x1D586: [.472, .032, .603],\n    0x1D587: [.69, .032, .59],\n    0x1D588: [.473, .026, .464],\n    0x1D589: [.632, .028, .589],\n    0x1D58A: [.471, .027, .472],\n    0x1D58B: [.687, .222, .388],\n    0x1D58C: [.472, .208, .595],\n    0x1D58D: [.687, .207, .615],\n    0x1D58E: [.686, .025, .331],\n    0x1D58F: [.682, .203, .332],\n    0x1D590: [.682, .025, .464],\n    0x1D591: [.681, .024, .337],\n    0x1D592: [.476, .031, .921],\n    0x1D593: [.473, .028, .654],\n    0x1D594: [.482, .034, .609],\n    0x1D595: [.557, .207, .604],\n    0x1D596: [.485, .211, .596],\n    0x1D597: [.472, .026, .46],\n    0x1D598: [.479, .034, .523],\n    0x1D599: [.648, .027, .393, { ic: .014 }],\n    0x1D59A: [.472, .032, .589, { ic: .014 }],\n    0x1D59B: [.546, .027, .604],\n    0x1D59C: [.549, .032, .918],\n    0x1D59D: [.471, .188, .459],\n    0x1D59E: [.557, .221, .589],\n    0x1D59F: [.471, .214, .461],\n    0x1D5A0: [.694, 0, .667],\n    0x1D5A1: [.694, 0, .667],\n    0x1D5A2: [.705, .011, .639],\n    0x1D5A3: [.694, 0, .722],\n    0x1D5A4: [.691, 0, .597],\n    0x1D5A5: [.691, 0, .569],\n    0x1D5A6: [.704, .011, .667],\n    0x1D5A7: [.694, 0, .708],\n    0x1D5A8: [.694, 0, .278],\n    0x1D5A9: [.694, .022, .472],\n    0x1D5AA: [.694, 0, .694],\n    0x1D5AB: [.694, 0, .542],\n    0x1D5AC: [.694, 0, .875],\n    0x1D5AD: [.694, 0, .708],\n    0x1D5AE: [.715, .022, .736],\n    0x1D5AF: [.694, 0, .639],\n    0x1D5B0: [.715, .125, .736],\n    0x1D5B1: [.694, 0, .646],\n    0x1D5B2: [.716, .022, .556],\n    0x1D5B3: [.688, 0, .681],\n    0x1D5B4: [.694, .022, .688],\n    0x1D5B5: [.694, 0, .667],\n    0x1D5B6: [.694, 0, .944],\n    0x1D5B7: [.694, 0, .667],\n    0x1D5B8: [.694, 0, .667],\n    0x1D5B9: [.694, 0, .611],\n    0x1D5BA: [.46, .01, .481],\n    0x1D5BB: [.694, .011, .517],\n    0x1D5BC: [.46, .01, .444],\n    0x1D5BD: [.694, .01, .517],\n    0x1D5BE: [.461, .01, .444],\n    0x1D5BF: [.705, 0, .306, { ic: .041 }],\n    0x1D5C0: [.455, .206, .5],\n    0x1D5C1: [.694, 0, .517],\n    0x1D5C2: [.68, 0, .239],\n    0x1D5C3: [.68, .205, .267],\n    0x1D5C4: [.694, 0, .489],\n    0x1D5C5: [.694, 0, .239],\n    0x1D5C6: [.455, 0, .794],\n    0x1D5C7: [.455, 0, .517],\n    0x1D5C8: [.46, .01, .5],\n    0x1D5C9: [.455, .194, .517],\n    0x1D5CA: [.455, .194, .517],\n    0x1D5CB: [.455, 0, .342],\n    0x1D5CC: [.46, .01, .383],\n    0x1D5CD: [.571, .01, .361],\n    0x1D5CE: [.444, .01, .517],\n    0x1D5CF: [.444, 0, .461],\n    0x1D5D0: [.444, 0, .683],\n    0x1D5D1: [.444, 0, .461],\n    0x1D5D2: [.444, .204, .461],\n    0x1D5D3: [.444, 0, .435],\n    0x1D5D4: [.694, 0, .733],\n    0x1D5D5: [.694, 0, .733],\n    0x1D5D6: [.704, .011, .703],\n    0x1D5D7: [.694, 0, .794],\n    0x1D5D8: [.691, 0, .642],\n    0x1D5D9: [.691, 0, .611],\n    0x1D5DA: [.705, .011, .733],\n    0x1D5DB: [.694, 0, .794],\n    0x1D5DC: [.694, 0, .331],\n    0x1D5DD: [.694, .022, .519],\n    0x1D5DE: [.694, 0, .764],\n    0x1D5DF: [.694, 0, .581],\n    0x1D5E0: [.694, 0, .978],\n    0x1D5E1: [.694, 0, .794],\n    0x1D5E2: [.716, .022, .794],\n    0x1D5E3: [.694, 0, .703],\n    0x1D5E4: [.716, .106, .794],\n    0x1D5E5: [.694, 0, .703],\n    0x1D5E6: [.716, .022, .611],\n    0x1D5E7: [.688, 0, .733],\n    0x1D5E8: [.694, .022, .764],\n    0x1D5E9: [.694, 0, .733],\n    0x1D5EA: [.694, 0, 1.039],\n    0x1D5EB: [.694, 0, .733],\n    0x1D5EC: [.694, 0, .733],\n    0x1D5ED: [.694, 0, .672],\n    0x1D5EE: [.475, .011, .525],\n    0x1D5EF: [.694, .01, .561],\n    0x1D5F0: [.475, .011, .489],\n    0x1D5F1: [.694, .011, .561],\n    0x1D5F2: [.474, .01, .511],\n    0x1D5F3: [.705, 0, .336, { ic: .045 }],\n    0x1D5F4: [.469, .206, .55],\n    0x1D5F5: [.694, 0, .561],\n    0x1D5F6: [.695, 0, .256],\n    0x1D5F7: [.695, .205, .286],\n    0x1D5F8: [.694, 0, .531],\n    0x1D5F9: [.694, 0, .256],\n    0x1D5FA: [.469, 0, .867],\n    0x1D5FB: [.468, 0, .561],\n    0x1D5FC: [.474, .011, .55],\n    0x1D5FD: [.469, .194, .561],\n    0x1D5FE: [.469, .194, .561],\n    0x1D5FF: [.469, 0, .372],\n    0x1D600: [.474, .01, .422],\n    0x1D601: [.589, .01, .404],\n    0x1D602: [.458, .011, .561],\n    0x1D603: [.458, 0, .5],\n    0x1D604: [.458, 0, .744],\n    0x1D605: [.458, 0, .5],\n    0x1D606: [.458, .205, .5],\n    0x1D607: [.458, 0, .476],\n    0x1D608: [.694, 0, .667],\n    0x1D609: [.694, 0, .667, { ic: .029 }],\n    0x1D60A: [.705, .01, .639, { ic: .08 }],\n    0x1D60B: [.694, 0, .722, { ic: .025 }],\n    0x1D60C: [.691, 0, .597, { ic: .091 }],\n    0x1D60D: [.691, 0, .569, { ic: .104 }],\n    0x1D60E: [.705, .011, .667, { ic: .063 }],\n    0x1D60F: [.694, 0, .708, { ic: .06 }],\n    0x1D610: [.694, 0, .278, { ic: .06 }],\n    0x1D611: [.694, .022, .472, { ic: .063 }],\n    0x1D612: [.694, 0, .694, { ic: .091 }],\n    0x1D613: [.694, 0, .542],\n    0x1D614: [.694, 0, .875, { ic: .054 }],\n    0x1D615: [.694, 0, .708, { ic: .058 }],\n    0x1D616: [.716, .022, .736, { ic: .027 }],\n    0x1D617: [.694, 0, .639, { ic: .051 }],\n    0x1D618: [.716, .125, .736, { ic: .027 }],\n    0x1D619: [.694, 0, .646, { ic: .052 }],\n    0x1D61A: [.716, .022, .556, { ic: .053 }],\n    0x1D61B: [.688, 0, .681, { ic: .109 }],\n    0x1D61C: [.694, .022, .688, { ic: .059 }],\n    0x1D61D: [.694, 0, .667, { ic: .132 }],\n    0x1D61E: [.694, 0, .944, { ic: .132 }],\n    0x1D61F: [.694, 0, .667, { ic: .091 }],\n    0x1D620: [.694, 0, .667, { ic: .143 }],\n    0x1D621: [.694, 0, .611, { ic: .091 }],\n    0x1D622: [.461, .01, .481],\n    0x1D623: [.694, .011, .517, { ic: .022 }],\n    0x1D624: [.46, .011, .444, { ic: .055 }],\n    0x1D625: [.694, .01, .517, { ic: .071 }],\n    0x1D626: [.46, .011, .444, { ic: .028 }],\n    0x1D627: [.705, 0, .306, { ic: .188 }],\n    0x1D628: [.455, .206, .5, { ic: .068 }],\n    0x1D629: [.694, 0, .517],\n    0x1D62A: [.68, 0, .239, { ic: .076 }],\n    0x1D62B: [.68, .204, .267, { ic: .069 }],\n    0x1D62C: [.694, 0, .489, { ic: .054 }],\n    0x1D62D: [.694, 0, .239, { ic: .072 }],\n    0x1D62E: [.455, 0, .794],\n    0x1D62F: [.454, 0, .517],\n    0x1D630: [.461, .011, .5, { ic: .023 }],\n    0x1D631: [.455, .194, .517, { ic: .021 }],\n    0x1D632: [.455, .194, .517, { ic: .021 }],\n    0x1D633: [.455, 0, .342, { ic: .082 }],\n    0x1D634: [.461, .011, .383, { ic: .053 }],\n    0x1D635: [.571, .011, .361, { ic: .049 }],\n    0x1D636: [.444, .01, .517, { ic: .02 }],\n    0x1D637: [.444, 0, .461, { ic: .079 }],\n    0x1D638: [.444, 0, .683, { ic: .079 }],\n    0x1D639: [.444, 0, .461, { ic: .076 }],\n    0x1D63A: [.444, .205, .461, { ic: .079 }],\n    0x1D63B: [.444, 0, .435, { ic: .059 }],\n    0x1D670: [.623, 0, .525],\n    0x1D671: [.611, 0, .525],\n    0x1D672: [.622, .011, .525],\n    0x1D673: [.611, 0, .525],\n    0x1D674: [.611, 0, .525],\n    0x1D675: [.611, 0, .525],\n    0x1D676: [.622, .011, .525],\n    0x1D677: [.611, 0, .525],\n    0x1D678: [.611, 0, .525],\n    0x1D679: [.611, .011, .525],\n    0x1D67A: [.611, 0, .525],\n    0x1D67B: [.611, 0, .525],\n    0x1D67C: [.611, 0, .525],\n    0x1D67D: [.611, 0, .525],\n    0x1D67E: [.621, .01, .525],\n    0x1D67F: [.611, 0, .525],\n    0x1D680: [.621, .138, .525],\n    0x1D681: [.611, .011, .525],\n    0x1D682: [.622, .011, .525],\n    0x1D683: [.611, 0, .525],\n    0x1D684: [.611, .011, .525],\n    0x1D685: [.611, .007, .525],\n    0x1D686: [.611, .007, .525],\n    0x1D687: [.611, 0, .525],\n    0x1D688: [.611, 0, .525],\n    0x1D689: [.611, 0, .525],\n    0x1D68A: [.439, .006, .525],\n    0x1D68B: [.611, .006, .525],\n    0x1D68C: [.44, .006, .525],\n    0x1D68D: [.611, .006, .525],\n    0x1D68E: [.44, .006, .525],\n    0x1D68F: [.617, 0, .525],\n    0x1D690: [.442, .229, .525],\n    0x1D691: [.611, 0, .525],\n    0x1D692: [.612, 0, .525],\n    0x1D693: [.612, .228, .525],\n    0x1D694: [.611, 0, .525],\n    0x1D695: [.611, 0, .525],\n    0x1D696: [.436, 0, .525, { ic: .011 }],\n    0x1D697: [.436, 0, .525],\n    0x1D698: [.44, .006, .525],\n    0x1D699: [.437, .221, .525],\n    0x1D69A: [.437, .221, .525, { ic: .02 }],\n    0x1D69B: [.437, 0, .525],\n    0x1D69C: [.44, .006, .525],\n    0x1D69D: [.554, .006, .525],\n    0x1D69E: [.431, .005, .525],\n    0x1D69F: [.431, 0, .525],\n    0x1D6A0: [.431, 0, .525],\n    0x1D6A1: [.431, 0, .525],\n    0x1D6A2: [.431, .228, .525],\n    0x1D6A3: [.431, 0, .525],\n    0x1D6A8: [.698, 0, .869],\n    0x1D6A9: [.686, 0, .818],\n    0x1D6AA: [.68, 0, .692],\n    0x1D6AB: [.698, 0, .958],\n    0x1D6AC: [.68, 0, .756],\n    0x1D6AD: [.686, 0, .703],\n    0x1D6AE: [.686, 0, .9],\n    0x1D6AF: [.696, .01, .894],\n    0x1D6B0: [.686, 0, .436],\n    0x1D6B1: [.686, 0, .901],\n    0x1D6B2: [.698, 0, .806],\n    0x1D6B3: [.686, 0, 1.092],\n    0x1D6B4: [.686, 0, .9],\n    0x1D6B5: [.675, 0, .767],\n    0x1D6B6: [.696, .01, .864],\n    0x1D6B7: [.68, 0, .9],\n    0x1D6B8: [.686, 0, .786],\n    0x1D6BA: [.686, 0, .831],\n    0x1D6BB: [.675, 0, .8],\n    0x1D6BC: [.697, 0, .894],\n    0x1D6BD: [.686, 0, .831],\n    0x1D6BE: [.686, 0, .869],\n    0x1D6BF: [.686, 0, .894],\n    0x1D6C0: [.696, 0, .831],\n    0x1D6C1: [.686, .024, .958],\n    0x1D6E2: [.716, 0, .75, { sk: .139 }],\n    0x1D6E3: [.683, 0, .759, { sk: .0833 }],\n    0x1D6E4: [.68, 0, .615, { ic: .106, sk: .0833 }],\n    0x1D6E5: [.716, 0, .833, { sk: .167 }],\n    0x1D6E6: [.68, 0, .738, { ic: .026, sk: .0833 }],\n    0x1D6E7: [.683, 0, .683, { ic: .04, sk: .0833 }],\n    0x1D6E8: [.683, 0, .831, { ic: .057, sk: .0556 }],\n    0x1D6E9: [.704, .022, .763, { sk: .0833 }],\n    0x1D6EA: [.683, 0, .44, { ic: .064, sk: .111 }],\n    0x1D6EB: [.683, 0, .849, { ic: .04, sk: .0556 }],\n    0x1D6EC: [.716, 0, .694, { sk: .167 }],\n    0x1D6ED: [.683, 0, .97, { ic: .081, sk: .0833 }],\n    0x1D6EE: [.683, 0, .803, { ic: .085, sk: .0833 }],\n    0x1D6EF: [.677, 0, .742, { ic: .035, sk: .0833 }],\n    0x1D6F0: [.704, .022, .763, { sk: .0833 }],\n    0x1D6F1: [.68, 0, .831, { ic: .056, sk: .0556 }],\n    0x1D6F2: [.683, 0, .642, { ic: .109, sk: .0833 }],\n    0x1D6F4: [.683, 0, .78, { ic: .026, sk: .0833 }],\n    0x1D6F5: [.677, 0, .584, { ic: .12, sk: .0833 }],\n    0x1D6F6: [.705, 0, .583, { ic: .117, sk: .0556 }],\n    0x1D6F7: [.683, 0, .667, { sk: .0833 }],\n    0x1D6F8: [.683, 0, .828, { ic: .024, sk: .0833 }],\n    0x1D6F9: [.683, 0, .612, { ic: .08, sk: .0556 }],\n    0x1D6FA: [.704, 0, .772, { ic: .014, sk: .0833 }],\n    0x1D6FC: [.442, .011, .64, { sk: .0278 }],\n    0x1D6FD: [.705, .194, .566, { sk: .0833 }],\n    0x1D6FE: [.441, .216, .518, { ic: .025 }],\n    0x1D6FF: [.717, .01, .444, { sk: .0556 }],\n    0x1D700: [.452, .022, .466, { sk: .0833 }],\n    0x1D701: [.704, .204, .438, { ic: .033, sk: .0833 }],\n    0x1D702: [.442, .216, .497, { sk: .0556 }],\n    0x1D703: [.705, .01, .469, { sk: .0833 }],\n    0x1D704: [.442, .01, .354, { sk: .0556 }],\n    0x1D705: [.442, .011, .576],\n    0x1D706: [.694, .012, .583],\n    0x1D707: [.442, .216, .603, { sk: .0278 }],\n    0x1D708: [.442, 0, .494, { ic: .036, sk: .0278 }],\n    0x1D709: [.704, .205, .438, { sk: .111 }],\n    0x1D70A: [.441, .011, .485, { sk: .0556 }],\n    0x1D70B: [.431, .011, .57],\n    0x1D70C: [.442, .216, .517, { sk: .0833 }],\n    0x1D70D: [.442, .107, .363, { ic: .042, sk: .0833 }],\n    0x1D70E: [.431, .011, .571],\n    0x1D70F: [.431, .013, .437, { ic: .08, sk: .0278 }],\n    0x1D710: [.443, .01, .54, { sk: .0278 }],\n    0x1D711: [.442, .218, .654, { sk: .0833 }],\n    0x1D712: [.442, .204, .626, { sk: .0556 }],\n    0x1D713: [.694, .205, .651, { sk: .111 }],\n    0x1D714: [.443, .011, .622],\n    0x1D715: [.715, .022, .531, { ic: .035, sk: .0833 }],\n    0x1D716: [.431, .011, .406, { sk: .0556 }],\n    0x1D717: [.705, .011, .591, { sk: .0833 }],\n    0x1D718: [.434, .006, .667, { ic: .067 }],\n    0x1D719: [.694, .205, .596, { sk: .0833 }],\n    0x1D71A: [.442, .194, .517, { sk: .0833 }],\n    0x1D71B: [.431, .01, .828],\n    0x1D71C: [.711, 0, .869, { sk: .16 }],\n    0x1D71D: [.686, 0, .866, { sk: .0958 }],\n    0x1D71E: [.68, 0, .657, { ic: .12, sk: .0958 }],\n    0x1D71F: [.711, 0, .958, { sk: .192 }],\n    0x1D720: [.68, 0, .81, { ic: .015, sk: .0958 }],\n    0x1D721: [.686, 0, .773, { ic: .032, sk: .0958 }],\n    0x1D722: [.686, 0, .982, { ic: .045, sk: .0639 }],\n    0x1D723: [.702, .017, .867, { sk: .0958 }],\n    0x1D724: [.686, 0, .511, { ic: .062, sk: .128 }],\n    0x1D725: [.686, 0, .971, { ic: .032, sk: .0639 }],\n    0x1D726: [.711, 0, .806, { sk: .192 }],\n    0x1D727: [.686, 0, 1.142, { ic: .077, sk: .0958 }],\n    0x1D728: [.686, 0, .95, { ic: .077, sk: .0958 }],\n    0x1D729: [.675, 0, .841, { ic: .026, sk: .0958 }],\n    0x1D72A: [.703, .017, .837, { sk: .0958 }],\n    0x1D72B: [.68, 0, .982, { ic: .044, sk: .0639 }],\n    0x1D72C: [.686, 0, .723, { ic: .124, sk: .0958 }],\n    0x1D72E: [.686, 0, .885, { ic: .017, sk: .0958 }],\n    0x1D72F: [.675, 0, .637, { ic: .135, sk: .0958 }],\n    0x1D730: [.703, 0, .671, { ic: .131, sk: .0639 }],\n    0x1D731: [.686, 0, .767, { sk: .0958 }],\n    0x1D732: [.686, 0, .947, { sk: .0958 }],\n    0x1D733: [.686, 0, .714, { ic: .076, sk: .0639 }],\n    0x1D734: [.703, 0, .879, { sk: .0958 }],\n    0x1D736: [.452, .008, .761, { sk: .0319 }],\n    0x1D737: [.701, .194, .66, { sk: .0958 }],\n    0x1D738: [.451, .211, .59, { ic: .027 }],\n    0x1D739: [.725, .008, .522, { sk: .0639 }],\n    0x1D73A: [.461, .017, .529, { sk: .0958 }],\n    0x1D73B: [.711, .202, .508, { ic: .013, sk: .0958 }],\n    0x1D73C: [.452, .211, .6, { sk: .0639 }],\n    0x1D73D: [.702, .008, .562, { sk: .0958 }],\n    0x1D73E: [.452, .008, .412, { sk: .0639 }],\n    0x1D73F: [.452, .008, .668],\n    0x1D740: [.694, .013, .671],\n    0x1D741: [.452, .211, .708, { sk: .0319 }],\n    0x1D742: [.452, 0, .577, { ic: .031, sk: .0319 }],\n    0x1D743: [.711, .201, .508, { sk: .128 }],\n    0x1D744: [.452, .008, .585, { sk: .0639 }],\n    0x1D745: [.444, .008, .682],\n    0x1D746: [.451, .211, .612, { sk: .0958 }],\n    0x1D747: [.451, .105, .424, { ic: .033, sk: .0958 }],\n    0x1D748: [.444, .008, .686],\n    0x1D749: [.444, .013, .521, { ic: .089, sk: .0319 }],\n    0x1D74A: [.453, .008, .631, { sk: .0319 }],\n    0x1D74B: [.452, .216, .747, { sk: .0958 }],\n    0x1D74C: [.452, .201, .718, { sk: .0639 }],\n    0x1D74D: [.694, .202, .758, { sk: .128 }],\n    0x1D74E: [.453, .008, .718],\n    0x1D74F: [.71, .017, .628, { ic: .029, sk: .0958 }],\n    0x1D750: [.444, .007, .483, { sk: .0639 }],\n    0x1D751: [.701, .008, .692, { sk: .0958 }],\n    0x1D752: [.434, .006, .667, { ic: .067 }],\n    0x1D753: [.694, .202, .712, { sk: .0958 }],\n    0x1D754: [.451, .194, .612, { sk: .0958 }],\n    0x1D755: [.444, .008, .975],\n    0x1D756: [.694, 0, .733],\n    0x1D757: [.694, 0, .733],\n    0x1D758: [.691, 0, .581],\n    0x1D759: [.694, 0, .917],\n    0x1D75A: [.691, 0, .642],\n    0x1D75B: [.694, 0, .672],\n    0x1D75C: [.694, 0, .794],\n    0x1D75D: [.716, .022, .856],\n    0x1D75E: [.694, 0, .331],\n    0x1D75F: [.694, 0, .764],\n    0x1D760: [.694, 0, .672],\n    0x1D761: [.694, 0, .978],\n    0x1D762: [.694, 0, .794],\n    0x1D763: [.688, 0, .733],\n    0x1D764: [.716, .022, .794],\n    0x1D765: [.691, 0, .794],\n    0x1D766: [.694, 0, .703],\n    0x1D768: [.694, 0, .794],\n    0x1D769: [.688, 0, .733],\n    0x1D76A: [.715, 0, .856],\n    0x1D76B: [.694, 0, .794],\n    0x1D76C: [.694, 0, .733],\n    0x1D76D: [.694, 0, .856],\n    0x1D76E: [.716, 0, .794],\n    0x1D7CE: [.654, .01, .575],\n    0x1D7CF: [.655, 0, .575],\n    0x1D7D0: [.654, 0, .575],\n    0x1D7D1: [.655, .011, .575],\n    0x1D7D2: [.656, 0, .575],\n    0x1D7D3: [.655, .011, .575],\n    0x1D7D4: [.655, .011, .575],\n    0x1D7D5: [.676, .011, .575],\n    0x1D7D6: [.654, .011, .575],\n    0x1D7D7: [.654, .011, .575],\n    0x1D7E2: [.678, .022, .5],\n    0x1D7E3: [.678, 0, .5],\n    0x1D7E4: [.677, 0, .5],\n    0x1D7E5: [.678, .022, .5],\n    0x1D7E6: [.656, 0, .5],\n    0x1D7E7: [.656, .021, .5],\n    0x1D7E8: [.677, .022, .5],\n    0x1D7E9: [.656, .011, .5],\n    0x1D7EA: [.678, .022, .5],\n    0x1D7EB: [.677, .022, .5],\n    0x1D7EC: [.715, .022, .55],\n    0x1D7ED: [.716, 0, .55],\n    0x1D7EE: [.716, 0, .55],\n    0x1D7EF: [.716, .022, .55],\n    0x1D7F0: [.694, 0, .55],\n    0x1D7F1: [.694, .022, .55],\n    0x1D7F2: [.716, .022, .55],\n    0x1D7F3: [.695, .011, .55],\n    0x1D7F4: [.715, .022, .55],\n    0x1D7F5: [.716, .022, .55],\n    0x1D7F6: [.621, .01, .525],\n    0x1D7F7: [.622, 0, .525],\n    0x1D7F8: [.622, 0, .525],\n    0x1D7F9: [.622, .011, .525],\n    0x1D7FA: [.624, 0, .525],\n    0x1D7FB: [.611, .01, .525],\n    0x1D7FC: [.622, .011, .525],\n    0x1D7FD: [.627, .01, .525],\n    0x1D7FE: [.621, .01, .525],\n    0x1D7FF: [.622, .011, .525],\n};\n//# sourceMappingURL=normal.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sansSerifBoldItalic = void 0;\nexports.sansSerifBoldItalic = {\n    0x131: [.458, 0, .256],\n    0x237: [.458, .205, .286],\n};\n//# sourceMappingURL=sans-serif-bold-italic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sansSerifBold = void 0;\nexports.sansSerifBold = {\n    0x21: [.694, 0, .367],\n    0x22: [.694, -0.442, .558],\n    0x23: [.694, .193, .917],\n    0x24: [.75, .056, .55],\n    0x25: [.75, .056, 1.029],\n    0x26: [.716, .022, .831],\n    0x27: [.694, -0.442, .306],\n    0x28: [.75, .249, .428],\n    0x29: [.75, .25, .428],\n    0x2A: [.75, -0.293, .55],\n    0x2B: [.617, .116, .856],\n    0x2C: [.146, .106, .306],\n    0x2D: [.273, -0.186, .367],\n    0x2E: [.146, 0, .306],\n    0x2F: [.75, .249, .55],\n    0x3A: [.458, 0, .306],\n    0x3B: [.458, .106, .306],\n    0x3D: [.407, -0.094, .856],\n    0x3F: [.705, 0, .519],\n    0x40: [.704, .011, .733],\n    0x5B: [.75, .25, .343],\n    0x5D: [.75, .25, .343],\n    0x5E: [.694, -0.537, .55],\n    0x5F: [-0.023, .11, .55],\n    0x7E: [.344, -0.198, .55],\n    0x131: [.458, 0, .256],\n    0x237: [.458, .205, .286],\n    0x300: [.694, -0.537, 0],\n    0x301: [.694, -0.537, 0],\n    0x302: [.694, -0.537, 0],\n    0x303: [.694, -0.548, 0],\n    0x304: [.66, -0.56, 0],\n    0x306: [.694, -0.552, 0],\n    0x307: [.695, -0.596, 0],\n    0x308: [.695, -0.595, 0],\n    0x30A: [.694, -0.538, 0],\n    0x30B: [.694, -0.537, 0],\n    0x30C: [.657, -0.5, 0],\n    0x2013: [.327, -0.24, .55],\n    0x2014: [.327, -0.24, 1.1],\n    0x2015: [.327, -0.24, 1.1],\n    0x2017: [-0.023, .11, .55],\n    0x2018: [.694, -0.443, .306],\n    0x2019: [.694, -0.442, .306],\n    0x201C: [.694, -0.443, .558],\n    0x201D: [.694, -0.442, .558],\n    0x2044: [.75, .249, .55],\n    0x2206: [.694, 0, .917],\n};\n//# sourceMappingURL=sans-serif-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sansSerifItalic = void 0;\nexports.sansSerifItalic = {\n    0x21: [.694, 0, .319, { ic: .036 }],\n    0x22: [.694, -0.471, .5],\n    0x23: [.694, .194, .833, { ic: .018 }],\n    0x24: [.75, .056, .5, { ic: .065 }],\n    0x25: [.75, .056, .833],\n    0x26: [.716, .022, .758],\n    0x27: [.694, -0.471, .278, { ic: .057 }],\n    0x28: [.75, .25, .389, { ic: .102 }],\n    0x29: [.75, .25, .389],\n    0x2A: [.75, -0.306, .5, { ic: .068 }],\n    0x2B: [.583, .083, .778],\n    0x2C: [.098, .125, .278],\n    0x2D: [.259, -0.186, .333],\n    0x2E: [.098, 0, .278],\n    0x2F: [.75, .25, .5, { ic: .1 }],\n    0x30: [.678, .022, .5, { ic: .049 }],\n    0x31: [.678, 0, .5],\n    0x32: [.678, 0, .5, { ic: .051 }],\n    0x33: [.678, .022, .5, { ic: .044 }],\n    0x34: [.656, 0, .5, { ic: .021 }],\n    0x35: [.656, .022, .5, { ic: .055 }],\n    0x36: [.678, .022, .5, { ic: .048 }],\n    0x37: [.656, .011, .5, { ic: .096 }],\n    0x38: [.678, .022, .5, { ic: .054 }],\n    0x39: [.677, .022, .5, { ic: .045 }],\n    0x3A: [.444, 0, .278],\n    0x3B: [.444, .125, .278],\n    0x3D: [.37, -0.13, .778, { ic: .018 }],\n    0x3F: [.704, 0, .472, { ic: .064 }],\n    0x40: [.705, .01, .667, { ic: .04 }],\n    0x5B: [.75, .25, .289, { ic: .136 }],\n    0x5D: [.75, .25, .289, { ic: .064 }],\n    0x5E: [.694, -0.527, .5, { ic: .033 }],\n    0x5F: [-0.038, .114, .5, { ic: .065 }],\n    0x7E: [.327, -0.193, .5, { ic: .06 }],\n    0x131: [.444, 0, .239, { ic: .019 }],\n    0x237: [.444, .204, .267, { ic: .019 }],\n    0x300: [.694, -0.527, 0],\n    0x301: [.694, -0.527, 0, { ic: .063 }],\n    0x302: [.694, -0.527, 0, { ic: .033 }],\n    0x303: [.677, -0.543, 0, { ic: .06 }],\n    0x304: [.631, -0.552, 0, { ic: .064 }],\n    0x306: [.694, -0.508, 0, { ic: .073 }],\n    0x307: [.68, -0.576, 0],\n    0x308: [.68, -0.582, 0, { ic: .04 }],\n    0x30A: [.693, -0.527, 0],\n    0x30B: [.694, -0.527, 0, { ic: .063 }],\n    0x30C: [.654, -0.487, 0, { ic: .06 }],\n    0x391: [.694, 0, .667],\n    0x392: [.694, 0, .667, { ic: .029 }],\n    0x393: [.691, 0, .542, { ic: .104 }],\n    0x394: [.694, 0, .833],\n    0x395: [.691, 0, .597, { ic: .091 }],\n    0x396: [.694, 0, .611, { ic: .091 }],\n    0x397: [.694, 0, .708, { ic: .06 }],\n    0x398: [.715, .022, .778, { ic: .026 }],\n    0x399: [.694, 0, .278, { ic: .06 }],\n    0x39A: [.694, 0, .694, { ic: .091 }],\n    0x39B: [.694, 0, .611],\n    0x39C: [.694, 0, .875, { ic: .054 }],\n    0x39D: [.694, 0, .708, { ic: .058 }],\n    0x39E: [.688, 0, .667, { ic: .098 }],\n    0x39F: [.716, .022, .736, { ic: .027 }],\n    0x3A0: [.691, 0, .708, { ic: .06 }],\n    0x3A1: [.694, 0, .639, { ic: .051 }],\n    0x3A3: [.694, 0, .722, { ic: .091 }],\n    0x3A4: [.688, 0, .681, { ic: .109 }],\n    0x3A5: [.716, 0, .778, { ic: .065 }],\n    0x3A6: [.694, 0, .722, { ic: .021 }],\n    0x3A7: [.694, 0, .667, { ic: .091 }],\n    0x3A8: [.694, 0, .778, { ic: .076 }],\n    0x3A9: [.716, 0, .722, { ic: .047 }],\n    0x2013: [.312, -0.236, .5, { ic: .065 }],\n    0x2014: [.312, -0.236, 1, { ic: .065 }],\n    0x2015: [.312, -0.236, 1, { ic: .065 }],\n    0x2017: [-0.038, .114, .5, { ic: .065 }],\n    0x2018: [.694, -0.471, .278, { ic: .058 }],\n    0x2019: [.694, -0.471, .278, { ic: .057 }],\n    0x201C: [.694, -0.471, .5, { ic: .114 }],\n    0x201D: [.694, -0.471, .5],\n    0x2044: [.75, .25, .5, { ic: .1 }],\n    0x2206: [.694, 0, .833],\n};\n//# sourceMappingURL=sans-serif-italic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sansSerif = void 0;\nexports.sansSerif = {\n    0x21: [.694, 0, .319],\n    0x22: [.694, -0.471, .5],\n    0x23: [.694, .194, .833],\n    0x24: [.75, .056, .5],\n    0x25: [.75, .056, .833],\n    0x26: [.716, .022, .758],\n    0x27: [.694, -0.471, .278],\n    0x28: [.75, .25, .389],\n    0x29: [.75, .25, .389],\n    0x2A: [.75, -0.306, .5],\n    0x2B: [.583, .082, .778],\n    0x2C: [.098, .125, .278],\n    0x2D: [.259, -0.186, .333],\n    0x2E: [.098, 0, .278],\n    0x2F: [.75, .25, .5],\n    0x3A: [.444, 0, .278],\n    0x3B: [.444, .125, .278],\n    0x3D: [.37, -0.13, .778],\n    0x3F: [.704, 0, .472],\n    0x40: [.704, .011, .667],\n    0x5B: [.75, .25, .289],\n    0x5D: [.75, .25, .289],\n    0x5E: [.694, -0.527, .5],\n    0x5F: [-0.038, .114, .5],\n    0x7E: [.327, -0.193, .5],\n    0x131: [.444, 0, .239],\n    0x237: [.444, .205, .267],\n    0x300: [.694, -0.527, 0],\n    0x301: [.694, -0.527, 0],\n    0x302: [.694, -0.527, 0],\n    0x303: [.677, -0.543, 0],\n    0x304: [.631, -0.552, 0],\n    0x306: [.694, -0.508, 0],\n    0x307: [.68, -0.576, 0],\n    0x308: [.68, -0.582, 0],\n    0x30A: [.694, -0.527, 0],\n    0x30B: [.694, -0.527, 0],\n    0x30C: [.654, -0.487, 0],\n    0x391: [.694, 0, .667],\n    0x392: [.694, 0, .667],\n    0x393: [.691, 0, .542],\n    0x394: [.694, 0, .833],\n    0x395: [.691, 0, .597],\n    0x396: [.694, 0, .611],\n    0x397: [.694, 0, .708],\n    0x398: [.716, .021, .778],\n    0x399: [.694, 0, .278],\n    0x39A: [.694, 0, .694],\n    0x39B: [.694, 0, .611],\n    0x39C: [.694, 0, .875],\n    0x39D: [.694, 0, .708],\n    0x39E: [.688, 0, .667],\n    0x39F: [.715, .022, .736],\n    0x3A0: [.691, 0, .708],\n    0x3A1: [.694, 0, .639],\n    0x3A3: [.694, 0, .722],\n    0x3A4: [.688, 0, .681],\n    0x3A5: [.716, 0, .778],\n    0x3A6: [.694, 0, .722],\n    0x3A7: [.694, 0, .667],\n    0x3A8: [.694, 0, .778],\n    0x3A9: [.716, 0, .722],\n    0x2013: [.312, -0.236, .5],\n    0x2014: [.312, -0.236, 1],\n    0x2015: [.312, -0.236, 1],\n    0x2017: [-0.038, .114, .5],\n    0x2018: [.694, -0.471, .278],\n    0x2019: [.694, -0.471, .278],\n    0x201C: [.694, -0.471, .5],\n    0x201D: [.694, -0.471, .5],\n    0x2044: [.75, .25, .5],\n    0x2206: [.694, 0, .833],\n};\n//# sourceMappingURL=sans-serif.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.scriptBold = void 0;\nexports.scriptBold = {};\n//# sourceMappingURL=script-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.script = void 0;\nexports.script = {};\n//# sourceMappingURL=script.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.smallop = void 0;\nexports.smallop = {\n    0x28: [.85, .349, .458],\n    0x29: [.85, .349, .458],\n    0x2F: [.85, .349, .578],\n    0x5B: [.85, .349, .417],\n    0x5C: [.85, .349, .578],\n    0x5D: [.85, .349, .417],\n    0x7B: [.85, .349, .583],\n    0x7D: [.85, .349, .583],\n    0x2C6: [.744, -0.551, .556],\n    0x2DC: [.722, -0.597, .556],\n    0x302: [.744, -0.551, 0],\n    0x303: [.722, -0.597, 0],\n    0x2016: [.602, 0, .778],\n    0x2044: [.85, .349, .578],\n    0x2191: [.6, 0, .667],\n    0x2193: [.6, 0, .667],\n    0x21D1: [.599, 0, .778],\n    0x21D3: [.6, 0, .778],\n    0x220F: [.75, .25, .944],\n    0x2210: [.75, .25, .944],\n    0x2211: [.75, .25, 1.056],\n    0x221A: [.85, .35, 1, { ic: .02 }],\n    0x2223: [.627, .015, .333],\n    0x2225: [.627, .015, .556],\n    0x222B: [.805, .306, .472, { ic: .138 }],\n    0x222C: [.805, .306, .819, { ic: .138 }],\n    0x222D: [.805, .306, 1.166, { ic: .138 }],\n    0x222E: [.805, .306, .472, { ic: .138 }],\n    0x22C0: [.75, .249, .833],\n    0x22C1: [.75, .249, .833],\n    0x22C2: [.75, .249, .833],\n    0x22C3: [.75, .249, .833],\n    0x2308: [.85, .349, .472],\n    0x2309: [.85, .349, .472],\n    0x230A: [.85, .349, .472],\n    0x230B: [.85, .349, .472],\n    0x2329: [.85, .35, .472],\n    0x232A: [.85, .35, .472],\n    0x23D0: [.602, 0, .667],\n    0x2758: [.627, .015, .333],\n    0x27E8: [.85, .35, .472],\n    0x27E9: [.85, .35, .472],\n    0x2A00: [.75, .25, 1.111],\n    0x2A01: [.75, .25, 1.111],\n    0x2A02: [.75, .25, 1.111],\n    0x2A04: [.75, .249, .833],\n    0x2A06: [.75, .249, .833],\n    0x2A0C: [.805, .306, 1.638, { ic: .138 }],\n    0x3008: [.85, .35, .472],\n    0x3009: [.85, .35, .472],\n};\n//# sourceMappingURL=smallop.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texCalligraphicBold = void 0;\nexports.texCalligraphicBold = {\n    0x41: [.751, .049, .921, { ic: .068, sk: .224 }],\n    0x42: [.705, .017, .748, { sk: .16 }],\n    0x43: [.703, .02, .613, { sk: .16 }],\n    0x44: [.686, 0, .892, { sk: .0958 }],\n    0x45: [.703, .016, .607, { ic: .02, sk: .128 }],\n    0x46: [.686, .03, .814, { ic: .116, sk: .128 }],\n    0x47: [.703, .113, .682, { sk: .128 }],\n    0x48: [.686, .048, .987, { sk: .128 }],\n    0x49: [.686, 0, .642, { ic: .104, sk: .0319 }],\n    0x4A: [.686, .114, .779, { ic: .158, sk: .192 }],\n    0x4B: [.703, .017, .871, { sk: .0639 }],\n    0x4C: [.703, .017, .788, { sk: .16 }],\n    0x4D: [.703, .049, 1.378, { sk: .16 }],\n    0x4E: [.84, .049, .937, { ic: .168, sk: .0958 }],\n    0x4F: [.703, .017, .906, { sk: .128 }],\n    0x50: [.686, .067, .81, { ic: .036, sk: .0958 }],\n    0x51: [.703, .146, .939, { sk: .128 }],\n    0x52: [.686, .017, .99, { sk: .0958 }],\n    0x53: [.703, .016, .696, { ic: .025, sk: .16 }],\n    0x54: [.72, .069, .644, { ic: .303, sk: .0319 }],\n    0x55: [.686, .024, .715, { ic: .056, sk: .0958 }],\n    0x56: [.686, .077, .737, { ic: .037, sk: .0319 }],\n    0x57: [.686, .077, 1.169, { ic: .037, sk: .0958 }],\n    0x58: [.686, 0, .817, { ic: .089, sk: .16 }],\n    0x59: [.686, .164, .759, { ic: .038, sk: .0958 }],\n    0x5A: [.686, 0, .818, { ic: .035, sk: .16 }],\n    0x131: [.452, .008, .394, { sk: .0319 }],\n    0x237: [.451, .201, .439, { sk: .0958 }],\n};\n//# sourceMappingURL=tex-calligraphic-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texCalligraphic = void 0;\nexports.texCalligraphic = {\n    0x41: [.728, .05, .798, { ic: .021, sk: .194 }],\n    0x42: [.705, .022, .657, { sk: .139 }],\n    0x43: [.705, .025, .527, { sk: .139 }],\n    0x44: [.683, 0, .771, { sk: .0833 }],\n    0x45: [.705, .022, .528, { ic: .036, sk: .111 }],\n    0x46: [.683, .032, .719, { ic: .11, sk: .111 }],\n    0x47: [.704, .119, .595, { sk: .111 }],\n    0x48: [.683, .048, .845, { sk: .111 }],\n    0x49: [.683, 0, .545, { ic: .097, sk: .0278 }],\n    0x4A: [.683, .119, .678, { ic: .161, sk: .167 }],\n    0x4B: [.705, .022, .762, { sk: .0556 }],\n    0x4C: [.705, .022, .69, { sk: .139 }],\n    0x4D: [.705, .05, 1.201, { sk: .139 }],\n    0x4E: [.789, .05, .82, { ic: .159, sk: .0833 }],\n    0x4F: [.705, .022, .796, { sk: .111 }],\n    0x50: [.683, .057, .696, { ic: .037, sk: .0833 }],\n    0x51: [.705, .131, .817, { sk: .111 }],\n    0x52: [.682, .022, .848, { sk: .0833 }],\n    0x53: [.705, .022, .606, { ic: .036, sk: .139 }],\n    0x54: [.717, .068, .545, { ic: .288, sk: .0278 }],\n    0x55: [.683, .028, .626, { ic: .061, sk: .0833 }],\n    0x56: [.683, .052, .613, { ic: .045, sk: .0278 }],\n    0x57: [.683, .053, .988, { ic: .046, sk: .0833 }],\n    0x58: [.683, 0, .713, { ic: .094, sk: .139 }],\n    0x59: [.683, .143, .668, { ic: .046, sk: .0833 }],\n    0x5A: [.683, 0, .725, { ic: .042, sk: .139 }],\n};\n//# sourceMappingURL=tex-calligraphic.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texMathit = void 0;\nexports.texMathit = {\n    0x41: [.716, 0, .743],\n    0x42: [.683, 0, .704],\n    0x43: [.705, .021, .716],\n    0x44: [.683, 0, .755],\n    0x45: [.68, 0, .678],\n    0x46: [.68, 0, .653],\n    0x47: [.705, .022, .774],\n    0x48: [.683, 0, .743],\n    0x49: [.683, 0, .386],\n    0x4A: [.683, .021, .525],\n    0x4B: [.683, 0, .769],\n    0x4C: [.683, 0, .627],\n    0x4D: [.683, 0, .897],\n    0x4E: [.683, 0, .743],\n    0x4F: [.704, .022, .767],\n    0x50: [.683, 0, .678],\n    0x51: [.704, .194, .767],\n    0x52: [.683, .022, .729],\n    0x53: [.705, .022, .562],\n    0x54: [.677, 0, .716],\n    0x55: [.683, .022, .743],\n    0x56: [.683, .022, .743],\n    0x57: [.683, .022, .999],\n    0x58: [.683, 0, .743],\n    0x59: [.683, 0, .743],\n    0x5A: [.683, 0, .613],\n    0x61: [.442, .011, .511],\n    0x62: [.694, .011, .46],\n    0x63: [.441, .01, .46],\n    0x64: [.694, .011, .511],\n    0x65: [.442, .01, .46],\n    0x66: [.705, .204, .307],\n    0x67: [.442, .205, .46],\n    0x68: [.694, .011, .511],\n    0x69: [.656, .01, .307],\n    0x6A: [.656, .204, .307],\n    0x6B: [.694, .011, .46],\n    0x6C: [.694, .011, .256],\n    0x6D: [.442, .011, .818],\n    0x6E: [.442, .011, .562],\n    0x6F: [.442, .011, .511],\n    0x70: [.442, .194, .511],\n    0x71: [.442, .194, .46],\n    0x72: [.442, .011, .422],\n    0x73: [.442, .011, .409],\n    0x74: [.626, .011, .332],\n    0x75: [.441, .011, .537],\n    0x76: [.443, .01, .46],\n    0x77: [.443, .011, .664],\n    0x78: [.442, .011, .464],\n    0x79: [.441, .205, .486],\n    0x7A: [.442, .011, .409],\n};\n//# sourceMappingURL=tex-mathit.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texOldstyleBold = void 0;\nexports.texOldstyleBold = {\n    0x30: [.46, .017, .575],\n    0x31: [.461, 0, .575],\n    0x32: [.46, 0, .575],\n    0x33: [.461, .211, .575],\n    0x34: [.469, .194, .575],\n    0x35: [.461, .211, .575],\n    0x36: [.66, .017, .575],\n    0x37: [.476, .211, .575],\n    0x38: [.661, .017, .575],\n    0x39: [.461, .21, .575],\n    0x41: [.751, .049, .921, { ic: .068, sk: .224 }],\n    0x42: [.705, .017, .748, { sk: .16 }],\n    0x43: [.703, .02, .613, { sk: .16 }],\n    0x44: [.686, 0, .892, { sk: .0958 }],\n    0x45: [.703, .016, .607, { ic: .02, sk: .128 }],\n    0x46: [.686, .03, .814, { ic: .116, sk: .128 }],\n    0x47: [.703, .113, .682, { sk: .128 }],\n    0x48: [.686, .048, .987, { sk: .128 }],\n    0x49: [.686, 0, .642, { ic: .104, sk: .0319 }],\n    0x4A: [.686, .114, .779, { ic: .158, sk: .192 }],\n    0x4B: [.703, .017, .871, { sk: .0639 }],\n    0x4C: [.703, .017, .788, { sk: .16 }],\n    0x4D: [.703, .049, 1.378, { sk: .16 }],\n    0x4E: [.84, .049, .937, { ic: .168, sk: .0958 }],\n    0x4F: [.703, .017, .906, { sk: .128 }],\n    0x50: [.686, .067, .81, { ic: .036, sk: .0958 }],\n    0x51: [.703, .146, .939, { sk: .128 }],\n    0x52: [.686, .017, .99, { sk: .0958 }],\n    0x53: [.703, .016, .696, { ic: .025, sk: .16 }],\n    0x54: [.72, .069, .644, { ic: .303, sk: .0319 }],\n    0x55: [.686, .024, .715, { ic: .056, sk: .0958 }],\n    0x56: [.686, .077, .737, { ic: .037, sk: .0319 }],\n    0x57: [.686, .077, 1.169, { ic: .037, sk: .0958 }],\n    0x58: [.686, 0, .817, { ic: .089, sk: .16 }],\n    0x59: [.686, .164, .759, { ic: .038, sk: .0958 }],\n    0x5A: [.686, 0, .818, { ic: .035, sk: .16 }],\n};\n//# sourceMappingURL=tex-oldstyle-bold.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texOldstyle = void 0;\nexports.texOldstyle = {\n    0x30: [.452, .022, .5],\n    0x31: [.453, 0, .5],\n    0x32: [.453, 0, .5],\n    0x33: [.452, .216, .5],\n    0x34: [.464, .194, .5],\n    0x35: [.453, .216, .5],\n    0x36: [.665, .022, .5],\n    0x37: [.463, .216, .5],\n    0x38: [.666, .021, .5],\n    0x39: [.453, .216, .5],\n    0x41: [.728, .05, .798, { ic: .021, sk: .194 }],\n    0x42: [.705, .022, .657, { sk: .139 }],\n    0x43: [.705, .025, .527, { sk: .139 }],\n    0x44: [.683, 0, .771, { sk: .0833 }],\n    0x45: [.705, .022, .528, { ic: .036, sk: .111 }],\n    0x46: [.683, .032, .719, { ic: .11, sk: .111 }],\n    0x47: [.704, .119, .595, { sk: .111 }],\n    0x48: [.683, .048, .845, { sk: .111 }],\n    0x49: [.683, 0, .545, { ic: .097, sk: .0278 }],\n    0x4A: [.683, .119, .678, { ic: .161, sk: .167 }],\n    0x4B: [.705, .022, .762, { sk: .0556 }],\n    0x4C: [.705, .022, .69, { sk: .139 }],\n    0x4D: [.705, .05, 1.201, { sk: .139 }],\n    0x4E: [.789, .05, .82, { ic: .159, sk: .0833 }],\n    0x4F: [.705, .022, .796, { sk: .111 }],\n    0x50: [.683, .057, .696, { ic: .037, sk: .0833 }],\n    0x51: [.705, .131, .817, { sk: .111 }],\n    0x52: [.682, .022, .848, { sk: .0833 }],\n    0x53: [.705, .022, .606, { ic: .036, sk: .139 }],\n    0x54: [.717, .068, .545, { ic: .288, sk: .0278 }],\n    0x55: [.683, .028, .626, { ic: .061, sk: .0833 }],\n    0x56: [.683, .052, .613, { ic: .045, sk: .0278 }],\n    0x57: [.683, .053, .988, { ic: .046, sk: .0833 }],\n    0x58: [.683, 0, .713, { ic: .094, sk: .139 }],\n    0x59: [.683, .143, .668, { ic: .046, sk: .0833 }],\n    0x5A: [.683, 0, .725, { ic: .042, sk: .139 }],\n};\n//# sourceMappingURL=tex-oldstyle.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texSize3 = void 0;\nexports.texSize3 = {\n    0x28: [1.45, .949, .736],\n    0x29: [1.45, .949, .736],\n    0x2F: [1.45, .949, 1.044],\n    0x5B: [1.45, .949, .528],\n    0x5C: [1.45, .949, 1.044],\n    0x5D: [1.45, .949, .528],\n    0x7B: [1.45, .949, .75],\n    0x7D: [1.45, .949, .75],\n    0x2C6: [.772, -0.564, 1.444],\n    0x2DC: [.749, -0.61, 1.444],\n    0x302: [.772, -0.564, 0],\n    0x303: [.749, -0.61, 0],\n    0x2044: [1.45, .949, 1.044],\n    0x221A: [1.45, .95, 1, { ic: .02 }],\n    0x2308: [1.45, .949, .583],\n    0x2309: [1.45, .949, .583],\n    0x230A: [1.45, .949, .583],\n    0x230B: [1.45, .949, .583],\n    0x2329: [1.45, .95, .75],\n    0x232A: [1.45, .949, .75],\n    0x27E8: [1.45, .95, .75],\n    0x27E9: [1.45, .949, .75],\n    0x3008: [1.45, .95, .75],\n    0x3009: [1.45, .949, .75],\n};\n//# sourceMappingURL=tex-size3.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texSize4 = void 0;\nexports.texSize4 = {\n    0x28: [1.75, 1.249, .792],\n    0x29: [1.75, 1.249, .792],\n    0x2F: [1.75, 1.249, 1.278],\n    0x5B: [1.75, 1.249, .583],\n    0x5C: [1.75, 1.249, 1.278],\n    0x5D: [1.75, 1.249, .583],\n    0x7B: [1.75, 1.249, .806],\n    0x7D: [1.75, 1.249, .806],\n    0x2C6: [.845, -0.561, 1.889, { ic: .013 }],\n    0x2DC: [.823, -0.583, 1.889],\n    0x302: [.845, -0.561, 0, { ic: .013 }],\n    0x303: [.823, -0.583, 0],\n    0x2044: [1.75, 1.249, 1.278],\n    0x221A: [1.75, 1.25, 1, { ic: .02 }],\n    0x2308: [1.75, 1.249, .639],\n    0x2309: [1.75, 1.249, .639],\n    0x230A: [1.75, 1.249, .639],\n    0x230B: [1.75, 1.249, .639],\n    0x2329: [1.75, 1.248, .806],\n    0x232A: [1.75, 1.248, .806],\n    0x239B: [1.154, .655, .875],\n    0x239C: [.61, .01, .875],\n    0x239D: [1.165, .644, .875],\n    0x239E: [1.154, .655, .875],\n    0x239F: [.61, .01, .875],\n    0x23A0: [1.165, .644, .875],\n    0x23A1: [1.154, .645, .667],\n    0x23A2: [.602, 0, .667],\n    0x23A3: [1.155, .644, .667],\n    0x23A4: [1.154, .645, .667],\n    0x23A5: [.602, 0, .667],\n    0x23A6: [1.155, .644, .667],\n    0x23A7: [.899, .01, .889],\n    0x23A8: [1.16, .66, .889],\n    0x23A9: [.01, .899, .889],\n    0x23AA: [.29, .015, .889],\n    0x23AB: [.899, .01, .889],\n    0x23AC: [1.16, .66, .889],\n    0x23AD: [.01, .899, .889],\n    0x23B7: [.935, .885, 1.056],\n    0x27E8: [1.75, 1.248, .806],\n    0x27E9: [1.75, 1.248, .806],\n    0x3008: [1.75, 1.248, .806],\n    0x3009: [1.75, 1.248, .806],\n    0xE000: [.625, .014, 1.056],\n    0xE001: [.605, .014, 1.056, { ic: .02 }],\n    0xE150: [.12, .213, .45, { ic: .01 }],\n    0xE151: [.12, .213, .45, { ic: .024 }],\n    0xE152: [.333, 0, .45, { ic: .01 }],\n    0xE153: [.333, 0, .45, { ic: .024 }],\n    0xE154: [.32, .2, .4, { ic: .01 }],\n    0xE155: [.333, 0, .9, { ic: .01 }],\n    0xE156: [.12, .213, .9, { ic: .01 }],\n};\n//# sourceMappingURL=tex-size4.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.texVariant = void 0;\nexports.texVariant = {\n    0x2C6: [.845, -0.561, 2.333, { ic: .013 }],\n    0x2DC: [.899, -0.628, 2.333],\n    0x302: [.845, -0.561, 0, { ic: .013 }],\n    0x303: [.899, -0.628, 0],\n    0x3F0: [.434, .006, .667, { ic: .067 }],\n    0x210F: [.695, .013, .54, { ic: .022 }],\n    0x2190: [.437, -0.064, .5],\n    0x2192: [.437, -0.064, .5],\n    0x21CC: [.514, .014, 1],\n    0x2204: [.86, .166, .556],\n    0x2205: [.587, 0, .778],\n    0x2212: [.27, -0.23, .5],\n    0x2216: [.43, .023, .778],\n    0x221D: [.472, -0.028, .778],\n    0x2223: [.43, .023, .222],\n    0x2224: [.43, .023, .222, { ic: .018 }],\n    0x2225: [.431, .023, .389],\n    0x2226: [.431, .024, .389, { ic: .018 }],\n    0x223C: [.365, -0.132, .778],\n    0x2248: [.481, -0.05, .778],\n    0x2268: [.752, .284, .778],\n    0x2269: [.752, .284, .778],\n    0x2270: [.919, .421, .778],\n    0x2271: [.919, .421, .778],\n    0x2288: [.828, .33, .778],\n    0x2289: [.828, .33, .778],\n    0x228A: [.634, .255, .778],\n    0x228B: [.634, .254, .778],\n    0x22A8: [.694, 0, .611],\n    0x22C5: [.189, 0, .278],\n    0x2322: [.378, -0.122, .778],\n    0x2323: [.378, -0.143, .778],\n    0x25B3: [.575, .02, .722],\n    0x25BD: [.576, .019, .722],\n    0x2A87: [.801, .303, .778],\n    0x2A88: [.801, .303, .778],\n    0x2ACB: [.752, .332, .778],\n    0x2ACC: [.752, .333, .778],\n};\n//# sourceMappingURL=tex-variant.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.px = exports.emRounded = exports.em = exports.percent = exports.length2em = exports.MATHSPACE = exports.RELUNITS = exports.UNITS = exports.BIGDIMEN = void 0;\nexports.BIGDIMEN = 1000000;\nexports.UNITS = {\n    px: 1,\n    'in': 96,\n    cm: 96 / 2.54,\n    mm: 96 / 25.4\n};\nexports.RELUNITS = {\n    em: 1,\n    ex: .431,\n    pt: 1 / 10,\n    pc: 12 / 10,\n    mu: 1 / 18\n};\nexports.MATHSPACE = {\n    veryverythinmathspace: 1 / 18,\n    verythinmathspace: 2 / 18,\n    thinmathspace: 3 / 18,\n    mediummathspace: 4 / 18,\n    thickmathspace: 5 / 18,\n    verythickmathspace: 6 / 18,\n    veryverythickmathspace: 7 / 18,\n    negativeveryverythinmathspace: -1 / 18,\n    negativeverythinmathspace: -2 / 18,\n    negativethinmathspace: -3 / 18,\n    negativemediummathspace: -4 / 18,\n    negativethickmathspace: -5 / 18,\n    negativeverythickmathspace: -6 / 18,\n    negativeveryverythickmathspace: -7 / 18,\n    thin: .04,\n    medium: .06,\n    thick: .1,\n    normal: 1,\n    big: 2,\n    small: 1 / Math.sqrt(2),\n    infinity: exports.BIGDIMEN\n};\nfunction length2em(length, size, scale, em) {\n    if (size === void 0) { size = 0; }\n    if (scale === void 0) { scale = 1; }\n    if (em === void 0) { em = 16; }\n    if (typeof length !== 'string') {\n        length = String(length);\n    }\n    if (length === '' || length == null) {\n        return size;\n    }\n    if (exports.MATHSPACE[length]) {\n        return exports.MATHSPACE[length];\n    }\n    var match = length.match(/^\\s*([-+]?(?:\\.\\d+|\\d+(?:\\.\\d*)?))?(pt|em|ex|mu|px|pc|in|mm|cm|%)?/);\n    if (!match) {\n        return size;\n    }\n    var m = parseFloat(match[1] || '1'), unit = match[2];\n    if (exports.UNITS.hasOwnProperty(unit)) {\n        return m * exports.UNITS[unit] / em / scale;\n    }\n    if (exports.RELUNITS.hasOwnProperty(unit)) {\n        return m * exports.RELUNITS[unit];\n    }\n    if (unit === '%') {\n        return m / 100 * size;\n    }\n    return m * size;\n}\nexports.length2em = length2em;\nfunction percent(m) {\n    return (100 * m).toFixed(1).replace(/\\.?0+$/, '') + '%';\n}\nexports.percent = percent;\nfunction em(m) {\n    if (Math.abs(m) < .001)\n        return '0';\n    return (m.toFixed(3).replace(/\\.?0+$/, '')) + 'em';\n}\nexports.em = em;\nfunction emRounded(m, em) {\n    if (em === void 0) { em = 16; }\n    m = (Math.round(m * em) + .05) / em;\n    if (Math.abs(m) < .001)\n        return '0em';\n    return m.toFixed(3).replace(/\\.?0+$/, '') + 'em';\n}\nexports.emRounded = emRounded;\nfunction px(m, M, em) {\n    if (M === void 0) { M = -exports.BIGDIMEN; }\n    if (em === void 0) { em = 16; }\n    m *= em;\n    if (M && m < M)\n        m = M;\n    if (Math.abs(m) < .1)\n        return '0';\n    return m.toFixed(1).replace(/\\.0$/, '') + 'px';\n}\nexports.px = px;\n//# sourceMappingURL=lengths.js.map"], "names": [], "sourceRoot": ""}