{"version": 3, "file": "7302.d78f81cccff1a175f9e0.js?v=d78f81cccff1a175f9e0", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACmF;AACzD;AACnB;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,yCAAyC;AACzD;AACA;AACA,4BAA4B,qBAAW,QAAQ,sCAAmB,UAAU,oBAAoB;AAChG;AACA;AACA;AACA;AACA;AACA,2BAA2B,qBAAW,QAAQ,sCAAmB,CAAC,uCAA6B,IAAI,oDAAoD,OAAO,WAAW,eAAe,mBAAmB;AAC3M,sCAAsC,OAAO;AAC7C;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACnDA;AACA;AACkH;AAC/D;AAC2C;AAC/B;AACgB;AACe;AACxC;AAC8B;AACzC;AACK;AAC2B;AACxB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,gCAAgC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,qCAAW;AAC1B,eAAe,gCAAsB;AACrC;AACA;AACA;AACA,gBAAgB,2BAA2B;AAC3C;AACA;AACA,mCAAmC,kBAAkB;AACrD;AACA,4BAA4B,0BAAO;AACnC,4BAA4B,uBAAI,GAAG,UAAU;AAC7C;AACA,6BAA6B,yCAAa;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,oCAAoC;AAChE;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,SAAS;AAC3C,aAAa;AACb;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,oCAAoC,0BAAO;AAC3C;AACA;AACA;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,6CAAmB,EAAE,gCAAsB,EAAE,qCAAW;AACvE;AACA;AACA;AACA,qCAAqC,oBAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,gBAAgB,WAAW;AAC3B,gCAAgC,kBAAkB;AAClD;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,6CAAmB;AAClC,eAAe,8CAAgB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,yBAAe;AAC9B;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,8CAAgB;AAC/B;AACA,gBAAgB,aAAa,EAAE,wBAAU;AACzC;AACA,iCAAiC,wBAAU;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE,GAAG;AACpE;AACA,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gCAAa,EAAE,6CAAmB;AACjD;AACA;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,6CAAmB;AAC3B,QAAQ,qCAAW;AACnB,QAAQ,8CAAgB;AACxB,QAAQ,gCAAsB;AAC9B,QAAQ,6CAAmB;AAC3B;AACA;AACA,QAAQ,6CAAuB;AAC/B,QAAQ,iDAAqB;AAC7B,QAAQ,qDAAyB;AACjC;AACA;AACA,cAAc,gCAAa;AAC3B;AACA,iCAAiC,qCAAkB;AACnD;AACA;AACA;AACA;AACA,6BAA6B,sCAAU;AACvC;AACA;AACA;AACA,sFAAsF,kCAAQ;AAC9F;AACA;AACA;AACA,SAAS;AACT,QAAQ,wBAAU,UAAU,kCAAoB;AAChD;AACA,gCAAgC,qCAAe;AAC/C;AACA;AACA,iCAAiC,uCAAW;AAC5C;AACA;AACA;AACA,8CAA8C,WAAW;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT,gBAAgB,UAAU;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAAe,OAAO,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree-extension/lib/fileactions.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree-extension/lib/index.js"], "sourcesContent": ["// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { CommandToolbarButtonComponent, ReactWidget, } from '@jupyterlab/apputils';\nimport React from 'react';\nexport class FilesActionButtons {\n    /**\n     * The constructor of FilesActionButtons.\n     * @param options\n     */\n    constructor(options) {\n        /**\n         * Triggered when the selection change in file browser.\n         */\n        this._onSelectionChanged = () => {\n            var _a, _b, _c, _d, _e, _f;\n            const selectedItems = Array.from(this._browser.selectedItems());\n            const selection = selectedItems.length > 0;\n            const oneFolder = selectedItems.some((item) => item.type === 'directory');\n            (_a = this._widgets.get('placeholder')) === null || _a === void 0 ? void 0 : _a.setHidden(selection);\n            (_b = this._widgets.get('delete')) === null || _b === void 0 ? void 0 : _b.setHidden(!selection);\n            (_c = this._widgets.get('duplicate')) === null || _c === void 0 ? void 0 : _c.setHidden(!selection || oneFolder);\n            (_d = this._widgets.get('download')) === null || _d === void 0 ? void 0 : _d.setHidden(!selection || oneFolder);\n            (_e = this._widgets.get('open')) === null || _e === void 0 ? void 0 : _e.setHidden(!selection || oneFolder);\n            (_f = this._widgets.get('rename')) === null || _f === void 0 ? void 0 : _f.setHidden(selectedItems.length !== 1);\n        };\n        this._widgets = new Map();\n        this._browser = options.browser;\n        const { commands, selectionChanged, translator } = options;\n        const trans = translator.load('notebook');\n        // Placeholder, when no file is selected.\n        const placeholder = ReactWidget.create(React.createElement(\"div\", { key: 'placeholder' }, trans.__('Select items to perform actions on them.')));\n        placeholder.id = 'fileAction-placeholder';\n        this._widgets.set('placeholder', placeholder);\n        // The action buttons.\n        const actions = ['open', 'download', 'rename', 'duplicate', 'delete'];\n        actions.forEach((action) => {\n            const widget = ReactWidget.create(React.createElement(CommandToolbarButtonComponent, { key: action, commands: commands, id: `filebrowser:${action}`, args: { toolbar: true }, icon: undefined }));\n            widget.id = `fileAction-${action}`;\n            widget.addClass('jp-ToolbarButton');\n            widget.addClass('jp-FileAction');\n            this._widgets.set(action, widget);\n        });\n        selectionChanged.connect(this._onSelectionChanged, this);\n        this._onSelectionChanged();\n    }\n    /**\n     * Return an iterator with all the action widgets.\n     */\n    get widgets() {\n        return this._widgets.values();\n    }\n}\n", "// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { ICommandPalette, IToolbarWidgetRegistry, createToolbarFactory, setToolbar, } from '@jupyterlab/apputils';\nimport { PageConfig } from '@jupyterlab/coreutils';\nimport { Uploader, IDefaultFileBrowser, IFileBrowserFactory, } from '@jupyterlab/filebrowser';\nimport { ISettingRegistry } from '@jupyterlab/settingregistry';\nimport { IRunningSessionManagers, RunningSessions } from '@jupyterlab/running';\nimport { IJSONSettingEditorTracker, ISettingEditorTracker, } from '@jupyterlab/settingeditor';\nimport { ITranslator } from '@jupyterlab/translation';\nimport { caretDownIcon, folderIcon, runningIcon, } from '@jupyterlab/ui-components';\nimport { Signal } from '@lumino/signaling';\nimport { Menu, MenuBar } from '@lumino/widgets';\nimport { NotebookTreeWidget, INotebookTree } from '@jupyter-notebook/tree';\nimport { FilesActionButtons } from './fileactions';\n/**\n * The file browser factory.\n */\nconst FILE_BROWSER_FACTORY = 'FileBrowser';\n/**\n * The file browser plugin id.\n */\nconst FILE_BROWSER_PLUGIN_ID = '@jupyterlab/filebrowser-extension:browser';\n/**\n * The namespace for command IDs.\n */\nvar CommandIDs;\n(function (CommandIDs) {\n    // The command to activate the filebrowser widget in tree view.\n    CommandIDs.activate = 'filebrowser:activate';\n    // Activate the file filter in the file browser\n    CommandIDs.toggleFileFilter = 'filebrowser:toggle-file-filter';\n})(CommandIDs || (CommandIDs = {}));\n/**\n * Plugin to add extra commands to the file browser to create\n * new notebooks, files, consoles and terminals\n */\nconst createNew = {\n    id: '@jupyter-notebook/tree-extension:new',\n    description: 'Plugin to add extra commands to the file browser to create new notebooks, files, consoles and terminals.',\n    requires: [ITranslator],\n    optional: [IToolbarWidgetRegistry],\n    autoStart: true,\n    activate: (app, translator, toolbarRegistry) => {\n        var _a;\n        const { commands, serviceManager } = app;\n        const trans = translator.load('notebook');\n        const overflowOptions = {\n            overflowMenuOptions: { isVisible: false },\n        };\n        const menubar = new MenuBar(overflowOptions);\n        const newMenu = new Menu({ commands });\n        newMenu.title.label = trans.__('New');\n        newMenu.title.icon = caretDownIcon;\n        menubar.addMenu(newMenu);\n        const populateNewMenu = () => {\n            var _a, _b;\n            // create an entry per kernel spec for creating a new notebook\n            const specs = (_b = (_a = serviceManager.kernelspecs) === null || _a === void 0 ? void 0 : _a.specs) === null || _b === void 0 ? void 0 : _b.kernelspecs;\n            for (const name in specs) {\n                newMenu.addItem({\n                    args: { kernelName: name, isLauncher: true },\n                    command: 'notebook:create-new',\n                });\n            }\n            const baseCommands = [\n                'terminal:create-new',\n                'console:create',\n                'filebrowser:create-new-file',\n                'filebrowser:create-new-directory',\n            ];\n            baseCommands.forEach((command) => {\n                newMenu.addItem({ command });\n            });\n        };\n        (_a = serviceManager.kernelspecs) === null || _a === void 0 ? void 0 : _a.specsChanged.connect(() => {\n            newMenu.clearItems();\n            populateNewMenu();\n        });\n        populateNewMenu();\n        if (toolbarRegistry) {\n            toolbarRegistry.addFactory(FILE_BROWSER_FACTORY, 'new-dropdown', (browser) => {\n                const menubar = new MenuBar(overflowOptions);\n                menubar.addMenu(newMenu);\n                menubar.addClass('jp-DropdownMenu');\n                return menubar;\n            });\n        }\n    },\n};\n/**\n * A plugin to add file browser actions to the file browser toolbar.\n */\nconst fileActions = {\n    id: '@jupyter-notebook/tree-extension:file-actions',\n    description: 'A plugin to add file browser actions to the file browser toolbar.',\n    autoStart: true,\n    requires: [IDefaultFileBrowser, IToolbarWidgetRegistry, ITranslator],\n    activate: (app, browser, toolbarRegistry, translator) => {\n        // TODO: use upstream signal when available to detect selection changes\n        // https://github.com/jupyterlab/jupyterlab/issues/14598\n        const selectionChanged = new Signal(browser);\n        const methods = [\n            '_selectItem',\n            '_handleMultiSelect',\n            'handleFileSelect',\n        ];\n        methods.forEach((method) => {\n            const original = browser['listing'][method];\n            browser['listing'][method] = (...args) => {\n                original.call(browser['listing'], ...args);\n                selectionChanged.emit(void 0);\n            };\n        });\n        browser.model.pathChanged.connect(() => {\n            selectionChanged.emit(void 0);\n        });\n        // Create a toolbar item that adds buttons to the file browser toolbar\n        // to perform actions on the files\n        const { commands } = app;\n        const fileActions = new FilesActionButtons({\n            commands,\n            browser,\n            selectionChanged,\n            translator,\n        });\n        for (const widget of fileActions.widgets) {\n            toolbarRegistry.addFactory(FILE_BROWSER_FACTORY, widget.id, () => widget);\n        }\n    },\n};\n/**\n * A plugin to set the default file browser settings.\n */\nconst fileBrowserSettings = {\n    id: '@jupyter-notebook/tree-extension:settings',\n    description: 'Set up the default file browser settings',\n    requires: [IDefaultFileBrowser],\n    optional: [ISettingRegistry],\n    autoStart: true,\n    activate: (app, browser, settingRegistry) => {\n        // Default config for notebook.\n        // This is a different set of defaults than JupyterLab.\n        const defaultFileBrowserConfig = {\n            navigateToCurrentDirectory: false,\n            singleClickNavigation: true,\n            showLastModifiedColumn: true,\n            showFileSizeColumn: true,\n            showHiddenFiles: false,\n            showFileCheckboxes: true,\n            sortNotebooksFirst: true,\n            showFullPath: false,\n        };\n        // Apply defaults on plugin activation\n        let key;\n        for (key in defaultFileBrowserConfig) {\n            browser[key] = defaultFileBrowserConfig[key];\n        }\n        if (settingRegistry) {\n            void settingRegistry.load(FILE_BROWSER_PLUGIN_ID).then((settings) => {\n                function onSettingsChanged(settings) {\n                    let key;\n                    for (key in defaultFileBrowserConfig) {\n                        const value = settings.get(key).user;\n                        // only set the setting if it is defined by the user\n                        if (value !== undefined) {\n                            browser[key] = value;\n                        }\n                    }\n                }\n                settings.changed.connect(onSettingsChanged);\n                onSettingsChanged(settings);\n            });\n        }\n    },\n};\n/**\n * A plugin to add the file filter toggle command to the palette\n */\nconst fileFilterCommand = {\n    id: '@jupyter-notebook/tree-extension:file-filter-command',\n    description: 'A plugin to add file filter command to the palette.',\n    autoStart: true,\n    optional: [ICommandPalette],\n    activate: (app, palette) => {\n        if (palette) {\n            palette.addItem({\n                command: CommandIDs.toggleFileFilter,\n                category: 'File Browser',\n            });\n        }\n    },\n};\n/**\n * Plugin to load the default plugins that are loaded on all the Notebook pages\n * (tree, edit, view, etc.) so they are visible in the settings editor.\n */\nconst loadPlugins = {\n    id: '@jupyter-notebook/tree-extension:load-plugins',\n    description: 'Plugin to load the default plugins that are loaded on all the Notebook pages (tree, edit, view, etc.) so they are visible in the settings editor.',\n    autoStart: true,\n    requires: [ISettingRegistry],\n    activate(app, settingRegistry) {\n        const { isDisabled } = PageConfig.Extension;\n        const connector = settingRegistry.connector;\n        const allPluginsOption = PageConfig.getOption('allPlugins');\n        if (!allPluginsOption) {\n            return;\n        }\n        // build the list of plugins shipped by default on the all the notebook pages\n        // this avoid explicitly loading `'all'` plugins such as the ones used\n        // in JupyterLab only\n        const allPlugins = JSON.parse(allPluginsOption);\n        const pluginsSet = new Set();\n        Object.keys(allPlugins).forEach((key) => {\n            const extensionsAndPlugins = allPlugins[key];\n            Object.keys(extensionsAndPlugins).forEach((plugin) => {\n                const value = extensionsAndPlugins[plugin];\n                if (typeof value === 'boolean' && value) {\n                    pluginsSet.add(plugin);\n                }\n                else if (Array.isArray(value)) {\n                    value.forEach((v) => {\n                        pluginsSet.add(v);\n                    });\n                }\n            });\n        });\n        app.restored.then(async () => {\n            const plugins = await connector.list('all');\n            plugins.ids.forEach(async (id) => {\n                const [extension] = id.split(':');\n                // load the plugin if it is built-in the notebook application explicitly\n                // either included as an extension or as a plugin directly\n                const hasPlugin = pluginsSet.has(extension) || pluginsSet.has(id);\n                if (!hasPlugin || isDisabled(id) || id in settingRegistry.plugins) {\n                    return;\n                }\n                try {\n                    await settingRegistry.load(id);\n                }\n                catch (error) {\n                    console.warn(`Settings failed to load for (${id})`, error);\n                }\n            });\n        });\n    },\n};\n/**\n * A plugin to add file browser commands for the tree view.\n */\nconst openFileBrowser = {\n    id: '@jupyter-notebook/tree-extension:open-file-browser',\n    description: 'A plugin to add file browser commands for the tree view.',\n    requires: [INotebookTree, IDefaultFileBrowser],\n    autoStart: true,\n    activate: (app, notebookTree, browser) => {\n        const { commands } = app;\n        commands.addCommand(CommandIDs.activate, {\n            execute: () => {\n                notebookTree.currentWidget = browser;\n            },\n        });\n    },\n};\n/**\n * A plugin to add the file browser widget to an INotebookShell\n */\nconst notebookTreeWidget = {\n    id: '@jupyter-notebook/tree-extension:widget',\n    description: 'A plugin to add the file browser widget to an INotebookShell.',\n    requires: [\n        IDefaultFileBrowser,\n        ITranslator,\n        ISettingRegistry,\n        IToolbarWidgetRegistry,\n        IFileBrowserFactory,\n    ],\n    optional: [\n        IRunningSessionManagers,\n        ISettingEditorTracker,\n        IJSONSettingEditorTracker,\n    ],\n    autoStart: true,\n    provides: INotebookTree,\n    activate: (app, browser, translator, settingRegistry, toolbarRegistry, factory, manager, settingEditorTracker, jsonSettingEditorTracker) => {\n        const nbTreeWidget = new NotebookTreeWidget();\n        const trans = translator.load('notebook');\n        browser.title.label = trans.__('Files');\n        browser.node.setAttribute('role', 'region');\n        browser.node.setAttribute('aria-label', trans.__('File Browser Section'));\n        browser.title.icon = folderIcon;\n        nbTreeWidget.addWidget(browser);\n        nbTreeWidget.tabBar.addTab(browser.title);\n        nbTreeWidget.tabsMovable = false;\n        toolbarRegistry.addFactory(FILE_BROWSER_FACTORY, 'uploader', (browser) => new Uploader({\n            model: browser.model,\n            translator,\n            label: trans.__('Upload'),\n        }));\n        setToolbar(browser, createToolbarFactory(toolbarRegistry, settingRegistry, FILE_BROWSER_FACTORY, notebookTreeWidget.id, translator));\n        if (manager) {\n            const running = new RunningSessions(manager, translator);\n            running.id = 'jp-running-sessions-tree';\n            running.title.label = trans.__('Running');\n            running.title.icon = runningIcon;\n            nbTreeWidget.addWidget(running);\n            nbTreeWidget.tabBar.addTab(running.title);\n        }\n        app.shell.add(nbTreeWidget, 'main', { rank: 100 });\n        // add a separate tab for each setting editor\n        [settingEditorTracker, jsonSettingEditorTracker].forEach((editorTracker) => {\n            if (editorTracker) {\n                editorTracker.widgetAdded.connect((_, editor) => {\n                    nbTreeWidget.addWidget(editor);\n                    nbTreeWidget.tabBar.addTab(editor.title);\n                    nbTreeWidget.currentWidget = editor;\n                });\n            }\n        });\n        const { tracker } = factory;\n        // TODO: remove\n        // Workaround to force the focus on the default file browser\n        // See https://github.com/jupyterlab/jupyterlab/issues/15629 for more info\n        const setCurrentToDefaultBrower = () => {\n            tracker['_pool'].current = browser;\n        };\n        tracker.widgetAdded.connect((sender, widget) => {\n            setCurrentToDefaultBrower();\n        });\n        setCurrentToDefaultBrower();\n        return nbTreeWidget;\n    },\n};\n/**\n * Export the plugins as default.\n */\nconst plugins = [\n    createNew,\n    fileActions,\n    fileBrowserSettings,\n    fileFilterCommand,\n    loadPlugins,\n    openFileBrowser,\n    notebookTreeWidget,\n];\nexport default plugins;\n"], "names": [], "sourceRoot": ""}