{"version": 3, "file": "6271.7ad87d1f14b9d74742ec.js?v=7ad87d1f14b9d74742ec", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAsF;AAC9B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,8BAAQ;AAClC;AACA;AACA,uBAAuB,8BAAQ;AAC/B,wBAAwB,0BAAI;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,0BAAI;AACvB,2DAA2D,0BAAI,CAAC,8BAAQ;AACxE,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,oBAAoB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,QAAQ;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,uCAAuC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,wBAAwB;AACxB;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,sBAAsB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,wBAAwB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,QAAQ;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,OAAO;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,gDAAgD;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAmB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,2FAA2F;AACxH,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,OAAO;AACrB,eAAe;AACf,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,OAAO,UAAU,YAAY;AAC3C;AACA;AACA,eAAe,gCAAgC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,0BAAI;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,IAAI;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0BAAI;AACnB;AACA;AACA,6BAA6B,4BAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,6BAA6B;AAC3C;AACA;AACA,gDAAgD;AAChD;AACA;AACA,sBAAsB,gCAAgC,2BAA2B,UAAU;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,8BAAQ;AACvC;AACA;AACA,sCAAsC,8BAAQ;AAC9C,iBAAiB;AACjB;AACA;AACA;AACA,iEAAiE,4BAAG;AACpE;AACA;AACA;AACA;AACA;AACA,0BAA0B,6BAAO;AACjC;AACA,yCAAyC,sCAAS;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,KAAK;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,aAAa;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E,KAAK;AACnF;AACA;AACA,iBAAiB,8BAAQ;AACzB,sBAAsB,gBAAgB;AACtC,mBAAmB,8BAAQ;AAC3B;AACA;AACA,yCAAyC,8BAAQ;AACjD;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0BAAI;AACnB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,iBAAiB;AACjB,qBAAqB;AACrB;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,2BAA2B;AAC3B,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,aAAa,EAAE;AACpD,uCAAuC,eAAe,EAAE;AACxD;AACA,mCAAmC,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,sBAAsB;AAC9C;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,0CAA0C;AAC1C;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,cAAc;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,mEAAmE,EAAE,wBAAwB,KAAK,iCAAiC,KAAK;AACxI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,0CAA0C,QAAQ;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,OAAO;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,OAAO;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,mBAAmB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,mBAAmB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,mBAAmB;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF,WAAW;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA,gBAAgB;AAChB;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,QAAQ;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,uBAAuB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,WAAW;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,OAAO;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,uBAAuB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,QAAQ;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,8BAAQ;AACzC;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA;AACA;AACA,gCAAgC,0BAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,mBAAmB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,sCAAS;AACtC,sBAAsB,6BAAI;AAC1B,oBAAoB,6BAAI;AACxB,0CAA0C,6BAAI;AAC9C,0CAA0C,6BAAI;AAC9C,uBAAuB,6BAAI;AAC3B,uBAAuB,6BAAI;AAC3B,uBAAuB,6BAAI;AAC3B,uBAAuB,6BAAI;AAC3B,4BAA4B,6BAAI;AAChC,YAAY,6BAAI;AAChB,YAAY,6BAAI;AAChB,oBAAoB,6BAAI;AACxB,0BAA0B,6BAAI;AAC9B,0BAA0B,6BAAI;AAC9B,sCAAsC,6BAAI;AAC1C,sBAAsB,6BAAI;AAC1B,2BAA2B,6BAAI;AAC/B,oBAAoB,6BAAI;AACxB,8EAA8E,6BAAI;AAClF,0BAA0B,6BAAI;AAC9B,eAAe,6BAAI;AACnB,eAAe,6BAAI;AACnB,CAAC;AACD;AACA,sCAAsC,6BAAO;;AAE7C;AACA;AACA,+CAA+C;AAC/C;AACA;AACA,0BAA0B,wBAAwB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,yBAAyB;AACnC,eAAe,oCAAU;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,KAAK;AACL,aAAa;AACb;;AAEA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,qBAAqB,6BAAI;AAC9C,SAAS;AACT;AACA,mBAAmB,6BAAI;AACvB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,iBAAiB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,4BAA4B;AACtC,UAAU,8BAA8B,mBAAmB,6BAAI,YAAY;AAC3E;AACA,UAAU,0BAA0B,6BAAI,UAAU;AAClD,UAAU,+BAA+B,6BAAI,wBAAwB;AACrE;AACA;AACA;AACA,4BAA4B,2DAA2D;AACvF;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,kCAAkC,6BAAI,OAAO;AACvD,UAAU,2BAA2B,6BAAI;AACzC;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,QAAQ;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,2BAA2B,oCAAoC;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,YAAY;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,4BAA4B,6BAAI,SAAS,6BAAI,WAAW;AAClE,UAAU,gCAAgC,6BAAI;AAC9C;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,0BAA0B,6BAAI,SAAS,6BAAI,WAAW;AAChE,UAAU,8BAA8B,6BAAI;AAC5C;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,oBAAoB,sBAAsB,6BAAI,YAAY;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEkL;;;;;ACjjE9F;AAC1C;AACoK;AACjJ;AAC2C;AACrC;AAC1B;;AAEzC,0BAA0B,+CAAmB,GAAG,iBAAiB,SAAS,gCAAgC;AAC1G,qCAAqC,8BAAQ;AAC7C,gCAAgC,MAAM;AACtC;AACA,qBAAqB,oCAAY;AACjC;AACA,sCAAsC,mDAAmD;AACzF,SAAS;AACT;AACA,qBAAqB,sCAAc;AACnC;AACA,SAAS;AACT,qBAAqB,wCAAgB;AACrC;AACA,SAAS;AACT;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,mCAAW;AAC7C,oBAAoB,sCAAU,+BAA+B,MAAM;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,CAAC;AACD;AACA,eAAe,gCAAQ;AACvB;AACA;AACA;AACA;AACA;AACA,oDAAoD,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK;AACtF;AACA,yBAAyB,oCAAY;AACrC,2CAA2C,mDAAmD;AAC9F,aAAa;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,2CAAmB;AAC3C,iCAAiC,2CAAmB;AACpD,uEAAuE,oCAAY;AACnF;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF,OAAO;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA,mCAAmC,QAAQ;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,IAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,+FAA+F;AAC9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,kCAAU;AACxC;AACA,cAAc,yBAAW;AACzB;AACA,sBAAsB,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,uCAAuC,iBAAiB;AACxD,eAAe,sCAAU,WAAW,MAAM;AAC1C;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,8BAA8B;AAC/D;AACA;AACA;AACA;AACA,yBAAyB,OAAO,yBAAe;AAC/C;AACA,mBAAmB;AACnB;AACA,yBAAyB,OAAO,yBAAe;AAC/C,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,qDAAqD;AACpG,sBAAsB,2CAA2C;AACjE,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,QAAQ;AAC5D;AACA,+CAA+C,yBAAW;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,iDAAiD;AACxE,iBAAiB,OAAO,yBAAe;AACvC,KAAK;AACL;AACA;AACA,qCAAqC,0CAA0C;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,QAAQ;AACpD;AACA,cAAc,yBAAW;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,8BAA8B;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gCAAgC,iBAAiB;AACjD,eAAe,sCAAU;AACzB;AACA,gCAAgC,MAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,OAAO,yBAAe;AACnD,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,yBAAW,2BAA2B,yBAAW;AAClG;AACA;AACA,iCAAiC,OAAO,yBAAe;AACvD,uCAAuC;AACvC;AACA;AACA;AACA,iCAAiC,OAAO,yBAAe,2BAA2B;AAClF;AACA;AACA;AACA,wBAAwB;AACxB,KAAK;AACL;AACA;AACA,qCAAqC,2CAA2C;AAChF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,gDAAgD;AACtD,MAAM;AACN;AACA,iCAAiC,uBAAI,GAAG,yBAAyB;AACjE;AACA;AACA;AACA,6BAA6B;AAC7B,UAAU,8DAA8D,SAAS,+EAA+E;AAChK,4BAA4B,cAAc;AAC1C;AACA;AACA;AACA,uCAAuC,uCAAe;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,SAAS,GAAG,yDAAyD;AACzF;AACA,qBAAqB,cAAI,MAAM,qBAAM;AACrC;AACA;AACA,oCAAoC,iCAAiC;AACrE,eAAe,uCAAe;AAC9B;AACA;AACA,UAAU,aAAa;AACvB;AACA;AACA,eAAe,sCAAU;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,uCAAoB,KAAK,8BAAiB,CAAC,qBAAW,UAAU,yBAAyB;AAC1G;AACA;;AAE6H", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@lezer/markdown/dist/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/lang-markdown/dist/index.js"], "sourcesContent": ["import { NodeType, NodeProp, NodeSet, Tree, Parser, parseMixed } from '@lezer/common';\nimport { styleTags, tags, Tag } from '@lezer/highlight';\n\nclass CompositeBlock {\n    static create(type, value, from, parentHash, end) {\n        let hash = (parentHash + (parentHash << 8) + type + (value << 4)) | 0;\n        return new CompositeBlock(type, value, from, hash, end, [], []);\n    }\n    constructor(type, \n    // Used for indentation in list items, markup character in lists\n    value, from, hash, end, children, positions) {\n        this.type = type;\n        this.value = value;\n        this.from = from;\n        this.hash = hash;\n        this.end = end;\n        this.children = children;\n        this.positions = positions;\n        this.hashProp = [[NodeProp.contextHash, hash]];\n    }\n    addChild(child, pos) {\n        if (child.prop(NodeProp.contextHash) != this.hash)\n            child = new Tree(child.type, child.children, child.positions, child.length, this.hashProp);\n        this.children.push(child);\n        this.positions.push(pos);\n    }\n    toTree(nodeSet, end = this.end) {\n        let last = this.children.length - 1;\n        if (last >= 0)\n            end = Math.max(end, this.positions[last] + this.children[last].length + this.from);\n        return new Tree(nodeSet.types[this.type], this.children, this.positions, end - this.from).balance({\n            makeTree: (children, positions, length) => new Tree(NodeType.none, children, positions, length, this.hashProp)\n        });\n    }\n}\nvar Type;\n(function (Type) {\n    Type[Type[\"Document\"] = 1] = \"Document\";\n    Type[Type[\"CodeBlock\"] = 2] = \"CodeBlock\";\n    Type[Type[\"FencedCode\"] = 3] = \"FencedCode\";\n    Type[Type[\"Blockquote\"] = 4] = \"Blockquote\";\n    Type[Type[\"HorizontalRule\"] = 5] = \"HorizontalRule\";\n    Type[Type[\"BulletList\"] = 6] = \"BulletList\";\n    Type[Type[\"OrderedList\"] = 7] = \"OrderedList\";\n    Type[Type[\"ListItem\"] = 8] = \"ListItem\";\n    Type[Type[\"ATXHeading1\"] = 9] = \"ATXHeading1\";\n    Type[Type[\"ATXHeading2\"] = 10] = \"ATXHeading2\";\n    Type[Type[\"ATXHeading3\"] = 11] = \"ATXHeading3\";\n    Type[Type[\"ATXHeading4\"] = 12] = \"ATXHeading4\";\n    Type[Type[\"ATXHeading5\"] = 13] = \"ATXHeading5\";\n    Type[Type[\"ATXHeading6\"] = 14] = \"ATXHeading6\";\n    Type[Type[\"SetextHeading1\"] = 15] = \"SetextHeading1\";\n    Type[Type[\"SetextHeading2\"] = 16] = \"SetextHeading2\";\n    Type[Type[\"HTMLBlock\"] = 17] = \"HTMLBlock\";\n    Type[Type[\"LinkReference\"] = 18] = \"LinkReference\";\n    Type[Type[\"Paragraph\"] = 19] = \"Paragraph\";\n    Type[Type[\"CommentBlock\"] = 20] = \"CommentBlock\";\n    Type[Type[\"ProcessingInstructionBlock\"] = 21] = \"ProcessingInstructionBlock\";\n    // Inline\n    Type[Type[\"Escape\"] = 22] = \"Escape\";\n    Type[Type[\"Entity\"] = 23] = \"Entity\";\n    Type[Type[\"HardBreak\"] = 24] = \"HardBreak\";\n    Type[Type[\"Emphasis\"] = 25] = \"Emphasis\";\n    Type[Type[\"StrongEmphasis\"] = 26] = \"StrongEmphasis\";\n    Type[Type[\"Link\"] = 27] = \"Link\";\n    Type[Type[\"Image\"] = 28] = \"Image\";\n    Type[Type[\"InlineCode\"] = 29] = \"InlineCode\";\n    Type[Type[\"HTMLTag\"] = 30] = \"HTMLTag\";\n    Type[Type[\"Comment\"] = 31] = \"Comment\";\n    Type[Type[\"ProcessingInstruction\"] = 32] = \"ProcessingInstruction\";\n    Type[Type[\"Autolink\"] = 33] = \"Autolink\";\n    // Smaller tokens\n    Type[Type[\"HeaderMark\"] = 34] = \"HeaderMark\";\n    Type[Type[\"QuoteMark\"] = 35] = \"QuoteMark\";\n    Type[Type[\"ListMark\"] = 36] = \"ListMark\";\n    Type[Type[\"LinkMark\"] = 37] = \"LinkMark\";\n    Type[Type[\"EmphasisMark\"] = 38] = \"EmphasisMark\";\n    Type[Type[\"CodeMark\"] = 39] = \"CodeMark\";\n    Type[Type[\"CodeText\"] = 40] = \"CodeText\";\n    Type[Type[\"CodeInfo\"] = 41] = \"CodeInfo\";\n    Type[Type[\"LinkTitle\"] = 42] = \"LinkTitle\";\n    Type[Type[\"LinkLabel\"] = 43] = \"LinkLabel\";\n    Type[Type[\"URL\"] = 44] = \"URL\";\n})(Type || (Type = {}));\n/// Data structure used to accumulate a block's content during [leaf\n/// block parsing](#BlockParser.leaf).\nclass LeafBlock {\n    /// @internal\n    constructor(\n    /// The start position of the block.\n    start, \n    /// The block's text content.\n    content) {\n        this.start = start;\n        this.content = content;\n        /// @internal\n        this.marks = [];\n        /// The block parsers active for this block.\n        this.parsers = [];\n    }\n}\n/// Data structure used during block-level per-line parsing.\nclass Line {\n    constructor() {\n        /// The line's full text.\n        this.text = \"\";\n        /// The base indent provided by the composite contexts (that have\n        /// been handled so far).\n        this.baseIndent = 0;\n        /// The string position corresponding to the base indent.\n        this.basePos = 0;\n        /// The number of contexts handled @internal\n        this.depth = 0;\n        /// Any markers (i.e. block quote markers) parsed for the contexts. @internal\n        this.markers = [];\n        /// The position of the next non-whitespace character beyond any\n        /// list, blockquote, or other composite block markers.\n        this.pos = 0;\n        /// The column of the next non-whitespace character.\n        this.indent = 0;\n        /// The character code of the character after `pos`.\n        this.next = -1;\n    }\n    /// @internal\n    forward() {\n        if (this.basePos > this.pos)\n            this.forwardInner();\n    }\n    /// @internal\n    forwardInner() {\n        let newPos = this.skipSpace(this.basePos);\n        this.indent = this.countIndent(newPos, this.pos, this.indent);\n        this.pos = newPos;\n        this.next = newPos == this.text.length ? -1 : this.text.charCodeAt(newPos);\n    }\n    /// Skip whitespace after the given position, return the position of\n    /// the next non-space character or the end of the line if there's\n    /// only space after `from`.\n    skipSpace(from) { return skipSpace(this.text, from); }\n    /// @internal\n    reset(text) {\n        this.text = text;\n        this.baseIndent = this.basePos = this.pos = this.indent = 0;\n        this.forwardInner();\n        this.depth = 1;\n        while (this.markers.length)\n            this.markers.pop();\n    }\n    /// Move the line's base position forward to the given position.\n    /// This should only be called by composite [block\n    /// parsers](#BlockParser.parse) or [markup skipping\n    /// functions](#NodeSpec.composite).\n    moveBase(to) {\n        this.basePos = to;\n        this.baseIndent = this.countIndent(to, this.pos, this.indent);\n    }\n    /// Move the line's base position forward to the given _column_.\n    moveBaseColumn(indent) {\n        this.baseIndent = indent;\n        this.basePos = this.findColumn(indent);\n    }\n    /// Store a composite-block-level marker. Should be called from\n    /// [markup skipping functions](#NodeSpec.composite) when they\n    /// consume any non-whitespace characters.\n    addMarker(elt) {\n        this.markers.push(elt);\n    }\n    /// Find the column position at `to`, optionally starting at a given\n    /// position and column.\n    countIndent(to, from = 0, indent = 0) {\n        for (let i = from; i < to; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return indent;\n    }\n    /// Find the position corresponding to the given column.\n    findColumn(goal) {\n        let i = 0;\n        for (let indent = 0; i < this.text.length && indent < goal; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return i;\n    }\n    /// @internal\n    scrub() {\n        if (!this.baseIndent)\n            return this.text;\n        let result = \"\";\n        for (let i = 0; i < this.basePos; i++)\n            result += \" \";\n        return result + this.text.slice(this.basePos);\n    }\n}\nfunction skipForList(bl, cx, line) {\n    if (line.pos == line.text.length ||\n        (bl != cx.block && line.indent >= cx.stack[line.depth + 1].value + line.baseIndent))\n        return true;\n    if (line.indent >= line.baseIndent + 4)\n        return false;\n    let size = (bl.type == Type.OrderedList ? isOrderedList : isBulletList)(line, cx, false);\n    return size > 0 &&\n        (bl.type != Type.BulletList || isHorizontalRule(line, cx, false) < 0) &&\n        line.text.charCodeAt(line.pos + size - 1) == bl.value;\n}\nconst DefaultSkipMarkup = {\n    [Type.Blockquote](bl, cx, line) {\n        if (line.next != 62 /* '>' */)\n            return false;\n        line.markers.push(elt(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1));\n        line.moveBase(line.pos + (space(line.text.charCodeAt(line.pos + 1)) ? 2 : 1));\n        bl.end = cx.lineStart + line.text.length;\n        return true;\n    },\n    [Type.ListItem](bl, _cx, line) {\n        if (line.indent < line.baseIndent + bl.value && line.next > -1)\n            return false;\n        line.moveBaseColumn(line.baseIndent + bl.value);\n        return true;\n    },\n    [Type.OrderedList]: skipForList,\n    [Type.BulletList]: skipForList,\n    [Type.Document]() { return true; }\n};\nfunction space(ch) { return ch == 32 || ch == 9 || ch == 10 || ch == 13; }\nfunction skipSpace(line, i = 0) {\n    while (i < line.length && space(line.charCodeAt(i)))\n        i++;\n    return i;\n}\nfunction skipSpaceBack(line, i, to) {\n    while (i > to && space(line.charCodeAt(i - 1)))\n        i--;\n    return i;\n}\nfunction isFencedCode(line) {\n    if (line.next != 96 && line.next != 126 /* '`~' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    if (pos < line.pos + 3)\n        return -1;\n    if (line.next == 96)\n        for (let i = pos; i < line.text.length; i++)\n            if (line.text.charCodeAt(i) == 96)\n                return -1;\n    return pos;\n}\nfunction isBlockquote(line) {\n    return line.next != 62 /* '>' */ ? -1 : line.text.charCodeAt(line.pos + 1) == 32 ? 2 : 1;\n}\nfunction isHorizontalRule(line, cx, breaking) {\n    if (line.next != 42 && line.next != 45 && line.next != 95 /* '_-*' */)\n        return -1;\n    let count = 1;\n    for (let pos = line.pos + 1; pos < line.text.length; pos++) {\n        let ch = line.text.charCodeAt(pos);\n        if (ch == line.next)\n            count++;\n        else if (!space(ch))\n            return -1;\n    }\n    // Setext headers take precedence\n    if (breaking && line.next == 45 && isSetextUnderline(line) > -1 && line.depth == cx.stack.length)\n        return -1;\n    return count < 3 ? -1 : 1;\n}\nfunction inList(cx, type) {\n    for (let i = cx.stack.length - 1; i >= 0; i--)\n        if (cx.stack[i].type == type)\n            return true;\n    return false;\n}\nfunction isBulletList(line, cx, breaking) {\n    return (line.next == 45 || line.next == 43 || line.next == 42 /* '-+*' */) &&\n        (line.pos == line.text.length - 1 || space(line.text.charCodeAt(line.pos + 1))) &&\n        (!breaking || inList(cx, Type.BulletList) || line.skipSpace(line.pos + 2) < line.text.length) ? 1 : -1;\n}\nfunction isOrderedList(line, cx, breaking) {\n    let pos = line.pos, next = line.next;\n    for (;;) {\n        if (next >= 48 && next <= 57 /* '0-9' */)\n            pos++;\n        else\n            break;\n        if (pos == line.text.length)\n            return -1;\n        next = line.text.charCodeAt(pos);\n    }\n    if (pos == line.pos || pos > line.pos + 9 ||\n        (next != 46 && next != 41 /* '.)' */) ||\n        (pos < line.text.length - 1 && !space(line.text.charCodeAt(pos + 1))) ||\n        breaking && !inList(cx, Type.OrderedList) &&\n            (line.skipSpace(pos + 1) == line.text.length || pos > line.pos + 1 || line.next != 49 /* '1' */))\n        return -1;\n    return pos + 1 - line.pos;\n}\nfunction isAtxHeading(line) {\n    if (line.next != 35 /* '#' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == 35)\n        pos++;\n    if (pos < line.text.length && line.text.charCodeAt(pos) != 32)\n        return -1;\n    let size = pos - line.pos;\n    return size > 6 ? -1 : size;\n}\nfunction isSetextUnderline(line) {\n    if (line.next != 45 && line.next != 61 /* '-=' */ || line.indent >= line.baseIndent + 4)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    let end = pos;\n    while (pos < line.text.length && space(line.text.charCodeAt(pos)))\n        pos++;\n    return pos == line.text.length ? end : -1;\n}\nconst EmptyLine = /^[ \\t]*$/, CommentEnd = /-->/, ProcessingEnd = /\\?>/;\nconst HTMLBlockStyle = [\n    [/^<(?:script|pre|style)(?:\\s|>|$)/i, /<\\/(?:script|pre|style)>/i],\n    [/^\\s*<!--/, CommentEnd],\n    [/^\\s*<\\?/, ProcessingEnd],\n    [/^\\s*<![A-Z]/, />/],\n    [/^\\s*<!\\[CDATA\\[/, /\\]\\]>/],\n    [/^\\s*<\\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\\s|\\/?>|$)/i, EmptyLine],\n    [/^\\s*(?:<\\/[a-z][\\w-]*\\s*>|<[a-z][\\w-]*(\\s+[a-z:_][\\w-.]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*>)\\s*$/i, EmptyLine]\n];\nfunction isHTMLBlock(line, _cx, breaking) {\n    if (line.next != 60 /* '<' */)\n        return -1;\n    let rest = line.text.slice(line.pos);\n    for (let i = 0, e = HTMLBlockStyle.length - (breaking ? 1 : 0); i < e; i++)\n        if (HTMLBlockStyle[i][0].test(rest))\n            return i;\n    return -1;\n}\nfunction getListIndent(line, pos) {\n    let indentAfter = line.countIndent(pos, line.pos, line.indent);\n    let indented = line.countIndent(line.skipSpace(pos), pos, indentAfter);\n    return indented >= indentAfter + 5 ? indentAfter + 1 : indented;\n}\nfunction addCodeText(marks, from, to) {\n    let last = marks.length - 1;\n    if (last >= 0 && marks[last].to == from && marks[last].type == Type.CodeText)\n        marks[last].to = to;\n    else\n        marks.push(elt(Type.CodeText, from, to));\n}\n// Rules for parsing blocks. A return value of false means the rule\n// doesn't apply here, true means it does. When true is returned and\n// `p.line` has been updated, the rule is assumed to have consumed a\n// leaf block. Otherwise, it is assumed to have opened a context.\nconst DefaultBlockParsers = {\n    LinkReference: undefined,\n    IndentedCode(cx, line) {\n        let base = line.baseIndent + 4;\n        if (line.indent < base)\n            return false;\n        let start = line.findColumn(base);\n        let from = cx.lineStart + start, to = cx.lineStart + line.text.length;\n        let marks = [], pendingMarks = [];\n        addCodeText(marks, from, to);\n        while (cx.nextLine() && line.depth >= cx.stack.length) {\n            if (line.pos == line.text.length) { // Empty\n                addCodeText(pendingMarks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    pendingMarks.push(m);\n            }\n            else if (line.indent < base) {\n                break;\n            }\n            else {\n                if (pendingMarks.length) {\n                    for (let m of pendingMarks) {\n                        if (m.type == Type.CodeText)\n                            addCodeText(marks, m.from, m.to);\n                        else\n                            marks.push(m);\n                    }\n                    pendingMarks = [];\n                }\n                addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                to = cx.lineStart + line.text.length;\n                let codeStart = cx.lineStart + line.findColumn(line.baseIndent + 4);\n                if (codeStart < to)\n                    addCodeText(marks, codeStart, to);\n            }\n        }\n        if (pendingMarks.length) {\n            pendingMarks = pendingMarks.filter(m => m.type != Type.CodeText);\n            if (pendingMarks.length)\n                line.markers = pendingMarks.concat(line.markers);\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(Type.CodeBlock, to - from), from);\n        return true;\n    },\n    FencedCode(cx, line) {\n        let fenceEnd = isFencedCode(line);\n        if (fenceEnd < 0)\n            return false;\n        let from = cx.lineStart + line.pos, ch = line.next, len = fenceEnd - line.pos;\n        let infoFrom = line.skipSpace(fenceEnd), infoTo = skipSpaceBack(line.text, line.text.length, infoFrom);\n        let marks = [elt(Type.CodeMark, from, from + len)];\n        if (infoFrom < infoTo)\n            marks.push(elt(Type.CodeInfo, cx.lineStart + infoFrom, cx.lineStart + infoTo));\n        for (let first = true; cx.nextLine() && line.depth >= cx.stack.length; first = false) {\n            let i = line.pos;\n            if (line.indent - line.baseIndent < 4)\n                while (i < line.text.length && line.text.charCodeAt(i) == ch)\n                    i++;\n            if (i - line.pos >= len && line.skipSpace(i) == line.text.length) {\n                for (let m of line.markers)\n                    marks.push(m);\n                marks.push(elt(Type.CodeMark, cx.lineStart + line.pos, cx.lineStart + i));\n                cx.nextLine();\n                break;\n            }\n            else {\n                if (!first)\n                    addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                let textStart = cx.lineStart + line.basePos, textEnd = cx.lineStart + line.text.length;\n                if (textStart < textEnd)\n                    addCodeText(marks, textStart, textEnd);\n            }\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from)\n            .finish(Type.FencedCode, cx.prevLineEnd() - from), from);\n        return true;\n    },\n    Blockquote(cx, line) {\n        let size = isBlockquote(line);\n        if (size < 0)\n            return false;\n        cx.startContext(Type.Blockquote, line.pos);\n        cx.addNode(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1);\n        line.moveBase(line.pos + size);\n        return null;\n    },\n    HorizontalRule(cx, line) {\n        if (isHorizontalRule(line, cx, false) < 0)\n            return false;\n        let from = cx.lineStart + line.pos;\n        cx.nextLine();\n        cx.addNode(Type.HorizontalRule, from);\n        return true;\n    },\n    BulletList(cx, line) {\n        let size = isBulletList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.BulletList)\n            cx.startContext(Type.BulletList, line.basePos, line.next);\n        let newBase = getListIndent(line, line.pos + 1);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    OrderedList(cx, line) {\n        let size = isOrderedList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.OrderedList)\n            cx.startContext(Type.OrderedList, line.basePos, line.text.charCodeAt(line.pos + size - 1));\n        let newBase = getListIndent(line, line.pos + size);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    ATXHeading(cx, line) {\n        let size = isAtxHeading(line);\n        if (size < 0)\n            return false;\n        let off = line.pos, from = cx.lineStart + off;\n        let endOfSpace = skipSpaceBack(line.text, line.text.length, off), after = endOfSpace;\n        while (after > off && line.text.charCodeAt(after - 1) == line.next)\n            after--;\n        if (after == endOfSpace || after == off || !space(line.text.charCodeAt(after - 1)))\n            after = line.text.length;\n        let buf = cx.buffer\n            .write(Type.HeaderMark, 0, size)\n            .writeElements(cx.parser.parseInline(line.text.slice(off + size + 1, after), from + size + 1), -from);\n        if (after < line.text.length)\n            buf.write(Type.HeaderMark, after - off, endOfSpace - off);\n        let node = buf.finish(Type.ATXHeading1 - 1 + size, line.text.length - off);\n        cx.nextLine();\n        cx.addNode(node, from);\n        return true;\n    },\n    HTMLBlock(cx, line) {\n        let type = isHTMLBlock(line, cx, false);\n        if (type < 0)\n            return false;\n        let from = cx.lineStart + line.pos, end = HTMLBlockStyle[type][1];\n        let marks = [], trailing = end != EmptyLine;\n        while (!end.test(line.text) && cx.nextLine()) {\n            if (line.depth < cx.stack.length) {\n                trailing = false;\n                break;\n            }\n            for (let m of line.markers)\n                marks.push(m);\n        }\n        if (trailing)\n            cx.nextLine();\n        let nodeType = end == CommentEnd ? Type.CommentBlock : end == ProcessingEnd ? Type.ProcessingInstructionBlock : Type.HTMLBlock;\n        let to = cx.prevLineEnd();\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(nodeType, to - from), from);\n        return true;\n    },\n    SetextHeading: undefined // Specifies relative precedence for block-continue function\n};\n// This implements a state machine that incrementally parses link references. At each\n// next line, it looks ahead to see if the line continues the reference or not. If it\n// doesn't and a valid link is available ending before that line, it finishes that.\n// Similarly, on `finish` (when the leaf is terminated by external circumstances), it\n// creates a link reference if there's a valid reference up to the current point.\nclass LinkReferenceParser {\n    constructor(leaf) {\n        this.stage = 0 /* RefStage.Start */;\n        this.elts = [];\n        this.pos = 0;\n        this.start = leaf.start;\n        this.advance(leaf.content);\n    }\n    nextLine(cx, line, leaf) {\n        if (this.stage == -1 /* RefStage.Failed */)\n            return false;\n        let content = leaf.content + \"\\n\" + line.scrub();\n        let finish = this.advance(content);\n        if (finish > -1 && finish < content.length)\n            return this.complete(cx, leaf, finish);\n        return false;\n    }\n    finish(cx, leaf) {\n        if ((this.stage == 2 /* RefStage.Link */ || this.stage == 3 /* RefStage.Title */) && skipSpace(leaf.content, this.pos) == leaf.content.length)\n            return this.complete(cx, leaf, leaf.content.length);\n        return false;\n    }\n    complete(cx, leaf, len) {\n        cx.addLeafElement(leaf, elt(Type.LinkReference, this.start, this.start + len, this.elts));\n        return true;\n    }\n    nextStage(elt) {\n        if (elt) {\n            this.pos = elt.to - this.start;\n            this.elts.push(elt);\n            this.stage++;\n            return true;\n        }\n        if (elt === false)\n            this.stage = -1 /* RefStage.Failed */;\n        return false;\n    }\n    advance(content) {\n        for (;;) {\n            if (this.stage == -1 /* RefStage.Failed */) {\n                return -1;\n            }\n            else if (this.stage == 0 /* RefStage.Start */) {\n                if (!this.nextStage(parseLinkLabel(content, this.pos, this.start, true)))\n                    return -1;\n                if (content.charCodeAt(this.pos) != 58 /* ':' */)\n                    return this.stage = -1 /* RefStage.Failed */;\n                this.elts.push(elt(Type.LinkMark, this.pos + this.start, this.pos + this.start + 1));\n                this.pos++;\n            }\n            else if (this.stage == 1 /* RefStage.Label */) {\n                if (!this.nextStage(parseURL(content, skipSpace(content, this.pos), this.start)))\n                    return -1;\n            }\n            else if (this.stage == 2 /* RefStage.Link */) {\n                let skip = skipSpace(content, this.pos), end = 0;\n                if (skip > this.pos) {\n                    let title = parseLinkTitle(content, skip, this.start);\n                    if (title) {\n                        let titleEnd = lineEnd(content, title.to - this.start);\n                        if (titleEnd > 0) {\n                            this.nextStage(title);\n                            end = titleEnd;\n                        }\n                    }\n                }\n                if (!end)\n                    end = lineEnd(content, this.pos);\n                return end > 0 && end < content.length ? end : -1;\n            }\n            else { // RefStage.Title\n                return lineEnd(content, this.pos);\n            }\n        }\n    }\n}\nfunction lineEnd(text, pos) {\n    for (; pos < text.length; pos++) {\n        let next = text.charCodeAt(pos);\n        if (next == 10)\n            break;\n        if (!space(next))\n            return -1;\n    }\n    return pos;\n}\nclass SetextHeadingParser {\n    nextLine(cx, line, leaf) {\n        let underline = line.depth < cx.stack.length ? -1 : isSetextUnderline(line);\n        let next = line.next;\n        if (underline < 0)\n            return false;\n        let underlineMark = elt(Type.HeaderMark, cx.lineStart + line.pos, cx.lineStart + underline);\n        cx.nextLine();\n        cx.addLeafElement(leaf, elt(next == 61 ? Type.SetextHeading1 : Type.SetextHeading2, leaf.start, cx.prevLineEnd(), [\n            ...cx.parser.parseInline(leaf.content, leaf.start),\n            underlineMark\n        ]));\n        return true;\n    }\n    finish() {\n        return false;\n    }\n}\nconst DefaultLeafBlocks = {\n    LinkReference(_, leaf) { return leaf.content.charCodeAt(0) == 91 /* '[' */ ? new LinkReferenceParser(leaf) : null; },\n    SetextHeading() { return new SetextHeadingParser; }\n};\nconst DefaultEndLeaf = [\n    (_, line) => isAtxHeading(line) >= 0,\n    (_, line) => isFencedCode(line) >= 0,\n    (_, line) => isBlockquote(line) >= 0,\n    (p, line) => isBulletList(line, p, true) >= 0,\n    (p, line) => isOrderedList(line, p, true) >= 0,\n    (p, line) => isHorizontalRule(line, p, true) >= 0,\n    (p, line) => isHTMLBlock(line, p, true) >= 0\n];\nconst scanLineResult = { text: \"\", end: 0 };\n/// Block-level parsing functions get access to this context object.\nclass BlockContext {\n    /// @internal\n    constructor(\n    /// The parser configuration used.\n    parser, \n    /// @internal\n    input, fragments, \n    /// @internal\n    ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.line = new Line();\n        this.atEnd = false;\n        /// For reused nodes on gaps, we can't directly put the original\n        /// node into the tree, since that may be bitter than its parent.\n        /// When this happens, we create a dummy tree that is replaced by\n        /// the proper node in `injectGaps` @internal\n        this.reusePlaceholders = new Map;\n        this.stoppedAt = null;\n        /// The range index that absoluteLineStart points into @internal\n        this.rangeI = 0;\n        this.to = ranges[ranges.length - 1].to;\n        this.lineStart = this.absoluteLineStart = this.absoluteLineEnd = ranges[0].from;\n        this.block = CompositeBlock.create(Type.Document, 0, this.lineStart, 0, 0);\n        this.stack = [this.block];\n        this.fragments = fragments.length ? new FragmentCursor(fragments, input) : null;\n        this.readLine();\n    }\n    get parsedPos() {\n        return this.absoluteLineStart;\n    }\n    advance() {\n        if (this.stoppedAt != null && this.absoluteLineStart > this.stoppedAt)\n            return this.finish();\n        let { line } = this;\n        for (;;) {\n            for (let markI = 0;;) {\n                let next = line.depth < this.stack.length ? this.stack[this.stack.length - 1] : null;\n                while (markI < line.markers.length && (!next || line.markers[markI].from < next.end)) {\n                    let mark = line.markers[markI++];\n                    this.addNode(mark.type, mark.from, mark.to);\n                }\n                if (!next)\n                    break;\n                this.finishContext();\n            }\n            if (line.pos < line.text.length)\n                break;\n            // Empty line\n            if (!this.nextLine())\n                return this.finish();\n        }\n        if (this.fragments && this.reuseFragment(line.basePos))\n            return null;\n        start: for (;;) {\n            for (let type of this.parser.blockParsers)\n                if (type) {\n                    let result = type(this, line);\n                    if (result != false) {\n                        if (result == true)\n                            return null;\n                        line.forward();\n                        continue start;\n                    }\n                }\n            break;\n        }\n        let leaf = new LeafBlock(this.lineStart + line.pos, line.text.slice(line.pos));\n        for (let parse of this.parser.leafBlockParsers)\n            if (parse) {\n                let parser = parse(this, leaf);\n                if (parser)\n                    leaf.parsers.push(parser);\n            }\n        lines: while (this.nextLine()) {\n            if (line.pos == line.text.length)\n                break;\n            if (line.indent < line.baseIndent + 4) {\n                for (let stop of this.parser.endLeafBlock)\n                    if (stop(this, line, leaf))\n                        break lines;\n            }\n            for (let parser of leaf.parsers)\n                if (parser.nextLine(this, line, leaf))\n                    return null;\n            leaf.content += \"\\n\" + line.scrub();\n            for (let m of line.markers)\n                leaf.marks.push(m);\n        }\n        this.finishLeaf(leaf);\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    reuseFragment(start) {\n        if (!this.fragments.moveTo(this.absoluteLineStart + start, this.absoluteLineStart) ||\n            !this.fragments.matches(this.block.hash))\n            return false;\n        let taken = this.fragments.takeNodes(this);\n        if (!taken)\n            return false;\n        this.absoluteLineStart += taken;\n        this.lineStart = toRelative(this.absoluteLineStart, this.ranges);\n        this.moveRangeI();\n        if (this.absoluteLineStart < this.to) {\n            this.lineStart++;\n            this.absoluteLineStart++;\n            this.readLine();\n        }\n        else {\n            this.atEnd = true;\n            this.readLine();\n        }\n        return true;\n    }\n    /// The number of parent blocks surrounding the current block.\n    get depth() {\n        return this.stack.length;\n    }\n    /// Get the type of the parent block at the given depth. When no\n    /// depth is passed, return the type of the innermost parent.\n    parentType(depth = this.depth - 1) {\n        return this.parser.nodeSet.types[this.stack[depth].type];\n    }\n    /// Move to the next input line. This should only be called by\n    /// (non-composite) [block parsers](#BlockParser.parse) that consume\n    /// the line directly, or leaf block parser\n    /// [`nextLine`](#LeafBlockParser.nextLine) methods when they\n    /// consume the current line (and return true).\n    nextLine() {\n        this.lineStart += this.line.text.length;\n        if (this.absoluteLineEnd >= this.to) {\n            this.absoluteLineStart = this.absoluteLineEnd;\n            this.atEnd = true;\n            this.readLine();\n            return false;\n        }\n        else {\n            this.lineStart++;\n            this.absoluteLineStart = this.absoluteLineEnd + 1;\n            this.moveRangeI();\n            this.readLine();\n            return true;\n        }\n    }\n    moveRangeI() {\n        while (this.rangeI < this.ranges.length - 1 && this.absoluteLineStart >= this.ranges[this.rangeI].to) {\n            this.rangeI++;\n            this.absoluteLineStart = Math.max(this.absoluteLineStart, this.ranges[this.rangeI].from);\n        }\n    }\n    /// @internal\n    scanLine(start) {\n        let r = scanLineResult;\n        r.end = start;\n        if (start >= this.to) {\n            r.text = \"\";\n        }\n        else {\n            r.text = this.lineChunkAt(start);\n            r.end += r.text.length;\n            if (this.ranges.length > 1) {\n                let textOffset = this.absoluteLineStart, rangeI = this.rangeI;\n                while (this.ranges[rangeI].to < r.end) {\n                    rangeI++;\n                    let nextFrom = this.ranges[rangeI].from;\n                    let after = this.lineChunkAt(nextFrom);\n                    r.end = nextFrom + after.length;\n                    r.text = r.text.slice(0, this.ranges[rangeI - 1].to - textOffset) + after;\n                    textOffset = r.end - r.text.length;\n                }\n            }\n        }\n        return r;\n    }\n    /// @internal\n    readLine() {\n        let { line } = this, { text, end } = this.scanLine(this.absoluteLineStart);\n        this.absoluteLineEnd = end;\n        line.reset(text);\n        for (; line.depth < this.stack.length; line.depth++) {\n            let cx = this.stack[line.depth], handler = this.parser.skipContextMarkup[cx.type];\n            if (!handler)\n                throw new Error(\"Unhandled block context \" + Type[cx.type]);\n            if (!handler(cx, this, line))\n                break;\n            line.forward();\n        }\n    }\n    lineChunkAt(pos) {\n        let next = this.input.chunk(pos), text;\n        if (!this.input.lineChunks) {\n            let eol = next.indexOf(\"\\n\");\n            text = eol < 0 ? next : next.slice(0, eol);\n        }\n        else {\n            text = next == \"\\n\" ? \"\" : next;\n        }\n        return pos + text.length > this.to ? text.slice(0, this.to - pos) : text;\n    }\n    /// The end position of the previous line.\n    prevLineEnd() { return this.atEnd ? this.lineStart : this.lineStart - 1; }\n    /// @internal\n    startContext(type, start, value = 0) {\n        this.block = CompositeBlock.create(type, value, this.lineStart + start, this.block.hash, this.lineStart + this.line.text.length);\n        this.stack.push(this.block);\n    }\n    /// Start a composite block. Should only be called from [block\n    /// parser functions](#BlockParser.parse) that return null.\n    startComposite(type, start, value = 0) {\n        this.startContext(this.parser.getNodeType(type), start, value);\n    }\n    /// @internal\n    addNode(block, from, to) {\n        if (typeof block == \"number\")\n            block = new Tree(this.parser.nodeSet.types[block], none, none, (to !== null && to !== void 0 ? to : this.prevLineEnd()) - from);\n        this.block.addChild(block, from - this.block.from);\n    }\n    /// Add a block element. Can be called by [block\n    /// parsers](#BlockParser.parse).\n    addElement(elt) {\n        this.block.addChild(elt.toTree(this.parser.nodeSet), elt.from - this.block.from);\n    }\n    /// Add a block element from a [leaf parser](#LeafBlockParser). This\n    /// makes sure any extra composite block markup (such as blockquote\n    /// markers) inside the block are also added to the syntax tree.\n    addLeafElement(leaf, elt) {\n        this.addNode(this.buffer\n            .writeElements(injectMarks(elt.children, leaf.marks), -elt.from)\n            .finish(elt.type, elt.to - elt.from), elt.from);\n    }\n    /// @internal\n    finishContext() {\n        let cx = this.stack.pop();\n        let top = this.stack[this.stack.length - 1];\n        top.addChild(cx.toTree(this.parser.nodeSet), cx.from - top.from);\n        this.block = top;\n    }\n    finish() {\n        while (this.stack.length > 1)\n            this.finishContext();\n        return this.addGaps(this.block.toTree(this.parser.nodeSet, this.lineStart));\n    }\n    addGaps(tree) {\n        return this.ranges.length > 1 ?\n            injectGaps(this.ranges, 0, tree.topNode, this.ranges[0].from, this.reusePlaceholders) : tree;\n    }\n    /// @internal\n    finishLeaf(leaf) {\n        for (let parser of leaf.parsers)\n            if (parser.finish(this, leaf))\n                return;\n        let inline = injectMarks(this.parser.parseInline(leaf.content, leaf.start), leaf.marks);\n        this.addNode(this.buffer\n            .writeElements(inline, -leaf.start)\n            .finish(Type.Paragraph, leaf.content.length), leaf.start);\n    }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n    /// @internal\n    get buffer() { return new Buffer(this.parser.nodeSet); }\n}\nfunction injectGaps(ranges, rangeI, tree, offset, dummies) {\n    let rangeEnd = ranges[rangeI].to;\n    let children = [], positions = [], start = tree.from + offset;\n    function movePastNext(upto, inclusive) {\n        while (inclusive ? upto >= rangeEnd : upto > rangeEnd) {\n            let size = ranges[rangeI + 1].from - rangeEnd;\n            offset += size;\n            upto += size;\n            rangeI++;\n            rangeEnd = ranges[rangeI].to;\n        }\n    }\n    for (let ch = tree.firstChild; ch; ch = ch.nextSibling) {\n        movePastNext(ch.from + offset, true);\n        let from = ch.from + offset, node, reuse = dummies.get(ch.tree);\n        if (reuse) {\n            node = reuse;\n        }\n        else if (ch.to + offset > rangeEnd) {\n            node = injectGaps(ranges, rangeI, ch, offset, dummies);\n            movePastNext(ch.to + offset, false);\n        }\n        else {\n            node = ch.toTree();\n        }\n        children.push(node);\n        positions.push(from - start);\n    }\n    movePastNext(tree.to + offset, false);\n    return new Tree(tree.type, children, positions, tree.to + offset - start, tree.tree ? tree.tree.propValues : undefined);\n}\n/// A Markdown parser configuration.\nclass MarkdownParser extends Parser {\n    /// @internal\n    constructor(\n    /// The parser's syntax [node\n    /// types](https://lezer.codemirror.net/docs/ref/#common.NodeSet).\n    nodeSet, \n    /// @internal\n    blockParsers, \n    /// @internal\n    leafBlockParsers, \n    /// @internal\n    blockNames, \n    /// @internal\n    endLeafBlock, \n    /// @internal\n    skipContextMarkup, \n    /// @internal\n    inlineParsers, \n    /// @internal\n    inlineNames, \n    /// @internal\n    wrappers) {\n        super();\n        this.nodeSet = nodeSet;\n        this.blockParsers = blockParsers;\n        this.leafBlockParsers = leafBlockParsers;\n        this.blockNames = blockNames;\n        this.endLeafBlock = endLeafBlock;\n        this.skipContextMarkup = skipContextMarkup;\n        this.inlineParsers = inlineParsers;\n        this.inlineNames = inlineNames;\n        this.wrappers = wrappers;\n        /// @internal\n        this.nodeTypes = Object.create(null);\n        for (let t of nodeSet.types)\n            this.nodeTypes[t.name] = t.id;\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new BlockContext(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /// Reconfigure the parser.\n    configure(spec) {\n        let config = resolveConfig(spec);\n        if (!config)\n            return this;\n        let { nodeSet, skipContextMarkup } = this;\n        let blockParsers = this.blockParsers.slice(), leafBlockParsers = this.leafBlockParsers.slice(), blockNames = this.blockNames.slice(), inlineParsers = this.inlineParsers.slice(), inlineNames = this.inlineNames.slice(), endLeafBlock = this.endLeafBlock.slice(), wrappers = this.wrappers;\n        if (nonEmpty(config.defineNodes)) {\n            skipContextMarkup = Object.assign({}, skipContextMarkup);\n            let nodeTypes = nodeSet.types.slice(), styles;\n            for (let s of config.defineNodes) {\n                let { name, block, composite, style } = typeof s == \"string\" ? { name: s } : s;\n                if (nodeTypes.some(t => t.name == name))\n                    continue;\n                if (composite)\n                    skipContextMarkup[nodeTypes.length] =\n                        (bl, cx, line) => composite(cx, line, bl.value);\n                let id = nodeTypes.length;\n                let group = composite ? [\"Block\", \"BlockContext\"] : !block ? undefined\n                    : id >= Type.ATXHeading1 && id <= Type.SetextHeading2 ? [\"Block\", \"LeafBlock\", \"Heading\"] : [\"Block\", \"LeafBlock\"];\n                nodeTypes.push(NodeType.define({\n                    id,\n                    name,\n                    props: group && [[NodeProp.group, group]]\n                }));\n                if (style) {\n                    if (!styles)\n                        styles = {};\n                    if (Array.isArray(style) || style instanceof Tag)\n                        styles[name] = style;\n                    else\n                        Object.assign(styles, style);\n                }\n            }\n            nodeSet = new NodeSet(nodeTypes);\n            if (styles)\n                nodeSet = nodeSet.extend(styleTags(styles));\n        }\n        if (nonEmpty(config.props))\n            nodeSet = nodeSet.extend(...config.props);\n        if (nonEmpty(config.remove)) {\n            for (let rm of config.remove) {\n                let block = this.blockNames.indexOf(rm), inline = this.inlineNames.indexOf(rm);\n                if (block > -1)\n                    blockParsers[block] = leafBlockParsers[block] = undefined;\n                if (inline > -1)\n                    inlineParsers[inline] = undefined;\n            }\n        }\n        if (nonEmpty(config.parseBlock)) {\n            for (let spec of config.parseBlock) {\n                let found = blockNames.indexOf(spec.name);\n                if (found > -1) {\n                    blockParsers[found] = spec.parse;\n                    leafBlockParsers[found] = spec.leaf;\n                }\n                else {\n                    let pos = spec.before ? findName(blockNames, spec.before)\n                        : spec.after ? findName(blockNames, spec.after) + 1 : blockNames.length - 1;\n                    blockParsers.splice(pos, 0, spec.parse);\n                    leafBlockParsers.splice(pos, 0, spec.leaf);\n                    blockNames.splice(pos, 0, spec.name);\n                }\n                if (spec.endLeaf)\n                    endLeafBlock.push(spec.endLeaf);\n            }\n        }\n        if (nonEmpty(config.parseInline)) {\n            for (let spec of config.parseInline) {\n                let found = inlineNames.indexOf(spec.name);\n                if (found > -1) {\n                    inlineParsers[found] = spec.parse;\n                }\n                else {\n                    let pos = spec.before ? findName(inlineNames, spec.before)\n                        : spec.after ? findName(inlineNames, spec.after) + 1 : inlineNames.length - 1;\n                    inlineParsers.splice(pos, 0, spec.parse);\n                    inlineNames.splice(pos, 0, spec.name);\n                }\n            }\n        }\n        if (config.wrap)\n            wrappers = wrappers.concat(config.wrap);\n        return new MarkdownParser(nodeSet, blockParsers, leafBlockParsers, blockNames, endLeafBlock, skipContextMarkup, inlineParsers, inlineNames, wrappers);\n    }\n    /// @internal\n    getNodeType(name) {\n        let found = this.nodeTypes[name];\n        if (found == null)\n            throw new RangeError(`Unknown node type '${name}'`);\n        return found;\n    }\n    /// Parse the given piece of inline text at the given offset,\n    /// returning an array of [`Element`](#Element) objects representing\n    /// the inline content.\n    parseInline(text, offset) {\n        let cx = new InlineContext(this, text, offset);\n        outer: for (let pos = offset; pos < cx.end;) {\n            let next = cx.char(pos);\n            for (let token of this.inlineParsers)\n                if (token) {\n                    let result = token(cx, next, pos);\n                    if (result >= 0) {\n                        pos = result;\n                        continue outer;\n                    }\n                }\n            pos++;\n        }\n        return cx.resolveMarkers(0);\n    }\n}\nfunction nonEmpty(a) {\n    return a != null && a.length > 0;\n}\nfunction resolveConfig(spec) {\n    if (!Array.isArray(spec))\n        return spec;\n    if (spec.length == 0)\n        return null;\n    let conf = resolveConfig(spec[0]);\n    if (spec.length == 1)\n        return conf;\n    let rest = resolveConfig(spec.slice(1));\n    if (!rest || !conf)\n        return conf || rest;\n    let conc = (a, b) => (a || none).concat(b || none);\n    let wrapA = conf.wrap, wrapB = rest.wrap;\n    return {\n        props: conc(conf.props, rest.props),\n        defineNodes: conc(conf.defineNodes, rest.defineNodes),\n        parseBlock: conc(conf.parseBlock, rest.parseBlock),\n        parseInline: conc(conf.parseInline, rest.parseInline),\n        remove: conc(conf.remove, rest.remove),\n        wrap: !wrapA ? wrapB : !wrapB ? wrapA :\n            (inner, input, fragments, ranges) => wrapA(wrapB(inner, input, fragments, ranges), input, fragments, ranges)\n    };\n}\nfunction findName(names, name) {\n    let found = names.indexOf(name);\n    if (found < 0)\n        throw new RangeError(`Position specified relative to unknown parser ${name}`);\n    return found;\n}\nlet nodeTypes = [NodeType.none];\nfor (let i = 1, name; name = Type[i]; i++) {\n    nodeTypes[i] = NodeType.define({\n        id: i,\n        name,\n        props: i >= Type.Escape ? [] : [[NodeProp.group, i in DefaultSkipMarkup ? [\"Block\", \"BlockContext\"] : [\"Block\", \"LeafBlock\"]]],\n        top: name == \"Document\"\n    });\n}\nconst none = [];\nclass Buffer {\n    constructor(nodeSet) {\n        this.nodeSet = nodeSet;\n        this.content = [];\n        this.nodes = [];\n    }\n    write(type, from, to, children = 0) {\n        this.content.push(type, from, to, 4 + children * 4);\n        return this;\n    }\n    writeElements(elts, offset = 0) {\n        for (let e of elts)\n            e.writeTo(this, offset);\n        return this;\n    }\n    finish(type, length) {\n        return Tree.build({\n            buffer: this.content,\n            nodeSet: this.nodeSet,\n            reused: this.nodes,\n            topID: type,\n            length\n        });\n    }\n}\n/// Elements are used to compose syntax nodes during parsing.\nclass Element {\n    /// @internal\n    constructor(\n    /// The node's\n    /// [id](https://lezer.codemirror.net/docs/ref/#common.NodeType.id).\n    type, \n    /// The start of the node, as an offset from the start of the document.\n    from, \n    /// The end of the node.\n    to, \n    /// The node's child nodes @internal\n    children = none) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.children = children;\n    }\n    /// @internal\n    writeTo(buf, offset) {\n        let startOff = buf.content.length;\n        buf.writeElements(this.children, offset);\n        buf.content.push(this.type, this.from + offset, this.to + offset, buf.content.length + 4 - startOff);\n    }\n    /// @internal\n    toTree(nodeSet) {\n        return new Buffer(nodeSet).writeElements(this.children, -this.from).finish(this.type, this.to - this.from);\n    }\n}\nclass TreeElement {\n    constructor(tree, from) {\n        this.tree = tree;\n        this.from = from;\n    }\n    get to() { return this.from + this.tree.length; }\n    get type() { return this.tree.type.id; }\n    get children() { return none; }\n    writeTo(buf, offset) {\n        buf.nodes.push(this.tree);\n        buf.content.push(buf.nodes.length - 1, this.from + offset, this.to + offset, -1);\n    }\n    toTree() { return this.tree; }\n}\nfunction elt(type, from, to, children) {\n    return new Element(type, from, to, children);\n}\nconst EmphasisUnderscore = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst EmphasisAsterisk = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst LinkStart = {}, ImageStart = {};\nclass InlineDelimiter {\n    constructor(type, from, to, side) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.side = side;\n    }\n}\nconst Escapable = \"!\\\"#$%&'()*+,-./:;<=>?@[\\\\]^_`{|}~\";\nlet Punctuation = /[!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~\\xA1\\u2010-\\u2027]/;\ntry {\n    Punctuation = new RegExp(\"[\\\\p{Pc}|\\\\p{Pd}|\\\\p{Pe}|\\\\p{Pf}|\\\\p{Pi}|\\\\p{Po}|\\\\p{Ps}]\", \"u\");\n}\ncatch (_) { }\nconst DefaultInline = {\n    Escape(cx, next, start) {\n        if (next != 92 /* '\\\\' */ || start == cx.end - 1)\n            return -1;\n        let escaped = cx.char(start + 1);\n        for (let i = 0; i < Escapable.length; i++)\n            if (Escapable.charCodeAt(i) == escaped)\n                return cx.append(elt(Type.Escape, start, start + 2));\n        return -1;\n    },\n    Entity(cx, next, start) {\n        if (next != 38 /* '&' */)\n            return -1;\n        let m = /^(?:#\\d+|#x[a-f\\d]+|\\w+);/i.exec(cx.slice(start + 1, start + 31));\n        return m ? cx.append(elt(Type.Entity, start, start + 1 + m[0].length)) : -1;\n    },\n    InlineCode(cx, next, start) {\n        if (next != 96 /* '`' */ || start && cx.char(start - 1) == 96)\n            return -1;\n        let pos = start + 1;\n        while (pos < cx.end && cx.char(pos) == 96)\n            pos++;\n        let size = pos - start, curSize = 0;\n        for (; pos < cx.end; pos++) {\n            if (cx.char(pos) == 96) {\n                curSize++;\n                if (curSize == size && cx.char(pos + 1) != 96)\n                    return cx.append(elt(Type.InlineCode, start, pos + 1, [\n                        elt(Type.CodeMark, start, start + size),\n                        elt(Type.CodeMark, pos + 1 - size, pos + 1)\n                    ]));\n            }\n            else {\n                curSize = 0;\n            }\n        }\n        return -1;\n    },\n    HTMLTag(cx, next, start) {\n        if (next != 60 /* '<' */ || start == cx.end - 1)\n            return -1;\n        let after = cx.slice(start + 1, cx.end);\n        let url = /^(?:[a-z][-\\w+.]+:[^\\s>]+|[a-z\\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?(?:\\.[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?)*)>/i.exec(after);\n        if (url) {\n            return cx.append(elt(Type.Autolink, start, start + 1 + url[0].length, [\n                elt(Type.LinkMark, start, start + 1),\n                // url[0] includes the closing bracket, so exclude it from this slice\n                elt(Type.URL, start + 1, start + url[0].length),\n                elt(Type.LinkMark, start + url[0].length, start + 1 + url[0].length)\n            ]));\n        }\n        let comment = /^!--[^>](?:-[^-]|[^-])*?-->/i.exec(after);\n        if (comment)\n            return cx.append(elt(Type.Comment, start, start + 1 + comment[0].length));\n        let procInst = /^\\?[^]*?\\?>/.exec(after);\n        if (procInst)\n            return cx.append(elt(Type.ProcessingInstruction, start, start + 1 + procInst[0].length));\n        let m = /^(?:![A-Z][^]*?>|!\\[CDATA\\[[^]*?\\]\\]>|\\/\\s*[a-zA-Z][\\w-]*\\s*>|\\s*[a-zA-Z][\\w-]*(\\s+[a-zA-Z:_][\\w-.:]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*(\\/\\s*)?>)/.exec(after);\n        if (!m)\n            return -1;\n        return cx.append(elt(Type.HTMLTag, start, start + 1 + m[0].length));\n    },\n    Emphasis(cx, next, start) {\n        if (next != 95 && next != 42)\n            return -1;\n        let pos = start + 1;\n        while (cx.char(pos) == next)\n            pos++;\n        let before = cx.slice(start - 1, start), after = cx.slice(pos, pos + 1);\n        let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n        let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n        let leftFlanking = !sAfter && (!pAfter || sBefore || pBefore);\n        let rightFlanking = !sBefore && (!pBefore || sAfter || pAfter);\n        let canOpen = leftFlanking && (next == 42 || !rightFlanking || pBefore);\n        let canClose = rightFlanking && (next == 42 || !leftFlanking || pAfter);\n        return cx.append(new InlineDelimiter(next == 95 ? EmphasisUnderscore : EmphasisAsterisk, start, pos, (canOpen ? 1 /* Mark.Open */ : 0 /* Mark.None */) | (canClose ? 2 /* Mark.Close */ : 0 /* Mark.None */)));\n    },\n    HardBreak(cx, next, start) {\n        if (next == 92 /* '\\\\' */ && cx.char(start + 1) == 10 /* '\\n' */)\n            return cx.append(elt(Type.HardBreak, start, start + 2));\n        if (next == 32) {\n            let pos = start + 1;\n            while (cx.char(pos) == 32)\n                pos++;\n            if (cx.char(pos) == 10 && pos >= start + 2)\n                return cx.append(elt(Type.HardBreak, start, pos + 1));\n        }\n        return -1;\n    },\n    Link(cx, next, start) {\n        return next == 91 /* '[' */ ? cx.append(new InlineDelimiter(LinkStart, start, start + 1, 1 /* Mark.Open */)) : -1;\n    },\n    Image(cx, next, start) {\n        return next == 33 /* '!' */ && cx.char(start + 1) == 91 /* '[' */\n            ? cx.append(new InlineDelimiter(ImageStart, start, start + 2, 1 /* Mark.Open */)) : -1;\n    },\n    LinkEnd(cx, next, start) {\n        if (next != 93 /* ']' */)\n            return -1;\n        // Scanning back to the next link/image start marker\n        for (let i = cx.parts.length - 1; i >= 0; i--) {\n            let part = cx.parts[i];\n            if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart)) {\n                // If this one has been set invalid (because it would produce\n                // a nested link) or there's no valid link here ignore both.\n                if (!part.side || cx.skipSpace(part.to) == start && !/[(\\[]/.test(cx.slice(start + 1, start + 2))) {\n                    cx.parts[i] = null;\n                    return -1;\n                }\n                // Finish the content and replace the entire range in\n                // this.parts with the link/image node.\n                let content = cx.takeContent(i);\n                let link = cx.parts[i] = finishLink(cx, content, part.type == LinkStart ? Type.Link : Type.Image, part.from, start + 1);\n                // Set any open-link markers before this link to invalid.\n                if (part.type == LinkStart)\n                    for (let j = 0; j < i; j++) {\n                        let p = cx.parts[j];\n                        if (p instanceof InlineDelimiter && p.type == LinkStart)\n                            p.side = 0 /* Mark.None */;\n                    }\n                return link.to;\n            }\n        }\n        return -1;\n    }\n};\nfunction finishLink(cx, content, type, start, startPos) {\n    let { text } = cx, next = cx.char(startPos), endPos = startPos;\n    content.unshift(elt(Type.LinkMark, start, start + (type == Type.Image ? 2 : 1)));\n    content.push(elt(Type.LinkMark, startPos - 1, startPos));\n    if (next == 40 /* '(' */) {\n        let pos = cx.skipSpace(startPos + 1);\n        let dest = parseURL(text, pos - cx.offset, cx.offset), title;\n        if (dest) {\n            pos = cx.skipSpace(dest.to);\n            // The destination and title must be separated by whitespace\n            if (pos != dest.to) {\n                title = parseLinkTitle(text, pos - cx.offset, cx.offset);\n                if (title)\n                    pos = cx.skipSpace(title.to);\n            }\n        }\n        if (cx.char(pos) == 41 /* ')' */) {\n            content.push(elt(Type.LinkMark, startPos, startPos + 1));\n            endPos = pos + 1;\n            if (dest)\n                content.push(dest);\n            if (title)\n                content.push(title);\n            content.push(elt(Type.LinkMark, pos, endPos));\n        }\n    }\n    else if (next == 91 /* '[' */) {\n        let label = parseLinkLabel(text, startPos - cx.offset, cx.offset, false);\n        if (label) {\n            content.push(label);\n            endPos = label.to;\n        }\n    }\n    return elt(type, start, endPos, content);\n}\n// These return `null` when falling off the end of the input, `false`\n// when parsing fails otherwise (for use in the incremental link\n// reference parser).\nfunction parseURL(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next == 60 /* '<' */) {\n        for (let pos = start + 1; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (ch == 62 /* '>' */)\n                return elt(Type.URL, start + offset, pos + 1 + offset);\n            if (ch == 60 || ch == 10 /* '<\\n' */)\n                return false;\n        }\n        return null;\n    }\n    else {\n        let depth = 0, pos = start;\n        for (let escaped = false; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (space(ch)) {\n                break;\n            }\n            else if (escaped) {\n                escaped = false;\n            }\n            else if (ch == 40 /* '(' */) {\n                depth++;\n            }\n            else if (ch == 41 /* ')' */) {\n                if (!depth)\n                    break;\n                depth--;\n            }\n            else if (ch == 92 /* '\\\\' */) {\n                escaped = true;\n            }\n        }\n        return pos > start ? elt(Type.URL, start + offset, pos + offset) : pos == text.length ? null : false;\n    }\n}\nfunction parseLinkTitle(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next != 39 && next != 34 && next != 40 /* '\"\\'(' */)\n        return false;\n    let end = next == 40 ? 41 : next;\n    for (let pos = start + 1, escaped = false; pos < text.length; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == end)\n            return elt(Type.LinkTitle, start + offset, pos + 1 + offset);\n        else if (ch == 92 /* '\\\\' */)\n            escaped = true;\n    }\n    return null;\n}\nfunction parseLinkLabel(text, start, offset, requireNonWS) {\n    for (let escaped = false, pos = start + 1, end = Math.min(text.length, pos + 999); pos < end; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == 93 /* ']' */)\n            return requireNonWS ? false : elt(Type.LinkLabel, start + offset, pos + 1 + offset);\n        else {\n            if (requireNonWS && !space(ch))\n                requireNonWS = false;\n            if (ch == 91 /* '[' */)\n                return false;\n            else if (ch == 92 /* '\\\\' */)\n                escaped = true;\n        }\n    }\n    return null;\n}\n/// Inline parsing functions get access to this context, and use it to\n/// read the content and emit syntax nodes.\nclass InlineContext {\n    /// @internal\n    constructor(\n    /// The parser that is being used.\n    parser, \n    /// The text of this inline section.\n    text, \n    /// The starting offset of the section in the document.\n    offset) {\n        this.parser = parser;\n        this.text = text;\n        this.offset = offset;\n        /// @internal\n        this.parts = [];\n    }\n    /// Get the character code at the given (document-relative)\n    /// position.\n    char(pos) { return pos >= this.end ? -1 : this.text.charCodeAt(pos - this.offset); }\n    /// The position of the end of this inline section.\n    get end() { return this.offset + this.text.length; }\n    /// Get a substring of this inline section. Again uses\n    /// document-relative positions.\n    slice(from, to) { return this.text.slice(from - this.offset, to - this.offset); }\n    /// @internal\n    append(elt) {\n        this.parts.push(elt);\n        return elt.to;\n    }\n    /// Add a [delimiter](#DelimiterType) at this given position. `open`\n    /// and `close` indicate whether this delimiter is opening, closing,\n    /// or both. Returns the end of the delimiter, for convenient\n    /// returning from [parse functions](#InlineParser.parse).\n    addDelimiter(type, from, to, open, close) {\n        return this.append(new InlineDelimiter(type, from, to, (open ? 1 /* Mark.Open */ : 0 /* Mark.None */) | (close ? 2 /* Mark.Close */ : 0 /* Mark.None */)));\n    }\n    /// Returns true when there is an unmatched link or image opening\n    /// token before the current position.\n    get hasOpenLink() {\n        for (let i = this.parts.length - 1; i >= 0; i--) {\n            let part = this.parts[i];\n            if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart))\n                return true;\n        }\n        return false;\n    }\n    /// Add an inline element. Returns the end of the element.\n    addElement(elt) {\n        return this.append(elt);\n    }\n    /// Resolve markers between this.parts.length and from, wrapping matched markers in the\n    /// appropriate node and updating the content of this.parts. @internal\n    resolveMarkers(from) {\n        // Scan forward, looking for closing tokens\n        for (let i = from; i < this.parts.length; i++) {\n            let close = this.parts[i];\n            if (!(close instanceof InlineDelimiter && close.type.resolve && (close.side & 2 /* Mark.Close */)))\n                continue;\n            let emp = close.type == EmphasisUnderscore || close.type == EmphasisAsterisk;\n            let closeSize = close.to - close.from;\n            let open, j = i - 1;\n            // Continue scanning for a matching opening token\n            for (; j >= from; j--) {\n                let part = this.parts[j];\n                if (part instanceof InlineDelimiter && (part.side & 1 /* Mark.Open */) && part.type == close.type &&\n                    // Ignore emphasis delimiters where the character count doesn't match\n                    !(emp && ((close.side & 1 /* Mark.Open */) || (part.side & 2 /* Mark.Close */)) &&\n                        (part.to - part.from + closeSize) % 3 == 0 && ((part.to - part.from) % 3 || closeSize % 3))) {\n                    open = part;\n                    break;\n                }\n            }\n            if (!open)\n                continue;\n            let type = close.type.resolve, content = [];\n            let start = open.from, end = close.to;\n            // Emphasis marker effect depends on the character count. Size consumed is minimum of the two\n            // markers.\n            if (emp) {\n                let size = Math.min(2, open.to - open.from, closeSize);\n                start = open.to - size;\n                end = close.from + size;\n                type = size == 1 ? \"Emphasis\" : \"StrongEmphasis\";\n            }\n            // Move the covered region into content, optionally adding marker nodes\n            if (open.type.mark)\n                content.push(this.elt(open.type.mark, start, open.to));\n            for (let k = j + 1; k < i; k++) {\n                if (this.parts[k] instanceof Element)\n                    content.push(this.parts[k]);\n                this.parts[k] = null;\n            }\n            if (close.type.mark)\n                content.push(this.elt(close.type.mark, close.from, end));\n            let element = this.elt(type, start, end, content);\n            // If there are leftover emphasis marker characters, shrink the close/open markers. Otherwise, clear them.\n            this.parts[j] = emp && open.from != start ? new InlineDelimiter(open.type, open.from, start, open.side) : null;\n            let keep = this.parts[i] = emp && close.to != end ? new InlineDelimiter(close.type, end, close.to, close.side) : null;\n            // Insert the new element in this.parts\n            if (keep)\n                this.parts.splice(i, 0, element);\n            else\n                this.parts[i] = element;\n        }\n        // Collect the elements remaining in this.parts into an array.\n        let result = [];\n        for (let i = from; i < this.parts.length; i++) {\n            let part = this.parts[i];\n            if (part instanceof Element)\n                result.push(part);\n        }\n        return result;\n    }\n    /// Find an opening delimiter of the given type. Returns `null` if\n    /// no delimiter is found, or an index that can be passed to\n    /// [`takeContent`](#InlineContext.takeContent) otherwise.\n    findOpeningDelimiter(type) {\n        for (let i = this.parts.length - 1; i >= 0; i--) {\n            let part = this.parts[i];\n            if (part instanceof InlineDelimiter && part.type == type)\n                return i;\n        }\n        return null;\n    }\n    /// Remove all inline elements and delimiters starting from the\n    /// given index (which you should get from\n    /// [`findOpeningDelimiter`](#InlineContext.findOpeningDelimiter),\n    /// resolve delimiters inside of them, and return them as an array\n    /// of elements.\n    takeContent(startIndex) {\n        let content = this.resolveMarkers(startIndex);\n        this.parts.length = startIndex;\n        return content;\n    }\n    /// Skip space after the given (document) position, returning either\n    /// the position of the next non-space character or the end of the\n    /// section.\n    skipSpace(from) { return skipSpace(this.text, from - this.offset) + this.offset; }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n}\nfunction injectMarks(elements, marks) {\n    if (!marks.length)\n        return elements;\n    if (!elements.length)\n        return marks;\n    let elts = elements.slice(), eI = 0;\n    for (let mark of marks) {\n        while (eI < elts.length && elts[eI].to < mark.to)\n            eI++;\n        if (eI < elts.length && elts[eI].from < mark.from) {\n            let e = elts[eI];\n            if (e instanceof Element)\n                elts[eI] = new Element(e.type, e.from, e.to, injectMarks(e.children, [mark]));\n        }\n        else {\n            elts.splice(eI++, 0, mark);\n        }\n    }\n    return elts;\n}\n// These are blocks that can span blank lines, and should thus only be\n// reused if their next sibling is also being reused.\nconst NotLast = [Type.CodeBlock, Type.ListItem, Type.OrderedList, Type.BulletList];\nclass FragmentCursor {\n    constructor(fragments, input) {\n        this.fragments = fragments;\n        this.input = input;\n        // Index into fragment array\n        this.i = 0;\n        // Active fragment\n        this.fragment = null;\n        this.fragmentEnd = -1;\n        // Cursor into the current fragment, if any. When `moveTo` returns\n        // true, this points at the first block after `pos`.\n        this.cursor = null;\n        if (fragments.length)\n            this.fragment = fragments[this.i++];\n    }\n    nextFragment() {\n        this.fragment = this.i < this.fragments.length ? this.fragments[this.i++] : null;\n        this.cursor = null;\n        this.fragmentEnd = -1;\n    }\n    moveTo(pos, lineStart) {\n        while (this.fragment && this.fragment.to <= pos)\n            this.nextFragment();\n        if (!this.fragment || this.fragment.from > (pos ? pos - 1 : 0))\n            return false;\n        if (this.fragmentEnd < 0) {\n            let end = this.fragment.to;\n            while (end > 0 && this.input.read(end - 1, end) != \"\\n\")\n                end--;\n            this.fragmentEnd = end ? end - 1 : 0;\n        }\n        let c = this.cursor;\n        if (!c) {\n            c = this.cursor = this.fragment.tree.cursor();\n            c.firstChild();\n        }\n        let rPos = pos + this.fragment.offset;\n        while (c.to <= rPos)\n            if (!c.parent())\n                return false;\n        for (;;) {\n            if (c.from >= rPos)\n                return this.fragment.from <= lineStart;\n            if (!c.childAfter(rPos))\n                return false;\n        }\n    }\n    matches(hash) {\n        let tree = this.cursor.tree;\n        return tree && tree.prop(NodeProp.contextHash) == hash;\n    }\n    takeNodes(cx) {\n        let cur = this.cursor, off = this.fragment.offset, fragEnd = this.fragmentEnd - (this.fragment.openEnd ? 1 : 0);\n        let start = cx.absoluteLineStart, end = start, blockI = cx.block.children.length;\n        let prevEnd = end, prevI = blockI;\n        for (;;) {\n            if (cur.to - off > fragEnd) {\n                if (cur.type.isAnonymous && cur.firstChild())\n                    continue;\n                break;\n            }\n            let pos = toRelative(cur.from - off, cx.ranges);\n            if (cur.to - off <= cx.ranges[cx.rangeI].to) { // Fits in current range\n                cx.addNode(cur.tree, pos);\n            }\n            else {\n                let dummy = new Tree(cx.parser.nodeSet.types[Type.Paragraph], [], [], 0, cx.block.hashProp);\n                cx.reusePlaceholders.set(dummy, cur.tree);\n                cx.addNode(dummy, pos);\n            }\n            // Taken content must always end in a block, because incremental\n            // parsing happens on block boundaries. Never stop directly\n            // after an indented code block, since those can continue after\n            // any number of blank lines.\n            if (cur.type.is(\"Block\")) {\n                if (NotLast.indexOf(cur.type.id) < 0) {\n                    end = cur.to - off;\n                    blockI = cx.block.children.length;\n                }\n                else {\n                    end = prevEnd;\n                    blockI = prevI;\n                    prevEnd = cur.to - off;\n                    prevI = cx.block.children.length;\n                }\n            }\n            if (!cur.nextSibling())\n                break;\n        }\n        while (cx.block.children.length > blockI) {\n            cx.block.children.pop();\n            cx.block.positions.pop();\n        }\n        return end - start;\n    }\n}\n// Convert an input-stream-relative position to a\n// Markdown-doc-relative position by subtracting the size of all input\n// gaps before `abs`.\nfunction toRelative(abs, ranges) {\n    let pos = abs;\n    for (let i = 1; i < ranges.length; i++) {\n        let gapFrom = ranges[i - 1].to, gapTo = ranges[i].from;\n        if (gapFrom < abs)\n            pos -= gapTo - gapFrom;\n    }\n    return pos;\n}\nconst markdownHighlighting = styleTags({\n    \"Blockquote/...\": tags.quote,\n    HorizontalRule: tags.contentSeparator,\n    \"ATXHeading1/... SetextHeading1/...\": tags.heading1,\n    \"ATXHeading2/... SetextHeading2/...\": tags.heading2,\n    \"ATXHeading3/...\": tags.heading3,\n    \"ATXHeading4/...\": tags.heading4,\n    \"ATXHeading5/...\": tags.heading5,\n    \"ATXHeading6/...\": tags.heading6,\n    \"Comment CommentBlock\": tags.comment,\n    Escape: tags.escape,\n    Entity: tags.character,\n    \"Emphasis/...\": tags.emphasis,\n    \"StrongEmphasis/...\": tags.strong,\n    \"Link/... Image/...\": tags.link,\n    \"OrderedList/... BulletList/...\": tags.list,\n    \"BlockQuote/...\": tags.quote,\n    \"InlineCode CodeText\": tags.monospace,\n    \"URL Autolink\": tags.url,\n    \"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark\": tags.processingInstruction,\n    \"CodeInfo LinkLabel\": tags.labelName,\n    LinkTitle: tags.string,\n    Paragraph: tags.content\n});\n/// The default CommonMark parser.\nconst parser = new MarkdownParser(new NodeSet(nodeTypes).extend(markdownHighlighting), Object.keys(DefaultBlockParsers).map(n => DefaultBlockParsers[n]), Object.keys(DefaultBlockParsers).map(n => DefaultLeafBlocks[n]), Object.keys(DefaultBlockParsers), DefaultEndLeaf, DefaultSkipMarkup, Object.keys(DefaultInline).map(n => DefaultInline[n]), Object.keys(DefaultInline), []);\n\nfunction leftOverSpace(node, from, to) {\n    let ranges = [];\n    for (let n = node.firstChild, pos = from;; n = n.nextSibling) {\n        let nextPos = n ? n.from : to;\n        if (nextPos > pos)\n            ranges.push({ from: pos, to: nextPos });\n        if (!n)\n            break;\n        pos = n.to;\n    }\n    return ranges;\n}\n/// Create a Markdown extension to enable nested parsing on code\n/// blocks and/or embedded HTML.\nfunction parseCode(config) {\n    let { codeParser, htmlParser } = config;\n    let wrap = parseMixed((node, input) => {\n        let id = node.type.id;\n        if (codeParser && (id == Type.CodeBlock || id == Type.FencedCode)) {\n            let info = \"\";\n            if (id == Type.FencedCode) {\n                let infoNode = node.node.getChild(Type.CodeInfo);\n                if (infoNode)\n                    info = input.read(infoNode.from, infoNode.to);\n            }\n            let parser = codeParser(info);\n            if (parser)\n                return { parser, overlay: node => node.type.id == Type.CodeText };\n        }\n        else if (htmlParser && (id == Type.HTMLBlock || id == Type.HTMLTag)) {\n            return { parser: htmlParser, overlay: leftOverSpace(node.node, node.from, node.to) };\n        }\n        return null;\n    });\n    return { wrap };\n}\n\nconst StrikethroughDelim = { resolve: \"Strikethrough\", mark: \"StrikethroughMark\" };\n/// An extension that implements\n/// [GFM-style](https://github.github.com/gfm/#strikethrough-extension-)\n/// Strikethrough syntax using `~~` delimiters.\nconst Strikethrough = {\n    defineNodes: [{\n            name: \"Strikethrough\",\n            style: { \"Strikethrough/...\": tags.strikethrough }\n        }, {\n            name: \"StrikethroughMark\",\n            style: tags.processingInstruction\n        }],\n    parseInline: [{\n            name: \"Strikethrough\",\n            parse(cx, next, pos) {\n                if (next != 126 /* '~' */ || cx.char(pos + 1) != 126 || cx.char(pos + 2) == 126)\n                    return -1;\n                let before = cx.slice(pos - 1, pos), after = cx.slice(pos + 2, pos + 3);\n                let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n                let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n                return cx.addDelimiter(StrikethroughDelim, pos, pos + 2, !sAfter && (!pAfter || sBefore || pBefore), !sBefore && (!pBefore || sAfter || pAfter));\n            },\n            after: \"Emphasis\"\n        }]\n};\nfunction parseRow(cx, line, startI = 0, elts, offset = 0) {\n    let count = 0, first = true, cellStart = -1, cellEnd = -1, esc = false;\n    let parseCell = () => {\n        elts.push(cx.elt(\"TableCell\", offset + cellStart, offset + cellEnd, cx.parser.parseInline(line.slice(cellStart, cellEnd), offset + cellStart)));\n    };\n    for (let i = startI; i < line.length; i++) {\n        let next = line.charCodeAt(i);\n        if (next == 124 /* '|' */ && !esc) {\n            if (!first || cellStart > -1)\n                count++;\n            first = false;\n            if (elts) {\n                if (cellStart > -1)\n                    parseCell();\n                elts.push(cx.elt(\"TableDelimiter\", i + offset, i + offset + 1));\n            }\n            cellStart = cellEnd = -1;\n        }\n        else if (esc || next != 32 && next != 9) {\n            if (cellStart < 0)\n                cellStart = i;\n            cellEnd = i + 1;\n        }\n        esc = !esc && next == 92;\n    }\n    if (cellStart > -1) {\n        count++;\n        if (elts)\n            parseCell();\n    }\n    return count;\n}\nfunction hasPipe(str, start) {\n    for (let i = start; i < str.length; i++) {\n        let next = str.charCodeAt(i);\n        if (next == 124 /* '|' */)\n            return true;\n        if (next == 92 /* '\\\\' */)\n            i++;\n    }\n    return false;\n}\nconst delimiterLine = /^\\|?(\\s*:?-+:?\\s*\\|)+(\\s*:?-+:?\\s*)?$/;\nclass TableParser {\n    constructor() {\n        // Null means we haven't seen the second line yet, false means this\n        // isn't a table, and an array means this is a table and we've\n        // parsed the given rows so far.\n        this.rows = null;\n    }\n    nextLine(cx, line, leaf) {\n        if (this.rows == null) { // Second line\n            this.rows = false;\n            let lineText;\n            if ((line.next == 45 || line.next == 58 || line.next == 124 /* '-:|' */) &&\n                delimiterLine.test(lineText = line.text.slice(line.pos))) {\n                let firstRow = [], firstCount = parseRow(cx, leaf.content, 0, firstRow, leaf.start);\n                if (firstCount == parseRow(cx, lineText, line.pos))\n                    this.rows = [cx.elt(\"TableHeader\", leaf.start, leaf.start + leaf.content.length, firstRow),\n                        cx.elt(\"TableDelimiter\", cx.lineStart + line.pos, cx.lineStart + line.text.length)];\n            }\n        }\n        else if (this.rows) { // Line after the second\n            let content = [];\n            parseRow(cx, line.text, line.pos, content, cx.lineStart);\n            this.rows.push(cx.elt(\"TableRow\", cx.lineStart + line.pos, cx.lineStart + line.text.length, content));\n        }\n        return false;\n    }\n    finish(cx, leaf) {\n        if (!this.rows)\n            return false;\n        cx.addLeafElement(leaf, cx.elt(\"Table\", leaf.start, leaf.start + leaf.content.length, this.rows));\n        return true;\n    }\n}\n/// This extension provides\n/// [GFM-style](https://github.github.com/gfm/#tables-extension-)\n/// tables, using syntax like this:\n///\n/// ```\n/// | head 1 | head 2 |\n/// | ---    | ---    |\n/// | cell 1 | cell 2 |\n/// ```\nconst Table = {\n    defineNodes: [\n        { name: \"Table\", block: true },\n        { name: \"TableHeader\", style: { \"TableHeader/...\": tags.heading } },\n        \"TableRow\",\n        { name: \"TableCell\", style: tags.content },\n        { name: \"TableDelimiter\", style: tags.processingInstruction },\n    ],\n    parseBlock: [{\n            name: \"Table\",\n            leaf(_, leaf) { return hasPipe(leaf.content, 0) ? new TableParser : null; },\n            endLeaf(cx, line, leaf) {\n                if (leaf.parsers.some(p => p instanceof TableParser) || !hasPipe(line.text, line.basePos))\n                    return false;\n                let next = cx.scanLine(cx.absoluteLineEnd + 1).text;\n                return delimiterLine.test(next) && parseRow(cx, line.text, line.basePos) == parseRow(cx, next, line.basePos);\n            },\n            before: \"SetextHeading\"\n        }]\n};\nclass TaskParser {\n    nextLine() { return false; }\n    finish(cx, leaf) {\n        cx.addLeafElement(leaf, cx.elt(\"Task\", leaf.start, leaf.start + leaf.content.length, [\n            cx.elt(\"TaskMarker\", leaf.start, leaf.start + 3),\n            ...cx.parser.parseInline(leaf.content.slice(3), leaf.start + 3)\n        ]));\n        return true;\n    }\n}\n/// Extension providing\n/// [GFM-style](https://github.github.com/gfm/#task-list-items-extension-)\n/// task list items, where list items can be prefixed with `[ ]` or\n/// `[x]` to add a checkbox.\nconst TaskList = {\n    defineNodes: [\n        { name: \"Task\", block: true, style: tags.list },\n        { name: \"TaskMarker\", style: tags.atom }\n    ],\n    parseBlock: [{\n            name: \"TaskList\",\n            leaf(cx, leaf) {\n                return /^\\[[ xX]\\][ \\t]/.test(leaf.content) && cx.parentType().name == \"ListItem\" ? new TaskParser : null;\n            },\n            after: \"SetextHeading\"\n        }]\n};\nconst autolinkRE = /(www\\.)|(https?:\\/\\/)|([\\w.+-]+@)|(mailto:|xmpp:)/gy;\nconst urlRE = /[\\w-]+(\\.[\\w-]+)+(\\/[^\\s<]*)?/gy;\nconst lastTwoDomainWords = /[\\w-]+\\.[\\w-]+($|\\/)/;\nconst emailRE = /[\\w.+-]+@[\\w-]+(\\.[\\w.-]+)+/gy;\nconst xmppResourceRE = /\\/[a-zA-Z\\d@.]+/gy;\nfunction count(str, from, to, ch) {\n    let result = 0;\n    for (let i = from; i < to; i++)\n        if (str[i] == ch)\n            result++;\n    return result;\n}\nfunction autolinkURLEnd(text, from) {\n    urlRE.lastIndex = from;\n    let m = urlRE.exec(text);\n    if (!m || lastTwoDomainWords.exec(m[0])[0].indexOf(\"_\") > -1)\n        return -1;\n    let end = from + m[0].length;\n    for (;;) {\n        let last = text[end - 1], m;\n        if (/[?!.,:*_~]/.test(last) ||\n            last == \")\" && count(text, from, end, \")\") > count(text, from, end, \"(\"))\n            end--;\n        else if (last == \";\" && (m = /&(?:#\\d+|#x[a-f\\d]+|\\w+);$/.exec(text.slice(from, end))))\n            end = from + m.index;\n        else\n            break;\n    }\n    return end;\n}\nfunction autolinkEmailEnd(text, from) {\n    emailRE.lastIndex = from;\n    let m = emailRE.exec(text);\n    if (!m)\n        return -1;\n    let last = m[0][m[0].length - 1];\n    return last == \"_\" || last == \"-\" ? -1 : from + m[0].length - (last == \".\" ? 1 : 0);\n}\n/// Extension that implements autolinking for\n/// `www.`/`http://`/`https://`/`mailto:`/`xmpp:` URLs and email\n/// addresses.\nconst Autolink = {\n    parseInline: [{\n            name: \"Autolink\",\n            parse(cx, next, absPos) {\n                let pos = absPos - cx.offset;\n                autolinkRE.lastIndex = pos;\n                let m = autolinkRE.exec(cx.text), end = -1;\n                if (!m)\n                    return -1;\n                if (m[1] || m[2]) { // www., http://\n                    end = autolinkURLEnd(cx.text, pos + m[0].length);\n                    if (end > -1 && cx.hasOpenLink) {\n                        let noBracket = /([^\\[\\]]|\\[[^\\]]*\\])*/.exec(cx.text.slice(pos, end));\n                        end = pos + noBracket[0].length;\n                    }\n                }\n                else if (m[3]) { // email address\n                    end = autolinkEmailEnd(cx.text, pos);\n                }\n                else { // mailto:/xmpp:\n                    end = autolinkEmailEnd(cx.text, pos + m[0].length);\n                    if (end > -1 && m[0] == \"xmpp:\") {\n                        xmppResourceRE.lastIndex = end;\n                        m = xmppResourceRE.exec(cx.text);\n                        if (m)\n                            end = m.index + m[0].length;\n                    }\n                }\n                if (end < 0)\n                    return -1;\n                cx.addElement(cx.elt(\"URL\", absPos, end + cx.offset));\n                return end + cx.offset;\n            }\n        }]\n};\n/// Extension bundle containing [`Table`](#Table),\n/// [`TaskList`](#TaskList), [`Strikethrough`](#Strikethrough), and\n/// [`Autolink`](#Autolink).\nconst GFM = [Table, TaskList, Strikethrough, Autolink];\nfunction parseSubSuper(ch, node, mark) {\n    return (cx, next, pos) => {\n        if (next != ch || cx.char(pos + 1) == ch)\n            return -1;\n        let elts = [cx.elt(mark, pos, pos + 1)];\n        for (let i = pos + 1; i < cx.end; i++) {\n            let next = cx.char(i);\n            if (next == ch)\n                return cx.addElement(cx.elt(node, pos, i + 1, elts.concat(cx.elt(mark, i, i + 1))));\n            if (next == 92 /* '\\\\' */)\n                elts.push(cx.elt(\"Escape\", i, i++ + 2));\n            if (space(next))\n                break;\n        }\n        return -1;\n    };\n}\n/// Extension providing\n/// [Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\n/// superscript using `^` markers.\nconst Superscript = {\n    defineNodes: [\n        { name: \"Superscript\", style: tags.special(tags.content) },\n        { name: \"SuperscriptMark\", style: tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Superscript\",\n            parse: parseSubSuper(94 /* '^' */, \"Superscript\", \"SuperscriptMark\")\n        }]\n};\n/// Extension providing\n/// [Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\n/// subscript using `~` markers.\nconst Subscript = {\n    defineNodes: [\n        { name: \"Subscript\", style: tags.special(tags.content) },\n        { name: \"SubscriptMark\", style: tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Subscript\",\n            parse: parseSubSuper(126 /* '~' */, \"Subscript\", \"SubscriptMark\")\n        }]\n};\n/// Extension that parses two colons with only letters, underscores,\n/// and numbers between them as `Emoji` nodes.\nconst Emoji = {\n    defineNodes: [{ name: \"Emoji\", style: tags.character }],\n    parseInline: [{\n            name: \"Emoji\",\n            parse(cx, next, pos) {\n                let match;\n                if (next != 58 /* ':' */ || !(match = /^[a-zA-Z_0-9]+:/.exec(cx.slice(pos + 1, cx.end))))\n                    return -1;\n                return cx.addElement(cx.elt(\"Emoji\", pos, pos + 1 + match[0].length));\n            }\n        }]\n};\n\nexport { Autolink, BlockContext, Element, Emoji, GFM, InlineContext, LeafBlock, Line, MarkdownParser, Strikethrough, Subscript, Superscript, Table, TaskList, parseCode, parser };\n", "import { EditorSelection, countColumn, Prec, EditorState } from '@codemirror/state';\nimport { keymap } from '@codemirror/view';\nimport { defineLanguageFacet, foldNodeProp, indentNodeProp, languageDataProp, foldService, syntaxTree, Language, LanguageDescription, ParseContext, indentUnit, LanguageSupport } from '@codemirror/language';\nimport { CompletionContext } from '@codemirror/autocomplete';\nimport { parser, GFM, Subscript, Superscript, Emoji, MarkdownParser, parseCode } from '@lezer/markdown';\nimport { html, htmlCompletionSource } from '@codemirror/lang-html';\nimport { NodeProp } from '@lezer/common';\n\nconst data = /*@__PURE__*/defineLanguageFacet({ commentTokens: { block: { open: \"<!--\", close: \"-->\" } } });\nconst headingProp = /*@__PURE__*/new NodeProp();\nconst commonmark = /*@__PURE__*/parser.configure({\n    props: [\n        /*@__PURE__*/foldNodeProp.add(type => {\n            return !type.is(\"Block\") || type.is(\"Document\") || isHeading(type) != null || isList(type) ? undefined\n                : (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to });\n        }),\n        /*@__PURE__*/headingProp.add(isHeading),\n        /*@__PURE__*/indentNodeProp.add({\n            Document: () => null\n        }),\n        /*@__PURE__*/languageDataProp.add({\n            Document: data\n        })\n    ]\n});\nfunction isHeading(type) {\n    let match = /^(?:ATX|Setext)Heading(\\d)$/.exec(type.name);\n    return match ? +match[1] : undefined;\n}\nfunction isList(type) {\n    return type.name == \"OrderedList\" || type.name == \"BulletList\";\n}\nfunction findSectionEnd(headerNode, level) {\n    let last = headerNode;\n    for (;;) {\n        let next = last.nextSibling, heading;\n        if (!next || (heading = isHeading(next.type)) != null && heading <= level)\n            break;\n        last = next;\n    }\n    return last.to;\n}\nconst headerIndent = /*@__PURE__*/foldService.of((state, start, end) => {\n    for (let node = syntaxTree(state).resolveInner(end, -1); node; node = node.parent) {\n        if (node.from < start)\n            break;\n        let heading = node.type.prop(headingProp);\n        if (heading == null)\n            continue;\n        let upto = findSectionEnd(node, heading);\n        if (upto > end)\n            return { from: end, to: upto };\n    }\n    return null;\n});\nfunction mkLang(parser) {\n    return new Language(data, parser, [headerIndent], \"markdown\");\n}\n/**\nLanguage support for strict CommonMark.\n*/\nconst commonmarkLanguage = /*@__PURE__*/mkLang(commonmark);\nconst extended = /*@__PURE__*/commonmark.configure([GFM, Subscript, Superscript, Emoji, {\n        props: [\n            /*@__PURE__*/foldNodeProp.add({\n                Table: (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to })\n            })\n        ]\n    }]);\n/**\nLanguage support for [GFM](https://github.github.com/gfm/) plus\nsubscript, superscript, and emoji syntax.\n*/\nconst markdownLanguage = /*@__PURE__*/mkLang(extended);\nfunction getCodeParser(languages, defaultLanguage) {\n    return (info) => {\n        if (info && languages) {\n            let found = null;\n            // Strip anything after whitespace\n            info = /\\S*/.exec(info)[0];\n            if (typeof languages == \"function\")\n                found = languages(info);\n            else\n                found = LanguageDescription.matchLanguageName(languages, info, true);\n            if (found instanceof LanguageDescription)\n                return found.support ? found.support.language.parser : ParseContext.getSkippingParser(found.load());\n            else if (found)\n                return found.parser;\n        }\n        return defaultLanguage ? defaultLanguage.parser : null;\n    };\n}\n\nclass Context {\n    constructor(node, from, to, spaceBefore, spaceAfter, type, item) {\n        this.node = node;\n        this.from = from;\n        this.to = to;\n        this.spaceBefore = spaceBefore;\n        this.spaceAfter = spaceAfter;\n        this.type = type;\n        this.item = item;\n    }\n    blank(maxWidth, trailing = true) {\n        let result = this.spaceBefore + (this.node.name == \"Blockquote\" ? \">\" : \"\");\n        if (maxWidth != null) {\n            while (result.length < maxWidth)\n                result += \" \";\n            return result;\n        }\n        else {\n            for (let i = this.to - this.from - result.length - this.spaceAfter.length; i > 0; i--)\n                result += \" \";\n            return result + (trailing ? this.spaceAfter : \"\");\n        }\n    }\n    marker(doc, add) {\n        let number = this.node.name == \"OrderedList\" ? String((+itemNumber(this.item, doc)[2] + add)) : \"\";\n        return this.spaceBefore + number + this.type + this.spaceAfter;\n    }\n}\nfunction getContext(node, doc) {\n    let nodes = [], context = [];\n    for (let cur = node; cur; cur = cur.parent) {\n        if (cur.name == \"FencedCode\")\n            return context;\n        if (cur.name == \"ListItem\" || cur.name == \"Blockquote\")\n            nodes.push(cur);\n    }\n    for (let i = nodes.length - 1; i >= 0; i--) {\n        let node = nodes[i], match;\n        let line = doc.lineAt(node.from), startPos = node.from - line.from;\n        if (node.name == \"Blockquote\" && (match = /^ *>( ?)/.exec(line.text.slice(startPos)))) {\n            context.push(new Context(node, startPos, startPos + match[0].length, \"\", match[1], \">\", null));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"OrderedList\" &&\n            (match = /^( *)\\d+([.)])( *)/.exec(line.text.slice(startPos)))) {\n            let after = match[3], len = match[0].length;\n            if (after.length >= 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, match[2], node));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"BulletList\" &&\n            (match = /^( *)([-+*])( {1,4}\\[[ xX]\\])?( +)/.exec(line.text.slice(startPos)))) {\n            let after = match[4], len = match[0].length;\n            if (after.length > 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            let type = match[2];\n            if (match[3])\n                type += match[3].replace(/[xX]/, ' ');\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, type, node));\n        }\n    }\n    return context;\n}\nfunction itemNumber(item, doc) {\n    return /^(\\s*)(\\d+)(?=[.)])/.exec(doc.sliceString(item.from, item.from + 10));\n}\nfunction renumberList(after, doc, changes, offset = 0) {\n    for (let prev = -1, node = after;;) {\n        if (node.name == \"ListItem\") {\n            let m = itemNumber(node, doc);\n            let number = +m[2];\n            if (prev >= 0) {\n                if (number != prev + 1)\n                    return;\n                changes.push({ from: node.from + m[1].length, to: node.from + m[0].length, insert: String(prev + 2 + offset) });\n            }\n            prev = number;\n        }\n        let next = node.nextSibling;\n        if (!next)\n            break;\n        node = next;\n    }\n}\nfunction normalizeIndent(content, state) {\n    let blank = /^[ \\t]*/.exec(content)[0].length;\n    if (!blank || state.facet(indentUnit) != \"\\t\")\n        return content;\n    let col = countColumn(content, 4, blank);\n    let space = \"\";\n    for (let i = col; i > 0;) {\n        if (i >= 4) {\n            space += \"\\t\";\n            i -= 4;\n        }\n        else {\n            space += \" \";\n            i--;\n        }\n    }\n    return space + content.slice(blank);\n}\n/**\nThis command, when invoked in Markdown context with cursor\nselection(s), will create a new line with the markup for\nblockquotes and lists that were active on the old line. If the\ncursor was directly after the end of the markup for the old line,\ntrailing whitespace and list markers are removed from that line.\n\nThe command does nothing in non-Markdown context, so it should\nnot be used as the only binding for Enter (even in a Markdown\ndocument, HTML and code regions might use a different language).\n*/\nconst insertNewlineContinueMarkup = ({ state, dispatch }) => {\n    let tree = syntaxTree(state), { doc } = state;\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty || !markdownLanguage.isActiveAt(state, range.from, 0))\n            return dont = { range };\n        let pos = range.from, line = doc.lineAt(pos);\n        let context = getContext(tree.resolveInner(pos, -1), doc);\n        while (context.length && context[context.length - 1].from > pos - line.from)\n            context.pop();\n        if (!context.length)\n            return dont = { range };\n        let inner = context[context.length - 1];\n        if (inner.to - inner.spaceAfter.length > pos - line.from)\n            return dont = { range };\n        let emptyLine = pos >= (inner.to - inner.spaceAfter.length) && !/\\S/.test(line.text.slice(inner.to));\n        // Empty line in list\n        if (inner.item && emptyLine) {\n            let first = inner.node.firstChild, second = inner.node.getChild(\"ListItem\", \"ListItem\");\n            // Not second item or blank line before: delete a level of markup\n            if (first.to >= pos || second && second.to < pos ||\n                line.from > 0 && !/[^\\s>]/.test(doc.lineAt(line.from - 1).text)) {\n                let next = context.length > 1 ? context[context.length - 2] : null;\n                let delTo, insert = \"\";\n                if (next && next.item) { // Re-add marker for the list at the next level\n                    delTo = line.from + next.from;\n                    insert = next.marker(doc, 1);\n                }\n                else {\n                    delTo = line.from + (next ? next.to : 0);\n                }\n                let changes = [{ from: delTo, to: pos, insert }];\n                if (inner.node.name == \"OrderedList\")\n                    renumberList(inner.item, doc, changes, -2);\n                if (next && next.node.name == \"OrderedList\")\n                    renumberList(next.item, doc, changes);\n                return { range: EditorSelection.cursor(delTo + insert.length), changes };\n            }\n            else { // Move second item down, making tight two-item list non-tight\n                let insert = blankLine(context, state, line);\n                return { range: EditorSelection.cursor(pos + insert.length + 1),\n                    changes: { from: line.from, insert: insert + state.lineBreak } };\n            }\n        }\n        if (inner.node.name == \"Blockquote\" && emptyLine && line.from) {\n            let prevLine = doc.lineAt(line.from - 1), quoted = />\\s*$/.exec(prevLine.text);\n            // Two aligned empty quoted lines in a row\n            if (quoted && quoted.index == inner.from) {\n                let changes = state.changes([{ from: prevLine.from + quoted.index, to: prevLine.to },\n                    { from: line.from + inner.from, to: line.to }]);\n                return { range: range.map(changes), changes };\n            }\n        }\n        let changes = [];\n        if (inner.node.name == \"OrderedList\")\n            renumberList(inner.item, doc, changes);\n        let continued = inner.item && inner.item.from < line.from;\n        let insert = \"\";\n        // If not dedented\n        if (!continued || /^[\\s\\d.)\\-+*>]*/.exec(line.text)[0].length >= inner.to) {\n            for (let i = 0, e = context.length - 1; i <= e; i++) {\n                insert += i == e && !continued ? context[i].marker(doc, 1)\n                    : context[i].blank(i < e ? countColumn(line.text, 4, context[i + 1].from) - insert.length : null);\n            }\n        }\n        let from = pos;\n        while (from > line.from && /\\s/.test(line.text.charAt(from - line.from - 1)))\n            from--;\n        insert = normalizeIndent(insert, state);\n        if (nonTightList(inner.node, state.doc))\n            insert = blankLine(context, state, line) + state.lineBreak + insert;\n        changes.push({ from, to: pos, insert: state.lineBreak + insert });\n        return { range: EditorSelection.cursor(from + insert.length + 1), changes };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\nfunction isMark(node) {\n    return node.name == \"QuoteMark\" || node.name == \"ListMark\";\n}\nfunction nonTightList(node, doc) {\n    if (node.name != \"OrderedList\" && node.name != \"BulletList\")\n        return false;\n    let first = node.firstChild, second = node.getChild(\"ListItem\", \"ListItem\");\n    if (!second)\n        return false;\n    let line1 = doc.lineAt(first.to), line2 = doc.lineAt(second.from);\n    let empty = /^[\\s>]*$/.test(line1.text);\n    return line1.number + (empty ? 0 : 1) < line2.number;\n}\nfunction blankLine(context, state, line) {\n    let insert = \"\";\n    for (let i = 0, e = context.length - 2; i <= e; i++) {\n        insert += context[i].blank(i < e\n            ? countColumn(line.text, 4, Math.min(line.text.length, context[i + 1].from)) - insert.length\n            : null, i < e);\n    }\n    return normalizeIndent(insert, state);\n}\nfunction contextNodeForDelete(tree, pos) {\n    let node = tree.resolveInner(pos, -1), scan = pos;\n    if (isMark(node)) {\n        scan = node.from;\n        node = node.parent;\n    }\n    for (let prev; prev = node.childBefore(scan);) {\n        if (isMark(prev)) {\n            scan = prev.from;\n        }\n        else if (prev.name == \"OrderedList\" || prev.name == \"BulletList\") {\n            node = prev.lastChild;\n            scan = node.to;\n        }\n        else {\n            break;\n        }\n    }\n    return node;\n}\n/**\nThis command will, when invoked in a Markdown context with the\ncursor directly after list or blockquote markup, delete one level\nof markup. When the markup is for a list, it will be replaced by\nspaces on the first invocation (a further invocation will delete\nthe spaces), to make it easy to continue a list.\n\nWhen not after Markdown block markup, this command will return\nfalse, so it is intended to be bound alongside other deletion\ncommands, with a higher precedence than the more generic commands.\n*/\nconst deleteMarkupBackward = ({ state, dispatch }) => {\n    let tree = syntaxTree(state);\n    let dont = null, changes = state.changeByRange(range => {\n        let pos = range.from, { doc } = state;\n        if (range.empty && markdownLanguage.isActiveAt(state, range.from)) {\n            let line = doc.lineAt(pos);\n            let context = getContext(contextNodeForDelete(tree, pos), doc);\n            if (context.length) {\n                let inner = context[context.length - 1];\n                let spaceEnd = inner.to - inner.spaceAfter.length + (inner.spaceAfter ? 1 : 0);\n                // Delete extra trailing space after markup\n                if (pos - line.from > spaceEnd && !/\\S/.test(line.text.slice(spaceEnd, pos - line.from)))\n                    return { range: EditorSelection.cursor(line.from + spaceEnd),\n                        changes: { from: line.from + spaceEnd, to: pos } };\n                if (pos - line.from == spaceEnd &&\n                    // Only apply this if we're on the line that has the\n                    // construct's syntax, or there's only indentation in the\n                    // target range\n                    (!inner.item || line.from <= inner.item.from || !/\\S/.test(line.text.slice(0, inner.to)))) {\n                    let start = line.from + inner.from;\n                    // Replace a list item marker with blank space\n                    if (inner.item && inner.node.from < inner.item.from && /\\S/.test(line.text.slice(inner.from, inner.to))) {\n                        let insert = inner.blank(countColumn(line.text, 4, inner.to) - countColumn(line.text, 4, inner.from));\n                        if (start == line.from)\n                            insert = normalizeIndent(insert, state);\n                        return { range: EditorSelection.cursor(start + insert.length),\n                            changes: { from: start, to: line.from + inner.to, insert } };\n                    }\n                    // Delete one level of indentation\n                    if (start < pos)\n                        return { range: EditorSelection.cursor(start), changes: { from: start, to: pos } };\n                }\n            }\n        }\n        return dont = { range };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"delete\" }));\n    return true;\n};\n\n/**\nA small keymap with Markdown-specific bindings. Binds Enter to\n[`insertNewlineContinueMarkup`](https://codemirror.net/6/docs/ref/#lang-markdown.insertNewlineContinueMarkup)\nand Backspace to\n[`deleteMarkupBackward`](https://codemirror.net/6/docs/ref/#lang-markdown.deleteMarkupBackward).\n*/\nconst markdownKeymap = [\n    { key: \"Enter\", run: insertNewlineContinueMarkup },\n    { key: \"Backspace\", run: deleteMarkupBackward }\n];\nconst htmlNoMatch = /*@__PURE__*/html({ matchClosingTags: false });\n/**\nMarkdown language support.\n*/\nfunction markdown(config = {}) {\n    let { codeLanguages, defaultCodeLanguage, addKeymap = true, base: { parser } = commonmarkLanguage, completeHTMLTags = true, htmlTagLanguage = htmlNoMatch } = config;\n    if (!(parser instanceof MarkdownParser))\n        throw new RangeError(\"Base parser provided to `markdown` should be a Markdown parser\");\n    let extensions = config.extensions ? [config.extensions] : [];\n    let support = [htmlTagLanguage.support], defaultCode;\n    if (defaultCodeLanguage instanceof LanguageSupport) {\n        support.push(defaultCodeLanguage.support);\n        defaultCode = defaultCodeLanguage.language;\n    }\n    else if (defaultCodeLanguage) {\n        defaultCode = defaultCodeLanguage;\n    }\n    let codeParser = codeLanguages || defaultCode ? getCodeParser(codeLanguages, defaultCode) : undefined;\n    extensions.push(parseCode({ codeParser, htmlParser: htmlTagLanguage.language.parser }));\n    if (addKeymap)\n        support.push(Prec.high(keymap.of(markdownKeymap)));\n    let lang = mkLang(parser.configure(extensions));\n    if (completeHTMLTags)\n        support.push(lang.data.of({ autocomplete: htmlTagCompletion }));\n    return new LanguageSupport(lang, support);\n}\nfunction htmlTagCompletion(context) {\n    let { state, pos } = context, m = /<[:\\-\\.\\w\\u00b7-\\uffff]*$/.exec(state.sliceDoc(pos - 25, pos));\n    if (!m)\n        return null;\n    let tree = syntaxTree(state).resolveInner(pos, -1);\n    while (tree && !tree.type.isTop) {\n        if (tree.name == \"CodeBlock\" || tree.name == \"FencedCode\" || tree.name == \"ProcessingInstructionBlock\" ||\n            tree.name == \"CommentBlock\" || tree.name == \"Link\" || tree.name == \"Image\")\n            return null;\n        tree = tree.parent;\n    }\n    return {\n        from: pos - m[0].length, to: pos,\n        options: htmlTagCompletions(),\n        validFor: /^<[:\\-\\.\\w\\u00b7-\\uffff]*$/\n    };\n}\nlet _tagCompletions = null;\nfunction htmlTagCompletions() {\n    if (_tagCompletions)\n        return _tagCompletions;\n    let result = htmlCompletionSource(new CompletionContext(EditorState.create({ extensions: htmlNoMatch }), 0, true));\n    return _tagCompletions = result ? result.options : [];\n}\n\nexport { commonmarkLanguage, deleteMarkupBackward, insertNewlineContinueMarkup, markdown, markdownKeymap, markdownLanguage };\n"], "names": [], "sourceRoot": ""}