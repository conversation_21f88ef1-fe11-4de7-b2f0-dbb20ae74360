{"version": 3, "file": "4038.edb04f3d9d68204491ba.js?v=edb04f3d9d68204491ba", "mappings": ";;;;;;;;;;AAAA;AACA,cAAc;AACd,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,6BAA6B;AAC7B;;AAEA;;AAEA;AACA,kBAAkB,cAAc;AAChC;AACA;;AAEA,eAAe;AACf;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,8BAA8B,EAAE;AAChC;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,gBAAgB,kCAAkC;AAClD,yCAAyC;AACzC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA,8CAA8C;AAC9C,wBAAwB,sBAAsB;AAC9C,yBAAyB;AACzB;;AAEA;AACA,GAAG;;AAEH;AACA,uBAAuB,QAAQ;AAC/B,oBAAoB,oBAAoB;AACxC;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/yacas.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar bodiedOps = words(\"Assert BackQuote D Defun Deriv For ForEach FromFile \" +\n                      \"FromString Function Integrate InverseTaylor Limit \" +\n                      \"LocalSymbols Macro MacroRule MacroRulePattern \" +\n                      \"NIntegrate Rule RulePattern Subst TD TExplicitSum \" +\n                      \"TSum Taylor Taylor1 Taylor2 Taylor3 ToFile \" +\n                      \"ToStdout ToString TraceRule Until While\");\n\n// patterns\nvar pFloatForm  = \"(?:(?:\\\\.\\\\d+|\\\\d+\\\\.\\\\d*|\\\\d+)(?:[eE][+-]?\\\\d+)?)\";\nvar pIdentifier = \"(?:[a-zA-Z\\\\$'][a-zA-Z0-9\\\\$']*)\";\n\n// regular expressions\nvar reFloatForm    = new RegExp(pFloatForm);\nvar reIdentifier   = new RegExp(pIdentifier);\nvar rePattern      = new RegExp(pIdentifier + \"?_\" + pIdentifier);\nvar reFunctionLike = new RegExp(pIdentifier + \"\\\\s*\\\\(\");\n\nfunction tokenBase(stream, state) {\n  var ch;\n\n  // get next character\n  ch = stream.next();\n\n  // string\n  if (ch === '\"') {\n    state.tokenize = tokenString;\n    return state.tokenize(stream, state);\n  }\n\n  // comment\n  if (ch === '/') {\n    if (stream.eat('*')) {\n      state.tokenize = tokenComment;\n      return state.tokenize(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n\n  // go back one character\n  stream.backUp(1);\n\n  // update scope info\n  var m = stream.match(/^(\\w+)\\s*\\(/, false);\n  if (m !== null && bodiedOps.hasOwnProperty(m[1]))\n    state.scopes.push('bodied');\n\n  var scope = currentScope(state);\n\n  if (scope === 'bodied' && ch === '[')\n    state.scopes.pop();\n\n  if (ch === '[' || ch === '{' || ch === '(')\n    state.scopes.push(ch);\n\n  scope = currentScope(state);\n\n  if (scope === '[' && ch === ']' ||\n      scope === '{' && ch === '}' ||\n      scope === '(' && ch === ')')\n    state.scopes.pop();\n\n  if (ch === ';') {\n    while (scope === 'bodied') {\n      state.scopes.pop();\n      scope = currentScope(state);\n    }\n  }\n\n  // look for ordered rules\n  if (stream.match(/\\d+ *#/, true, false)) {\n    return 'qualifier';\n  }\n\n  // look for numbers\n  if (stream.match(reFloatForm, true, false)) {\n    return 'number';\n  }\n\n  // look for placeholders\n  if (stream.match(rePattern, true, false)) {\n    return 'variableName.special';\n  }\n\n  // match all braces separately\n  if (stream.match(/(?:\\[|\\]|{|}|\\(|\\))/, true, false)) {\n    return 'bracket';\n  }\n\n  // literals looking like function calls\n  if (stream.match(reFunctionLike, true, false)) {\n    stream.backUp(1);\n    return 'variableName.function';\n  }\n\n  // all other identifiers\n  if (stream.match(reIdentifier, true, false)) {\n    return 'variable';\n  }\n\n  // operators; note that operators like @@ or /; are matched separately for each symbol.\n  if (stream.match(/(?:\\\\|\\+|\\-|\\*|\\/|,|;|\\.|:|@|~|=|>|<|&|\\||_|`|'|\\^|\\?|!|%|#)/, true, false)) {\n    return 'operator';\n  }\n\n  // everything else is an error\n  return 'error';\n}\n\nfunction tokenString(stream, state) {\n  var next, end = false, escaped = false;\n  while ((next = stream.next()) != null) {\n    if (next === '\"' && !escaped) {\n      end = true;\n      break;\n    }\n    escaped = !escaped && next === '\\\\';\n  }\n  if (end && !escaped) {\n    state.tokenize = tokenBase;\n  }\n  return 'string';\n};\n\nfunction tokenComment(stream, state) {\n  var prev, next;\n  while((next = stream.next()) != null) {\n    if (prev === '*' && next === '/') {\n      state.tokenize = tokenBase;\n      break;\n    }\n    prev = next;\n  }\n  return 'comment';\n}\n\nfunction currentScope(state) {\n  var scope = null;\n  if (state.scopes.length > 0)\n    scope = state.scopes[state.scopes.length - 1];\n  return scope;\n}\n\nexport const yacas = {\n  name: \"yacas\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      scopes: []\n    };\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize !== tokenBase && state.tokenize !== null)\n      return null;\n\n    var delta = 0;\n    if (textAfter === ']' || textAfter === '];' ||\n        textAfter === '}' || textAfter === '};' ||\n        textAfter === ');')\n      delta = -1;\n\n    return (state.scopes.length + delta) * cx.unit;\n  },\n\n  languageData: {\n    electricInput: /[{}\\[\\]()\\;]/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n"], "names": [], "sourceRoot": ""}