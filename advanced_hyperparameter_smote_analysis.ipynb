# Install required packages
# !pip install pandas numpy matplotlib seaborn scikit-learn imbalanced-learn scipy

# Import libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Machine Learning imports
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import (
    train_test_split, cross_val_score, RandomizedSearchCV, StratifiedKFold
)
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.naive_bayes import MultinomialNB
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support,
    cohen_kappa_score, matthews_corrcoef, classification_report
)
from sklearn.preprocessing import LabelEncoder

# SMOTE for class balancing
from imblearn.over_sampling import SMOTE
from imblearn.pipeline import Pipeline as ImbPipeline

# Statistical analysis
from scipy.stats import friedmanchisquare
import itertools

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
pd.set_option('display.max_columns', None)

print("✅ All libraries imported successfully!")

def load_and_prepare_data():
    """Load and prepare the dataset."""
    print("📊 Loading and preparing dataset...")
    
    try:
        df = pd.read_csv('google_play_reviews_DigitalBank_sentiment_analysis.csv')
        print(f"✅ Dataset loaded successfully! Shape: {df.shape}")
        
        # Check required columns
        required_columns = [
            'stemmed_text', 'sentiment_score_based', 'sentiment_textblob',
            'sentiment_vader', 'sentiment_ensemble'
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return None, None, None
        
        # Remove rows with missing values
        df_clean = df.dropna(subset=required_columns)
        print(f"📋 Clean dataset shape: {df_clean.shape}")
        
        # Prepare TF-IDF features
        print("🔧 Preparing TF-IDF features...")
        vectorizer = TfidfVectorizer(
            max_features=5000,
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.95
        )
        
        X = vectorizer.fit_transform(df_clean['stemmed_text'].fillna(''))
        print(f"✅ TF-IDF features prepared: {X.shape}")
        
        # Encode labels for each sentiment method
        sentiment_columns = ['sentiment_score_based', 'sentiment_textblob', 'sentiment_vader', 'sentiment_ensemble']
        encoded_labels = {}
        label_encoders = {}
        
        for method in sentiment_columns:
            le = LabelEncoder()
            encoded_labels[method] = le.fit_transform(df_clean[method])
            label_encoders[method] = le
            print(f"   {method}: {le.classes_} -> {list(range(len(le.classes_)))}")
        
        return X, encoded_labels, sentiment_columns
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return None, None, None

# Load and prepare data
X, encoded_labels, sentiment_columns = load_and_prepare_data()

def analyze_class_distributions(encoded_labels, sentiment_columns):
    """Analyze class distributions for each labeling method."""
    print("\n🔍 Analyzing class distributions...")
    
    distribution_stats = {}
    
    for method in sentiment_columns:
        y = encoded_labels[method]
        unique, counts = np.unique(y, return_counts=True)
        
        # Calculate imbalance metrics
        majority_class = counts.max()
        minority_class = counts.min()
        imbalance_ratio = majority_class / minority_class if minority_class > 0 else float('inf')
        
        distribution_stats[method] = {
            'classes': unique,
            'counts': counts,
            'imbalance_ratio': imbalance_ratio,
            'total_samples': len(y)
        }
        
        print(f"📊 {method}:")
        print(f"   Classes: {unique}")
        print(f"   Counts: {counts}")
        print(f"   Imbalance Ratio: {imbalance_ratio:.2f}")
    
    return distribution_stats

# Analyze class distributions
if X is not None:
    distribution_stats = analyze_class_distributions(encoded_labels, sentiment_columns)
else:
    print("❌ Cannot analyze distributions - data loading failed")

def define_hyperparameter_grids():
    """Define hyperparameter grids for RandomizedSearchCV."""
    param_grids = {
        'SVM_Linear': {
            'C': [0.1, 1, 10, 100],
            'kernel': ['linear'],
            'class_weight': [None, 'balanced']
        },
        'SVM_RBF': {
            'C': [0.1, 1, 10, 100],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
            'kernel': ['rbf'],
            'class_weight': [None, 'balanced']
        },
        'Random_Forest': {
            'n_estimators': [50, 100, 200, 300],
            'max_depth': [None, 10, 20, 30],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4],
            'class_weight': [None, 'balanced']
        },
        'Logistic_Regression': {
            'C': [0.1, 1, 10, 100],
            'penalty': ['l1', 'l2'],
            'solver': ['liblinear', 'saga'],
            'class_weight': [None, 'balanced'],
            'max_iter': [1000, 2000]
        },
        'Naive_Bayes': {
            'alpha': [0.1, 0.5, 1.0, 2.0, 5.0]
        }
    }
    
    base_algorithms = {
        'SVM_Linear': SVC(random_state=42),
        'SVM_RBF': SVC(random_state=42),
        'Random_Forest': RandomForestClassifier(random_state=42),
        'Logistic_Regression': LogisticRegression(random_state=42),
        'Naive_Bayes': MultinomialNB()
    }
    
    return param_grids, base_algorithms

# Define hyperparameter grids
param_grids, base_algorithms = define_hyperparameter_grids()
print("🎯 Hyperparameter grids defined for:")
for algo in param_grids.keys():
    print(f"   - {algo}: {len(param_grids[algo])} parameters")

def perform_hyperparameter_tuning(X, encoded_labels, sentiment_columns):
    """Perform hyperparameter tuning using RandomizedSearchCV."""
    print("\n🎯 HYPERPARAMETER TUNING WITH RANDOMIZEDSEARCHCV")
    print("="*60)
    
    param_grids, base_algorithms = define_hyperparameter_grids()
    tuning_results = []
    best_models = {}
    
    for sentiment_method in sentiment_columns:
        print(f"\n🎯 Tuning for {sentiment_method}...")
        y = encoded_labels[sentiment_method]
        
        # Split data for tuning
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        method_best_models = {}
        
        for algo_name in param_grids.keys():
            print(f"   🔧 Tuning {algo_name}...")
            
            try:
                # Setup RandomizedSearchCV
                random_search = RandomizedSearchCV(
                    estimator=base_algorithms[algo_name],
                    param_distributions=param_grids[algo_name],
                    n_iter=20,  # Increased iterations for better search
                    cv=5,       # Simple 5-fold CV for hyperparameter search only
                    scoring='accuracy',
                    random_state=42,
                    n_jobs=-1,
                    verbose=0
                )
                
                # Fit the random search
                random_search.fit(X_train, y_train)
                
                # Get best model and evaluate
                best_model = random_search.best_estimator_
                best_cv_score = random_search.best_score_
                best_params = random_search.best_params_
                
                # Test on holdout set
                y_pred = best_model.predict(X_test)
                test_accuracy = accuracy_score(y_test, y_pred)
                test_precision, test_recall, test_f1, _ = precision_recall_fscore_support(y_test, y_pred, average='weighted')
                test_kappa = cohen_kappa_score(y_test, y_pred)
                
                # Store results
                tuning_result = {
                    'Sentiment_Method': sentiment_method,
                    'Algorithm': algo_name,
                    'Best_CV_Score': best_cv_score,
                    'Test_Accuracy': test_accuracy,
                    'Test_Precision': test_precision,
                    'Test_Recall': test_recall,
                    'Test_F1': test_f1,
                    'Test_Kappa': test_kappa,
                    'Best_Params': str(best_params)
                }
                
                tuning_results.append(tuning_result)
                method_best_models[algo_name] = best_model
                
                print(f"      ✅ Best CV Score: {best_cv_score:.4f}")
                print(f"      📊 Test Accuracy: {test_accuracy:.4f}")
                print(f"      🎯 Test F1: {test_f1:.4f}")
                print(f"      🎯 Best Params: {best_params}")
                
            except Exception as e:
                print(f"      ❌ Error tuning {algo_name}: {str(e)}")
                continue
        
        best_models[sentiment_method] = method_best_models
    
    return pd.DataFrame(tuning_results), best_models

# Perform hyperparameter tuning
if X is not None:
    tuning_df, best_models = perform_hyperparameter_tuning(X, encoded_labels, sentiment_columns)
    print(f"\n✅ Hyperparameter tuning completed! Results shape: {tuning_df.shape}")
    
    if len(tuning_df) > 0:
        print("\n📊 TUNING RESULTS SUMMARY:")
        summary = tuning_df.groupby('Sentiment_Method')[['Test_Accuracy', 'Test_F1', 'CV_Std']].agg(['mean', 'max']).round(4)
        print(summary)
else:
    print("❌ Cannot perform tuning - data loading failed")

def perform_smote_analysis(X, encoded_labels, sentiment_columns):
    """Perform SMOTE analysis for class balancing."""
    print("\n⚖️ SMOTE ANALYSIS FOR CLASS BALANCING")
    print("="*60)
    
    smote_results = []
    cv_strategy = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    # Test algorithms
    algorithms_to_test = {
        'SVM_Linear': SVC(kernel='linear', random_state=42),
        'SVM_RBF': SVC(kernel='rbf', random_state=42),
        'Random_Forest': RandomForestClassifier(n_estimators=100, random_state=42),
        'Logistic_Regression': LogisticRegression(random_state=42, max_iter=1000),
        'Naive_Bayes': MultinomialNB()
    }
    
    for sentiment_method in sentiment_columns:
        print(f"\n🎯 SMOTE analysis for {sentiment_method}...")
        y = encoded_labels[sentiment_method]
        
        # Check original distribution
        unique, counts = np.unique(y, return_counts=True)
        print(f"   📊 Original distribution: {dict(zip(unique, counts))}")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Apply SMOTE
        try:
            # Adjust k_neighbors based on minority class size
            min_class_size = min(np.bincount(y_train))
            k_neighbors = min(5, min_class_size - 1) if min_class_size > 1 else 1
            
            smote = SMOTE(random_state=42, k_neighbors=k_neighbors)
            X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)
            
            # Check new distribution
            unique_smote, counts_smote = np.unique(y_train_smote, return_counts=True)
            print(f"   ⚖️ SMOTE distribution: {dict(zip(unique_smote, counts_smote))}")
            print(f"   📈 Samples: {len(X_train)} → {len(X_train_smote)} (+{len(X_train_smote) - len(X_train)})")
            
            for algo_name, algorithm in algorithms_to_test.items():
                print(f"   🤖 Testing {algo_name}...")
                
                try:
                    # Without SMOTE - Cross-validation
                    cv_scores_original = cross_val_score(algorithm, X_train, y_train, cv=cv_strategy, scoring='accuracy')
                    cv_mean_original = cv_scores_original.mean()
                    cv_std_original = cv_scores_original.std()
                    
                    # Train and test without SMOTE
                    model_original = algorithm.fit(X_train, y_train)
                    y_pred_original = model_original.predict(X_test)
                    
                    acc_original = accuracy_score(y_test, y_pred_original)
                    f1_original = precision_recall_fscore_support(y_test, y_pred_original, average='weighted')[2]
                    kappa_original = cohen_kappa_score(y_test, y_pred_original)
                    
                    # With SMOTE - Cross-validation
                    cv_scores_smote = cross_val_score(algorithm, X_train_smote, y_train_smote, cv=cv_strategy, scoring='accuracy')
                    cv_mean_smote = cv_scores_smote.mean()
                    cv_std_smote = cv_scores_smote.std()
                    
                    # Train and test with SMOTE
                    model_smote = algorithm.fit(X_train_smote, y_train_smote)
                    y_pred_smote = model_smote.predict(X_test)
                    
                    acc_smote = accuracy_score(y_test, y_pred_smote)
                    f1_smote = precision_recall_fscore_support(y_test, y_pred_smote, average='weighted')[2]
                    kappa_smote = cohen_kappa_score(y_test, y_pred_smote)
                    
                    # Calculate improvements
                    acc_improvement = acc_smote - acc_original
                    f1_improvement = f1_smote - f1_original
                    kappa_improvement = kappa_smote - kappa_original
                    cv_improvement = cv_mean_smote - cv_mean_original
                    
                    # Store results
                    smote_result = {
                        'Sentiment_Method': sentiment_method,
                        'Algorithm': algo_name,
                        'Original_CV_Mean': cv_mean_original,
                        'Original_CV_Std': cv_std_original,
                        'SMOTE_CV_Mean': cv_mean_smote,
                        'SMOTE_CV_Std': cv_std_smote,
                        'CV_Improvement': cv_improvement,
                        'Original_Accuracy': acc_original,
                        'SMOTE_Accuracy': acc_smote,
                        'Accuracy_Improvement': acc_improvement,
                        'Original_F1': f1_original,
                        'SMOTE_F1': f1_smote,
                        'F1_Improvement': f1_improvement,
                        'Original_Kappa': kappa_original,
                        'SMOTE_Kappa': kappa_smote,
                        'Kappa_Improvement': kappa_improvement,
                        'Original_Samples': len(X_train),
                        'SMOTE_Samples': len(X_train_smote)
                    }
                    
                    smote_results.append(smote_result)
                    
                    print(f"      📊 CV: {cv_mean_original:.4f}(±{cv_std_original:.4f}) → {cv_mean_smote:.4f}(±{cv_std_smote:.4f})")
                    print(f"      📊 Accuracy: {acc_original:.4f} → {acc_smote:.4f} ({acc_improvement:+.4f})")
                    print(f"      🎯 F1: {f1_original:.4f} → {f1_smote:.4f} ({f1_improvement:+.4f})")
                    print(f"      🤝 Kappa: {kappa_original:.4f} → {kappa_smote:.4f} ({kappa_improvement:+.4f})")
                    
                except Exception as e:
                    print(f"      ❌ Error with {algo_name}: {str(e)}")
                    continue
                    
        except Exception as e:
            print(f"   ❌ SMOTE error for {sentiment_method}: {str(e)}")
            continue
    
    return pd.DataFrame(smote_results)

# Perform SMOTE analysis
if X is not None:
    smote_df = perform_smote_analysis(X, encoded_labels, sentiment_columns)
    print(f"\n✅ SMOTE analysis completed! Results shape: {smote_df.shape}")
    
    if len(smote_df) > 0:
        print("\n📊 SMOTE IMPACT SUMMARY:")
        
        # Overall statistics
        avg_acc_improvement = smote_df['Accuracy_Improvement'].mean()
        avg_f1_improvement = smote_df['F1_Improvement'].mean()
        avg_kappa_improvement = smote_df['Kappa_Improvement'].mean()
        
        print(f"   📊 Average Accuracy Improvement: {avg_acc_improvement:+.4f}")
        print(f"   🎯 Average F1-Score Improvement: {avg_f1_improvement:+.4f}")
        print(f"   🤝 Average Kappa Improvement: {avg_kappa_improvement:+.4f}")
        
        # Count positive improvements
        positive_acc = (smote_df['Accuracy_Improvement'] > 0).sum()
        positive_f1 = (smote_df['F1_Improvement'] > 0).sum()
        positive_kappa = (smote_df['Kappa_Improvement'] > 0).sum()
        total_experiments = len(smote_df)
        
        print(f"\n📊 POSITIVE IMPROVEMENTS:")
        print(f"   Accuracy: {positive_acc}/{total_experiments} ({positive_acc/total_experiments*100:.1f}%)")
        print(f"   F1-Score: {positive_f1}/{total_experiments} ({positive_f1/total_experiments*100:.1f}%)")
        print(f"   Kappa: {positive_kappa}/{total_experiments} ({positive_kappa/total_experiments*100:.1f}%)")
else:
    print("❌ Cannot perform SMOTE analysis - data loading failed")

def perform_final_cross_validation(X, encoded_labels, sentiment_columns, best_models):
    """Perform comprehensive cross-validation for final model evaluation."""
    print("\n🔄 FINAL CROSS-VALIDATION FOR MODEL TRAINING AND TESTING")
    print("="*70)
    
    # Cross-validation strategy for final evaluation
    cv_strategy = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    final_cv_results = []
    
    for sentiment_method in sentiment_columns:
        print(f"\n🎯 Cross-validation for {sentiment_method}...")
        y = encoded_labels[sentiment_method]
        
        # Get best models for this sentiment method
        if sentiment_method in best_models:
            method_best_models = best_models[sentiment_method]
            
            for algo_name, best_model in method_best_models.items():
                print(f"   🤖 Cross-validating {algo_name}...")
                
                try:
                    # Perform cross-validation with SMOTE in each fold
                    cv_scores_accuracy = []
                    cv_scores_f1 = []
                    cv_scores_precision = []
                    cv_scores_recall = []
                    
                    for fold, (train_idx, val_idx) in enumerate(cv_strategy.split(X, y)):
                        print(f"      📊 Fold {fold + 1}/5...")
                        
                        # Split data for this fold
                        X_train_fold = X[train_idx]
                        X_val_fold = X[val_idx]
                        y_train_fold = y[train_idx]
                        y_val_fold = y[val_idx]
                        
                        # Apply SMOTE to training fold
                        min_class_size = min(np.bincount(y_train_fold))
                        k_neighbors = min(5, min_class_size - 1) if min_class_size > 1 else 1
                        
                        smote = SMOTE(random_state=42, k_neighbors=k_neighbors)
                        X_train_smote, y_train_smote = smote.fit_resample(X_train_fold, y_train_fold)
                        
                        # Train model on SMOTE-balanced data
                        fold_model = best_model.fit(X_train_smote, y_train_smote)
                        
                        # Predict on validation fold
                        y_pred_fold = fold_model.predict(X_val_fold)
                        
                        # Calculate metrics for this fold
                        fold_accuracy = accuracy_score(y_val_fold, y_pred_fold)
                        fold_precision, fold_recall, fold_f1, _ = precision_recall_fscore_support(
                            y_val_fold, y_pred_fold, average='weighted'
                        )
                        
                        cv_scores_accuracy.append(fold_accuracy)
                        cv_scores_f1.append(fold_f1)
                        cv_scores_precision.append(fold_precision)
                        cv_scores_recall.append(fold_recall)
                    
                    # Calculate cross-validation statistics
                    cv_accuracy_mean = np.mean(cv_scores_accuracy)
                    cv_accuracy_std = np.std(cv_scores_accuracy)
                    cv_f1_mean = np.mean(cv_scores_f1)
                    cv_f1_std = np.std(cv_scores_f1)
                    cv_precision_mean = np.mean(cv_scores_precision)
                    cv_recall_mean = np.mean(cv_scores_recall)
                    
                    # Store cross-validation results
                    cv_result = {
                        'Sentiment_Method': sentiment_method,
                        'Algorithm': algo_name,
                        'CV_Accuracy_Mean': cv_accuracy_mean,
                        'CV_Accuracy_Std': cv_accuracy_std,
                        'CV_F1_Mean': cv_f1_mean,
                        'CV_F1_Std': cv_f1_std,
                        'CV_Precision_Mean': cv_precision_mean,
                        'CV_Recall_Mean': cv_recall_mean,
                        'CV_Scores_Accuracy': cv_scores_accuracy,
                        'CV_Scores_F1': cv_scores_f1
                    }
                    
                    final_cv_results.append(cv_result)
                    
                    print(f"      ✅ CV Accuracy: {cv_accuracy_mean:.4f} (±{cv_accuracy_std:.4f})")
                    print(f"      🎯 CV F1-Score: {cv_f1_mean:.4f} (±{cv_f1_std:.4f})")
                    print(f"      📊 CV Precision: {cv_precision_mean:.4f}")
                    print(f"      📈 CV Recall: {cv_recall_mean:.4f}")
                    
                except Exception as e:
                    print(f"      ❌ Error with {algo_name}: {str(e)}")
                    continue
    
    return pd.DataFrame(final_cv_results)

# Perform final cross-validation
if X is not None and 'best_models' in locals():
    final_cv_df = perform_final_cross_validation(X, encoded_labels, sentiment_columns, best_models)
    print(f"\n✅ Final cross-validation completed! Results shape: {final_cv_df.shape}")
    
    if len(final_cv_df) > 0:
        print("\n📊 CROSS-VALIDATION SUMMARY:")
        
        # Best performing configurations based on CV
        print("\n🏆 BEST PERFORMING CONFIGURATIONS (Cross-Validation):")
        for method in sentiment_columns:
            method_data = final_cv_df[final_cv_df['Sentiment_Method'] == method]
            if len(method_data) > 0:
                best_cv = method_data.loc[method_data['CV_Accuracy_Mean'].idxmax()]
                print(f"\n   {method.upper()}:")
                print(f"      Best Algorithm: {best_cv['Algorithm']}")
                print(f"      CV Accuracy: {best_cv['CV_Accuracy_Mean']:.4f} (±{best_cv['CV_Accuracy_Std']:.4f})")
                print(f"      CV F1-Score: {best_cv['CV_F1_Mean']:.4f} (±{best_cv['CV_F1_Std']:.4f})")
                print(f"      CV Precision: {best_cv['CV_Precision_Mean']:.4f}")
                print(f"      CV Recall: {best_cv['CV_Recall_Mean']:.4f}")
        
        # Overall statistics
        print(f"\n📈 OVERALL CROSS-VALIDATION STATISTICS:")
        print(f"   Average CV Accuracy: {final_cv_df['CV_Accuracy_Mean'].mean():.4f}")
        print(f"   Average CV F1-Score: {final_cv_df['CV_F1_Mean'].mean():.4f}")
        print(f"   Best CV Accuracy: {final_cv_df['CV_Accuracy_Mean'].max():.4f}")
        print(f"   Best CV F1-Score: {final_cv_df['CV_F1_Mean'].max():.4f}")
        
        # Stability analysis
        print(f"\n📊 MODEL STABILITY ANALYSIS:")
        print(f"   Most Stable (lowest std): {final_cv_df.loc[final_cv_df['CV_Accuracy_Std'].idxmin(), 'Algorithm']}")
        print(f"   Stability Score: {final_cv_df['CV_Accuracy_Std'].min():.4f}")
        print(f"   Average Stability: {final_cv_df['CV_Accuracy_Std'].mean():.4f}")
else:
    print("❌ Cannot perform final cross-validation - missing best models")

def create_comprehensive_visualizations(tuning_df, smote_df, sentiment_columns):
    """Create comprehensive visualizations of results."""
    print("\n📊 CREATING COMPREHENSIVE VISUALIZATIONS")
    print("="*60)
    
    # Create figure with subplots
    fig, axes = plt.subplots(3, 2, figsize=(20, 18))
    fig.suptitle('Advanced Hyperparameter Tuning and SMOTE Analysis Results', fontsize=16, fontweight='bold')
    
    # 1. Hyperparameter Tuning Results - Test Accuracy
    if len(tuning_df) > 0:
        ax1 = axes[0, 0]
        tuning_pivot = tuning_df.pivot(index='Sentiment_Method', columns='Algorithm', values='Test_Accuracy')
        sns.heatmap(tuning_pivot, annot=True, fmt='.3f', cmap='YlOrRd', ax=ax1, cbar_kws={'label': 'Test Accuracy'})
        ax1.set_title('🎯 Hyperparameter Tuning Results\n(Test Accuracy)', fontweight='bold')
        ax1.set_xlabel('Algorithm')
        ax1.set_ylabel('Sentiment Method')
        
        # 2. Cross-Validation Stability
        ax2 = axes[0, 1]
        cv_pivot = tuning_df.pivot(index='Sentiment_Method', columns='Algorithm', values='CV_Std')
        sns.heatmap(cv_pivot, annot=True, fmt='.4f', cmap='YlOrRd_r', ax=ax2, cbar_kws={'label': 'CV Std (Lower=Better)'})
        ax2.set_title('📊 Cross-Validation Stability\n(Lower Standard Deviation = More Stable)', fontweight='bold')
        ax2.set_xlabel('Algorithm')
        ax2.set_ylabel('Sentiment Method')
    
    # 3. SMOTE Impact on Accuracy
    if len(smote_df) > 0:
        ax3 = axes[1, 0]
        smote_acc_pivot = smote_df.pivot(index='Sentiment_Method', columns='Algorithm', values='Accuracy_Improvement')
        sns.heatmap(smote_acc_pivot, annot=True, fmt='.3f', cmap='RdYlGn', center=0, ax=ax3,
                    cbar_kws={'label': 'Accuracy Improvement'})
        ax3.set_title('⚖️ SMOTE Impact on Accuracy\n(Green=Improvement, Red=Degradation)', fontweight='bold')
        ax3.set_xlabel('Algorithm')
        ax3.set_ylabel('Sentiment Method')
        
        # 4. SMOTE Impact on F1-Score
        ax4 = axes[1, 1]
        smote_f1_pivot = smote_df.pivot(index='Sentiment_Method', columns='Algorithm', values='F1_Improvement')
        sns.heatmap(smote_f1_pivot, annot=True, fmt='.3f', cmap='RdYlGn', center=0, ax=ax4,
                    cbar_kws={'label': 'F1-Score Improvement'})
        ax4.set_title('🎯 SMOTE Impact on F1-Score\n(Green=Improvement, Red=Degradation)', fontweight='bold')
        ax4.set_xlabel('Algorithm')
        ax4.set_ylabel('Sentiment Method')
        
        # 5. SMOTE Cross-Validation Improvement
        ax5 = axes[2, 0]
        smote_cv_pivot = smote_df.pivot(index='Sentiment_Method', columns='Algorithm', values='CV_Improvement')
        sns.heatmap(smote_cv_pivot, annot=True, fmt='.3f', cmap='RdYlGn', center=0, ax=ax5,
                    cbar_kws={'label': 'CV Score Improvement'})
        ax5.set_title('📈 SMOTE Impact on Cross-Validation\n(Green=Improvement, Red=Degradation)', fontweight='bold')
        ax5.set_xlabel('Algorithm')
        ax5.set_ylabel('Sentiment Method')
    
    # 6. Overall Performance Comparison
    ax6 = axes[2, 1]
    if len(tuning_df) > 0 and len(smote_df) > 0:
        # Compare best tuned vs best SMOTE for each method
        comparison_data = []
        
        for method in sentiment_columns:
            # Best tuned result
            tuned_data = tuning_df[tuning_df['Sentiment_Method'] == method]
            if len(tuned_data) > 0:
                best_tuned = tuned_data.loc[tuned_data['Test_Accuracy'].idxmax()]
                comparison_data.append({
                    'Method': method.replace('sentiment_', ''),
                    'Type': 'Tuned',
                    'Accuracy': best_tuned['Test_Accuracy']
                })
            
            # Best SMOTE result
            smote_data = smote_df[smote_df['Sentiment_Method'] == method]
            if len(smote_data) > 0:
                best_smote = smote_data.loc[smote_data['SMOTE_Accuracy'].idxmax()]
                comparison_data.append({
                    'Method': method.replace('sentiment_', ''),
                    'Type': 'SMOTE',
                    'Accuracy': best_smote['SMOTE_Accuracy']
                })
        
        if comparison_data:
            comp_df = pd.DataFrame(comparison_data)
            sns.barplot(data=comp_df, x='Method', y='Accuracy', hue='Type', ax=ax6)
            ax6.set_title('🏆 Best Performance Comparison\n(Tuned vs SMOTE)', fontweight='bold')
            ax6.set_xlabel('Sentiment Method')
            ax6.set_ylabel('Accuracy')
            ax6.tick_params(axis='x', rotation=45)
            ax6.legend(title='Approach')
            
            # Add value labels on bars
            for container in ax6.containers:
                ax6.bar_label(container, fmt='%.3f', rotation=90, fontsize=8)
    
    plt.tight_layout()
    plt.savefig('advanced_hyperparameter_smote_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Comprehensive visualization saved as 'advanced_hyperparameter_smote_analysis.png'")

# Create visualizations
if X is not None and 'tuning_df' in locals() and 'smote_df' in locals():
    create_comprehensive_visualizations(tuning_df, smote_df, sentiment_columns)
else:
    print("❌ Cannot create visualizations - missing data or analysis results")

def generate_final_recommendations(tuning_df, smote_df, sentiment_columns):
    """Generate final recommendations based on all analyses."""
    print("\n🎯 GENERATING FINAL RECOMMENDATIONS")
    print("="*60)
    
    recommendations = {}
    
    for method in sentiment_columns:
        method_recommendations = {
            'best_tuned': None,
            'best_smote': None,
            'overall_best': None
        }
        
        # Best tuned configuration
        if len(tuning_df) > 0:
            tuned_data = tuning_df[tuning_df['Sentiment_Method'] == method]
            if len(tuned_data) > 0:
                best_tuned = tuned_data.loc[tuned_data['Test_Accuracy'].idxmax()]
                method_recommendations['best_tuned'] = {
                    'algorithm': best_tuned['Algorithm'],
                    'accuracy': best_tuned['Test_Accuracy'],
                    'f1': best_tuned['Test_F1'],
                    'cv_score': best_tuned['Best_CV_Score'],
                    'cv_std': best_tuned['CV_Std'],
                    'params': best_tuned['Best_Params']
                }
        
        # Best SMOTE configuration
        if len(smote_df) > 0:
            smote_data = smote_df[smote_df['Sentiment_Method'] == method]
            if len(smote_data) > 0:
                best_smote = smote_data.loc[smote_data['SMOTE_Accuracy'].idxmax()]
                method_recommendations['best_smote'] = {
                    'algorithm': best_smote['Algorithm'],
                    'accuracy': best_smote['SMOTE_Accuracy'],
                    'f1': best_smote['SMOTE_F1'],
                    'cv_score': best_smote['SMOTE_CV_Mean'],
                    'cv_std': best_smote['SMOTE_CV_Std'],
                    'improvement': best_smote['Accuracy_Improvement']
                }
        
        # Determine overall best
        best_configs = []
        if method_recommendations['best_tuned']:
            best_configs.append(('tuned', method_recommendations['best_tuned']['accuracy']))
        if method_recommendations['best_smote']:
            best_configs.append(('smote', method_recommendations['best_smote']['accuracy']))
        
        if best_configs:
            overall_best_type = max(best_configs, key=lambda x: x[1])[0]
            method_recommendations['overall_best'] = overall_best_type
        
        recommendations[method] = method_recommendations
    
    # Print recommendations
    print("\n🏆 FINAL RECOMMENDATIONS BY SENTIMENT METHOD:")
    print("="*60)
    
    for method, rec in recommendations.items():
        print(f"\n📊 {method.upper()}:")
        
        if rec['best_tuned']:
            tuned = rec['best_tuned']
            print(f"   🎯 Best Tuned Configuration:")
            print(f"      Algorithm: {tuned['algorithm']}")
            print(f"      Test Accuracy: {tuned['accuracy']:.4f}")
            print(f"      Test F1-Score: {tuned['f1']:.4f}")
            print(f"      CV Score: {tuned['cv_score']:.4f} (±{tuned['cv_std']:.4f})")
            print(f"      Parameters: {tuned['params']}")
        
        if rec['best_smote']:
            smote = rec['best_smote']
            print(f"   ⚖️ Best SMOTE Configuration:")
            print(f"      Algorithm: {smote['algorithm']}")
            print(f"      Test Accuracy: {smote['accuracy']:.4f}")
            print(f"      Test F1-Score: {smote['f1']:.4f}")
            print(f"      CV Score: {smote['cv_score']:.4f} (±{smote['cv_std']:.4f})")
            print(f"      Improvement: {smote['improvement']:+.4f}")
        
        if rec['overall_best']:
            print(f"   🏅 RECOMMENDED APPROACH: {rec['overall_best'].upper()}")
    
    # Overall best method
    all_best_scores = []
    for method, rec in recommendations.items():
        if rec['best_tuned']:
            all_best_scores.append((method, 'tuned', rec['best_tuned']['accuracy']))
        if rec['best_smote']:
            all_best_scores.append((method, 'smote', rec['best_smote']['accuracy']))
    
    if all_best_scores:
        overall_best = max(all_best_scores, key=lambda x: x[2])
        print(f"\n🎖️ OVERALL BEST CONFIGURATION:")
        print(f"   Method: {overall_best[0]}")
        print(f"   Approach: {overall_best[1].upper()}")
        print(f"   Accuracy: {overall_best[2]:.4f}")
    
    return recommendations

def export_results(tuning_df, smote_df):
    """Export all results to CSV files."""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    print(f"\n📁 EXPORTING RESULTS...")
    
    # Export tuning results
    if len(tuning_df) > 0:
        tuning_filename = f'hyperparameter_tuning_results_{timestamp}.csv'
        tuning_df.to_csv(tuning_filename, index=False)
        print(f"   🎯 Hyperparameter tuning: {tuning_filename}")
    
    # Export SMOTE results
    if len(smote_df) > 0:
        smote_filename = f'smote_analysis_results_{timestamp}.csv'
        smote_df.to_csv(smote_filename, index=False)
        print(f"   ⚖️ SMOTE analysis: {smote_filename}")
    
    return timestamp

# Generate recommendations and export results
if X is not None and 'tuning_df' in locals() and 'smote_df' in locals():
    recommendations = generate_final_recommendations(tuning_df, smote_df, sentiment_columns)
    timestamp = export_results(tuning_df, smote_df)
    
    # Final summary
    print("\n" + "="*80)
    print("✅ ADVANCED ANALYSIS COMPLETED SUCCESSFULLY!")
    print("="*80)
    print(f"📊 Hyperparameter tuning experiments: {len(tuning_df)}")
    print(f"⚖️ SMOTE analysis experiments: {len(smote_df)}")
    print(f"📁 Results exported with timestamp: {timestamp}")
    print(f"📈 Visualization saved: advanced_hyperparameter_smote_analysis.png")
    print("\n🎯 Ready for production implementation with optimized configurations!")
else:
    print("❌ Cannot generate recommendations - missing analysis results")