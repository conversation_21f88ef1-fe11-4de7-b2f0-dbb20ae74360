Metadata-Version: 2.4
Name: jupyterlab
Version: 4.4.4
Summary: JupyterLab computational environment
Project-URL: Homepage, https://jupyter.org
Project-URL: Changelog, https://jupyterlab.readthedocs.io/en/stable/getting_started/changelog.html
Project-URL: Documentation, https://jupyterlab.readthedocs.io
Project-URL: Source, https://github.com/jupyterlab/jupyterlab
Project-URL: Issues, https://github.com/jupyterlab/jupyterlab/issues/new/choose
Project-URL: Zulip, https://jupyter.zulipchat.com/#narrow/channel/469762-jupyterlab
Project-URL: Pypi, https://pypi.org/project/jupyterlab
Author-email: Jupyter Development Team <<EMAIL>>
License: Copyright (c) 2015-2025 Project Jupyter Contributors
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        1. Redistributions of source code must retain the above copyright notice, this
           list of conditions and the following disclaimer.
        
        2. Redistributions in binary form must reproduce the above copyright notice,
           this list of conditions and the following disclaimer in the documentation
           and/or other materials provided with the distribution.
        
        3. Neither the name of the copyright holder nor the names of its
           contributors may be used to endorse or promote products derived from
           this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
        
        Semver File License
        ===================
        
        The semver.py file is from https://github.com/podhmo/python-semver
        which is licensed under the "MIT" license.  See the semver.py file for details.
License-File: LICENSE
Keywords: ipython,jupyter
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Jupyter
Classifier: Framework :: Jupyter :: JupyterLab
Classifier: Framework :: Jupyter :: JupyterLab :: 4
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.9
Requires-Dist: async-lru>=1.0.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: importlib-metadata>=4.8.3; python_version < '3.10'
Requires-Dist: ipykernel>=6.5.0
Requires-Dist: jinja2>=3.0.3
Requires-Dist: jupyter-core
Requires-Dist: jupyter-lsp>=2.0.0
Requires-Dist: jupyter-server<3,>=2.4.0
Requires-Dist: jupyterlab-server<3,>=2.27.1
Requires-Dist: notebook-shim>=0.2
Requires-Dist: packaging
Requires-Dist: setuptools>=41.1.0
Requires-Dist: tomli>=1.2.2; python_version < '3.11'
Requires-Dist: tornado>=6.2.0
Requires-Dist: traitlets
Provides-Extra: dev
Requires-Dist: build; extra == 'dev'
Requires-Dist: bump2version; extra == 'dev'
Requires-Dist: coverage; extra == 'dev'
Requires-Dist: hatch; extra == 'dev'
Requires-Dist: pre-commit; extra == 'dev'
Requires-Dist: pytest-cov; extra == 'dev'
Requires-Dist: ruff==0.11.4; extra == 'dev'
Provides-Extra: docs
Requires-Dist: jsx-lexer; extra == 'docs'
Requires-Dist: myst-parser; extra == 'docs'
Requires-Dist: pydata-sphinx-theme>=0.13.0; extra == 'docs'
Requires-Dist: pytest; extra == 'docs'
Requires-Dist: pytest-check-links; extra == 'docs'
Requires-Dist: pytest-jupyter; extra == 'docs'
Requires-Dist: sphinx-copybutton; extra == 'docs'
Requires-Dist: sphinx<8.2.0,>=1.8; extra == 'docs'
Provides-Extra: docs-screenshots
Requires-Dist: altair==5.5.0; extra == 'docs-screenshots'
Requires-Dist: ipython==8.16.1; extra == 'docs-screenshots'
Requires-Dist: ipywidgets==8.1.5; extra == 'docs-screenshots'
Requires-Dist: jupyterlab-geojson==3.4.0; extra == 'docs-screenshots'
Requires-Dist: jupyterlab-language-pack-zh-cn==4.3.post1; extra == 'docs-screenshots'
Requires-Dist: matplotlib==3.10.0; extra == 'docs-screenshots'
Requires-Dist: nbconvert>=7.0.0; extra == 'docs-screenshots'
Requires-Dist: pandas==2.2.3; extra == 'docs-screenshots'
Requires-Dist: scipy==1.15.1; extra == 'docs-screenshots'
Requires-Dist: vega-datasets==0.9.0; extra == 'docs-screenshots'
Provides-Extra: test
Requires-Dist: coverage; extra == 'test'
Requires-Dist: pytest-check-links>=0.7; extra == 'test'
Requires-Dist: pytest-console-scripts; extra == 'test'
Requires-Dist: pytest-cov; extra == 'test'
Requires-Dist: pytest-jupyter>=0.5.3; extra == 'test'
Requires-Dist: pytest-timeout; extra == 'test'
Requires-Dist: pytest-tornasync; extra == 'test'
Requires-Dist: pytest>=7.0; extra == 'test'
Requires-Dist: requests; extra == 'test'
Requires-Dist: requests-cache; extra == 'test'
Requires-Dist: virtualenv; extra == 'test'
Provides-Extra: upgrade-extension
Requires-Dist: copier<10,>=9; extra == 'upgrade-extension'
Requires-Dist: jinja2-time<0.3; extra == 'upgrade-extension'
Requires-Dist: pydantic<3.0; extra == 'upgrade-extension'
Requires-Dist: pyyaml-include<3.0; extra == 'upgrade-extension'
Requires-Dist: tomli-w<2.0; extra == 'upgrade-extension'
Description-Content-Type: text/markdown

**[Installation](#installation)** |
**[Documentation](https://jupyterlab.readthedocs.io)** |
**[Contributing](#contributing)** |
**[License](#license)** |
**[Team](#team)** |
**[Getting help](#getting-help)** |

# [JupyterLab](https://jupyterlab.readthedocs.io)

[![PyPI version](https://badge.fury.io/py/jupyterlab.svg)](https://badge.fury.io/py/jupyterlab)
[![Downloads](https://static.pepy.tech/badge/jupyterlab/month)](https://pepy.tech/project/jupyterlab)
[![Build Status](https://github.com/jupyterlab/jupyterlab/workflows/Linux%20Tests/badge.svg)](https://github.com/jupyterlab/jupyterlab/actions?query=branch%3Amain+workflow%3A%22Linux+Tests%22)
[![Build Status](https://github.com/jupyterlab/jupyterlab/workflows/Windows%20Tests/badge.svg)](https://github.com/jupyterlab/jupyterlab/actions?query=branch%3Amain+workflow%3A%22Windows+Tests%22)
[![Documentation Status](https://readthedocs.org/projects/jupyterlab/badge/?version=stable)](http://jupyterlab.readthedocs.io/en/stable/)
[![Crowdin](https://badges.crowdin.net/jupyterlab/localized.svg)](https://crowdin.com/project/jupyterlab)
[![OpenSSF Best Practices](https://www.bestpractices.dev/projects/8675/badge)](https://www.bestpractices.dev/projects/8675)
[![OpenSSF Scorecard](https://api.scorecard.dev/projects/github.com/jupyterlab/jupyterlab/badge)](https://scorecard.dev/viewer/?uri=github.com/jupyterlab/jupyterlab)
[![GitHub](https://img.shields.io/badge/issue_tracking-github-blue.svg)](https://github.com/jupyterlab/jupyterlab/issues)
[![Discourse](https://img.shields.io/badge/help_forum-discourse-blue.svg)](https://discourse.jupyter.org/c/jupyterlab)
[![Zulip](https://img.shields.io/badge/social_chat-zulip-blue.svg)](https://jupyter.zulipchat.com/#narrow/channel/469762-jupyterlab)
[![Gitpod](https://img.shields.io/badge/gitpod_editor-open-blue.svg)](https://gitpod.io/#https://github.com/jupyterlab/jupyterlab)

[![Binder](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/jupyterlab/jupyterlab-demo/HEAD?urlpath=lab/tree/demo)

An extensible environment for interactive and reproducible computing, based on the
Jupyter Notebook and Architecture.

[JupyterLab](http://jupyterlab.readthedocs.io/en/stable/) is the next-generation user interface for [Project Jupyter](https://jupyter.org) offering
all the familiar building blocks of the classic Jupyter Notebook (notebook,
terminal, text editor, file browser, rich outputs, etc.) in a flexible and
powerful user interface.

JupyterLab can be extended using [npm](https://www.npmjs.com/) packages
that use our public APIs. The _prebuilt_ extensions can be distributed
via [PyPI](https://pypi.org/search/?q=jupyterlab&o=-created&c=Framework+%3A%3A+Jupyter),
conda, and other package managers. The _source_ extensions can be installed
directly from npm (search for [jupyterlab-extension](https://www.npmjs.com/search?q=keywords:jupyterlab-extension)) but require an additional build step.
You can also find JupyterLab extensions exploring GitHub topic [jupyterlab-extension](https://github.com/topics/jupyterlab-extension).
To learn more about extensions, see the [user documentation](https://jupyterlab.readthedocs.io/en/stable/user/extensions.html).

Read the current JupyterLab documentation on [ReadTheDocs](http://jupyterlab.readthedocs.io/en/stable/).

> [!IMPORTANT]
> JupyterLab 3 reached its end of maintenance date on May 15, 2024. Fixes for critical issues will still be backported until December 31, 2024. If you are still running JupyterLab 3, we strongly encourage you to **upgrade to JupyterLab 4 as soon as possible.** For more information, see [JupyterLab 3 end of maintenance](https://blog.jupyter.org/jupyterlab-3-end-of-maintenance-879778927db2) on the Jupyter Blog.

---

## Getting started

### Installation

If you use [conda](https://docs.conda.io/en/latest/), [mamba](https://mamba.readthedocs.io/en/latest/), or [pip](https://docs.python.org/3/installing/index.html), you can install JupyterLab with one of the following commands.

- If you use conda:
  ```shell
  conda install -c conda-forge jupyterlab
  ```
- If you use mamba:
  ```shell
  mamba install -c conda-forge jupyterlab
  ```
- If you use pip:
  ```shell
  pip install jupyterlab
  ```
  If installing using `pip install --user`, you must add the user-level `bin` directory to your `PATH` environment variable in order to launch `jupyter lab`. If you are using a Unix derivative (e.g., FreeBSD, GNU/Linux, macOS), you can do this by running `export PATH="$HOME/.local/bin:$PATH"`.

For more detailed instructions, consult the [installation guide](http://jupyterlab.readthedocs.io/en/stable/getting_started/installation.html). Project installation instructions from the git sources are available in the [contributor documentation](CONTRIBUTING.md).

#### Installing with Previous Versions of Jupyter Notebook

When using a version of Jupyter Notebook earlier than 5.3, the following command must be run after installing JupyterLab to enable the JupyterLab server extension:

```bash
jupyter serverextension enable --py jupyterlab --sys-prefix
```

### Running

Start up JupyterLab using:

```bash
jupyter lab
```

JupyterLab will open automatically in the browser. See the [documentation](http://jupyterlab.readthedocs.io/en/stable/getting_started/starting.html) for additional details.

If you encounter an error like "Command 'jupyter' not found", please make sure `PATH` environment variable is set correctly. Alternatively, you can start up JupyterLab using `~/.local/bin/jupyter lab` without changing the `PATH` environment variable.

### Prerequisites and Supported Browsers

The latest versions of the following browsers are currently _known to work_:

- Firefox
- Chrome
- Safari

See our [documentation](http://jupyterlab.readthedocs.io/en/stable/getting_started/installation.html) for additional details.

---

## Getting help

We encourage you to ask questions on the [Discourse forum](https://discourse.jupyter.org/c/jupyterlab). A question answered there can become a useful resource for others.

### Bug report

To report a bug please read the [guidelines](https://jupyterlab.readthedocs.io/en/stable/getting_started/issue.html) and then open a [Github issue](https://github.com/jupyterlab/jupyterlab/issues/new?labels=bug%2C+status%3ANeeds+Triage&template=bug_report.md). To keep resolved issues self-contained, the [lock bot](https://github.com/apps/lock) will lock closed issues as resolved after a period of inactivity. If a related discussion is still needed after an issue is locked, please open a new issue and reference the old issue.

### Feature request

We also welcome suggestions for new features as they help make the project more useful for everyone. To request a feature please use the [feature request template](https://github.com/jupyterlab/jupyterlab/issues/new?labels=enhancement%2C+status%3ANeeds+Triage&template=feature_request.md).

---

## Development

### Extending JupyterLab

To start developing an extension for JupyterLab, see the [developer documentation](https://jupyterlab.readthedocs.io/en/stable/extension/extension_dev.html) and the [API docs](https://jupyterlab.readthedocs.io/en/stable/api/).

### Contributing

To contribute code or documentation to JupyterLab itself, please read the [contributor documentation](https://jupyterlab.readthedocs.io/en/stable/developer/contributing.html).

JupyterLab follows the Jupyter [Community Guides](https://jupyter.readthedocs.io/en/latest/community/content-community.html).

### License

JupyterLab uses a shared copyright model that enables all contributors to maintain the
copyright on their contributions. All code is licensed under the terms of the revised [BSD license](https://github.com/jupyterlab/jupyterlab/blob/4.4.x/LICENSE).

### Team

JupyterLab is part of [Project Jupyter](https://jupyter.org/) and is developed by an open community. The maintenance team is assisted by a much larger group of contributors to JupyterLab and Project Jupyter as a whole.

JupyterLab's current maintainers are listed in alphabetical order, with affiliation, and main areas of contribution:

- Mehmet Bektas, Netflix (general development, extensions).
- Alex Bozarth, IBM (general development, extensions).
- Eric Charles, Datalayer, (general development, extensions).
- Frédéric Collonval, WebScIT (general development, extensions).
- Martha Cryan, Mito (general development, extensions).
- Afshin Darian, QuantStack (co-creator, application/high-level architecture,
  prolific contributions throughout the code base).
- Vidar T. Fauske, JPMorgan Chase (general development, extensions).
- Brian Granger, AWS (co-creator, strategy, vision, management, UI/UX design,
  architecture).
- Jason Grout, Databricks (co-creator, vision, general development).
- Michał Krassowski, Quansight (general development, extensions).
- Max Klein, JPMorgan Chase (UI Package, build system, general development, extensions).
- Gonzalo Peña-Castellanos, QuanSight (general development, i18n, extensions).
- Fernando Perez, UC Berkeley (co-creator, vision).
- Isabela Presedo-Floyd, QuanSight Labs (design/UX).
- Steven Silvester, MongoDB (co-creator, release management, packaging,
  prolific contributions throughout the code base).
- Jeremy Tuloup, QuantStack (general development, extensions).

Maintainer emeritus:

- Chris Colbert, Project Jupyter (co-creator, application/low-level architecture,
  technical leadership, vision, PhosphorJS)
- Jessica Forde, Project Jupyter (demo, documentation)
- Tim George, Cal Poly (UI/UX design, strategy, management, user needs analysis).
- Cameron Oelsen, Cal Poly (UI/UX design).
- Ian Rose, Quansight/City of LA (general core development, extensions).
- Andrew Schlaepfer, Bloomberg (general development, extensions).
- Saul Shanabrook, Quansight (general development, extensions)

This list is provided to give the reader context on who we are and how our team functions.
To be listed, please submit a pull request with your information.

---

### Weekly Dev Meeting

We have videoconference meetings every week where we discuss what we have been working on and get feedback from one another.

Anyone is welcome to attend, if they would like to discuss a topic or just listen in.

- When: Wednesdays 9:00 AM Pacific Time (USA)
- Where: [`jovyan` Zoom](https://zoom.us/j/95228013874?pwd=Ep7HIk8t9JP6VToxt1Wj4P7K5PshC0.1)
- What: [Meeting notes](https://hackmd.io/Y7fBMQPSQ1C08SDGI-fwtg?both)

> Notes are archived on [GitHub Jupyter Frontends team compass](https://github.com/jupyterlab/frontends-team-compass/issues).
