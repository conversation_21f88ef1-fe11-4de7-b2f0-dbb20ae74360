{"version": 3, "file": "1091.f006368c55525d627dc3.js?v=f006368c55525d627dc3", "mappings": ";;;;;;;;;;;AAA2C;AACpC,aAAa,oEAAU;AAC9B;AACA;AACA,KAAK,0FAA0F;;AAE/F;AACA,MAAM,8CAA8C;AACpD,MAAM,8CAA8C;AACpD,MAAM,8CAA8C;;AAEpD;AACA,KAAK,2SAA2S;;AAEhT;AACA,KAAK,wFAAwF;AAC7F,KAAK,4EAA4E;;AAEjF;AACA,KAAK,soFAAsoF;AAC3oF,KAAK,sFAAsF;AAC3F,KAAK,2FAA2F;;AAEhG;AACA,KAAK,64BAA64B;AACl5B,KAAK,gbAAgb;;AAErb;AACA,KAAK,YAAY,wRAAwR,sCAAsC;;AAE/U;AACA,KAAK,YAAY,oMAAoM,sCAAsC;;AAE3P;AACA,KAAK,YAAY,oEAAoE,sCAAsC;;AAE3H;AACA,KAAK,YAAY,mIAAmI,sCAAsC;;AAE1L;AACA,KAAK,YAAY,oHAAoH,oCAAoC;;AAEzK;AACA,KAAK,YAAY,iGAAiG,mCAAmC;;AAErJ;AACA,KAAK,YAAY,gBAAgB,oCAAoC;AACrE,KAAK,YAAY,oCAAoC,mCAAmC;;AAExF;AACA,KAAK,YAAY,uBAAuB;;AAExC;AACA,KAAK,iDAAiD;;AAEtD;AACA,KAAK,yCAAyC;;AAE9C;AACA,KAAK,wCAAwC;;AAE7C;AACA,KAAK,WAAW,YAAY,kCAAkC;;AAE9D;AACA,KAAK;AACL;AACA;AACA,KAAK,kDAAkD;AACvD,KAAK;AACL;AACA;AACA;AACA,+FAA+F,yCAAyC;AACxI,oBAAoB,mBAAmB;AACvC;AACA,CAAC;;;;;;;;;;;;AC5EM;AACP;AACA,kBAAkB,kCAAkC;AACpD;AACA;AACA,oBAAoB,iBAAiB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,KAAK;AACL;AACA,eAAe;AACf;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,kBAAkB,gBAAgB;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,oBAAoB;AAC9C;AACA,kCAAkC,2CAA2C;AAC7E;AACA;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB;AACjB,sBAAsB,kBAAkB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/nsis.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/simple-mode.js"], "sourcesContent": ["import {simpleMode} from \"./simple-mode.js\"\nexport const nsis = simpleMode({\n  start:[\n    // Numbers\n    {regex: /(?:[+-]?)(?:0x[\\d,a-f]+)|(?:0o[0-7]+)|(?:0b[0,1]+)|(?:\\d+.?\\d*)/, token: \"number\"},\n\n    // Strings\n    { regex: /\"(?:[^\\\\\"]|\\\\.)*\"?/, token: \"string\" },\n    { regex: /'(?:[^\\\\']|\\\\.)*'?/, token: \"string\" },\n    { regex: /`(?:[^\\\\`]|\\\\.)*`?/, token: \"string\" },\n\n    // Compile Time Commands\n    {regex: /^\\s*(?:\\!(addincludedir|addplugindir|appendfile|assert|cd|define|delfile|echo|error|execute|finalize|getdllversion|gettlbversion|include|insertmacro|macro|macroend|makensis|packhdr|pragma|searchparse|searchreplace|system|tempfile|undef|uninstfinalize|verbose|warning))\\b/i, token: \"keyword\"},\n\n    // Conditional Compilation\n    {regex: /^\\s*(?:\\!(if(?:n?def)?|ifmacron?def|macro))\\b/i, token: \"keyword\", indent: true},\n    {regex: /^\\s*(?:\\!(else|endif|macroend))\\b/i, token: \"keyword\", dedent: true},\n\n    // Runtime Commands\n    {regex: /^\\s*(?:Abort|AddBrandingImage|AddSize|AllowRootDirInstall|AllowSkipFiles|AutoCloseWindow|BGFont|BGGradient|BrandingText|BringToFront|Call|CallInstDLL|Caption|ChangeUI|CheckBitmap|ClearErrors|CompletedText|ComponentText|CopyFiles|CRCCheck|CreateDirectory|CreateFont|CreateShortCut|Delete|DeleteINISec|DeleteINIStr|DeleteRegKey|DeleteRegValue|DetailPrint|DetailsButtonText|DirText|DirVar|DirVerify|EnableWindow|EnumRegKey|EnumRegValue|Exch|Exec|ExecShell|ExecShellWait|ExecWait|ExpandEnvStrings|File|FileBufSize|FileClose|FileErrorText|FileOpen|FileRead|FileReadByte|FileReadUTF16LE|FileReadWord|FileWriteUTF16LE|FileSeek|FileWrite|FileWriteByte|FileWriteWord|FindClose|FindFirst|FindNext|FindWindow|FlushINI|GetCurInstType|GetCurrentAddress|GetDlgItem|GetDLLVersion|GetDLLVersionLocal|GetErrorLevel|GetFileTime|GetFileTimeLocal|GetFullPathName|GetFunctionAddress|GetInstDirError|GetKnownFolderPath|GetLabelAddress|GetTempFileName|GetWinVer|Goto|HideWindow|Icon|IfAbort|IfErrors|IfFileExists|IfRebootFlag|IfRtlLanguage|IfShellVarContextAll|IfSilent|InitPluginsDir|InstallButtonText|InstallColors|InstallDir|InstallDirRegKey|InstProgressFlags|InstType|InstTypeGetText|InstTypeSetText|Int64Cmp|Int64CmpU|Int64Fmt|IntCmp|IntCmpU|IntFmt|IntOp|IntPtrCmp|IntPtrCmpU|IntPtrOp|IsWindow|LangString|LicenseBkColor|LicenseData|LicenseForceSelection|LicenseLangString|LicenseText|LoadAndSetImage|LoadLanguageFile|LockWindow|LogSet|LogText|ManifestDPIAware|ManifestLongPathAware|ManifestMaxVersionTested|ManifestSupportedOS|MessageBox|MiscButtonText|Name|Nop|OutFile|Page|PageCallbacks|PEAddResource|PEDllCharacteristics|PERemoveResource|PESubsysVer|Pop|Push|Quit|ReadEnvStr|ReadINIStr|ReadRegDWORD|ReadRegStr|Reboot|RegDLL|Rename|RequestExecutionLevel|ReserveFile|Return|RMDir|SearchPath|SectionGetFlags|SectionGetInstTypes|SectionGetSize|SectionGetText|SectionIn|SectionSetFlags|SectionSetInstTypes|SectionSetSize|SectionSetText|SendMessage|SetAutoClose|SetBrandingImage|SetCompress|SetCompressor|SetCompressorDictSize|SetCtlColors|SetCurInstType|SetDatablockOptimize|SetDateSave|SetDetailsPrint|SetDetailsView|SetErrorLevel|SetErrors|SetFileAttributes|SetFont|SetOutPath|SetOverwrite|SetRebootFlag|SetRegView|SetShellVarContext|SetSilent|ShowInstDetails|ShowUninstDetails|ShowWindow|SilentInstall|SilentUnInstall|Sleep|SpaceTexts|StrCmp|StrCmpS|StrCpy|StrLen|SubCaption|Target|Unicode|UninstallButtonText|UninstallCaption|UninstallIcon|UninstallSubCaption|UninstallText|UninstPage|UnRegDLL|Var|VIAddVersionKey|VIFileVersion|VIProductVersion|WindowIcon|WriteINIStr|WriteRegBin|WriteRegDWORD|WriteRegExpandStr|WriteRegMultiStr|WriteRegNone|WriteRegStr|WriteUninstaller|XPStyle)\\b/i, token: \"keyword\"},\n    {regex: /^\\s*(?:Function|PageEx|Section(?:Group)?)\\b/i, token: \"keyword\", indent: true},\n    {regex: /^\\s*(?:(Function|PageEx|Section(?:Group)?)End)\\b/i, token: \"keyword\", dedent: true},\n\n    // Command Options\n    {regex: /\\b(?:ARCHIVE|FILE_ATTRIBUTE_ARCHIVE|FILE_ATTRIBUTE_HIDDEN|FILE_ATTRIBUTE_NORMAL|FILE_ATTRIBUTE_OFFLINE|FILE_ATTRIBUTE_READONLY|FILE_ATTRIBUTE_SYSTEM|FILE_ATTRIBUTE_TEMPORARY|HIDDEN|HKCC|HKCR(32|64)?|HKCU(32|64)?|HKDD|HKEY_CLASSES_ROOT|HKEY_CURRENT_CONFIG|HKEY_CURRENT_USER|HKEY_DYN_DATA|HKEY_LOCAL_MACHINE|HKEY_PERFORMANCE_DATA|HKEY_USERS|HKLM(32|64)?|HKPD|HKU|IDABORT|IDCANCEL|IDD_DIR|IDD_INST|IDD_INSTFILES|IDD_LICENSE|IDD_SELCOM|IDD_UNINST|IDD_VERIFY|IDIGNORE|IDNO|IDOK|IDRETRY|IDYES|MB_ABORTRETRYIGNORE|MB_DEFBUTTON1|MB_DEFBUTTON2|MB_DEFBUTTON3|MB_DEFBUTTON4|MB_ICONEXCLAMATION|MB_ICONINFORMATION|MB_ICONQUESTION|MB_ICONSTOP|MB_OK|MB_OKCANCEL|MB_RETRYCANCEL|MB_RIGHT|MB_RTLREADING|MB_SETFOREGROUND|MB_TOPMOST|MB_USERICON|MB_YESNO|MB_YESNOCANCEL|NORMAL|OFFLINE|READONLY|SHCTX|SHELL_CONTEXT|SW_HIDE|SW_SHOWDEFAULT|SW_SHOWMAXIMIZED|SW_SHOWMINIMIZED|SW_SHOWNORMAL|SYSTEM|TEMPORARY)\\b/i, token: \"atom\"},\n    {regex: /\\b(?:admin|all|amd64-unicode|auto|both|bottom|bzip2|components|current|custom|directory|false|force|hide|highest|ifdiff|ifnewer|instfiles|lastused|leave|left|license|listonly|lzma|nevershow|none|normal|notset|off|on|right|show|silent|silentlog|textonly|top|true|try|un\\.components|un\\.custom|un\\.directory|un\\.instfiles|un\\.license|uninstConfirm|user|Win10|Win7|Win8|WinVista|x-86-(ansi|unicode)|zlib)\\b/i, token: \"builtin\"},\n\n    // LogicLib.nsh\n    {regex: /\\$\\{(?:And(?:If(?:Not)?|Unless)|Break|Case(?:2|3|4|5|Else)?|Continue|Default|Do(?:Until|While)?|Else(?:If(?:Not)?|Unless)?|End(?:If|Select|Switch)|Exit(?:Do|For|While)|For(?:Each)?|If(?:Cmd|Not(?:Then)?|Then)?|Loop(?:Until|While)?|Or(?:If(?:Not)?|Unless)|Select|Switch|Unless|While)\\}/i, token: \"variable-2\", indent: true},\n\n    // FileFunc.nsh\n    {regex: /\\$\\{(?:BannerTrimPath|DirState|DriveSpace|Get(BaseName|Drives|ExeName|ExePath|FileAttributes|FileExt|FileName|FileVersion|Options|OptionsS|Parameters|Parent|Root|Size|Time)|Locate|RefreshShellIcons)\\}/i, token: \"variable-2\", dedent: true},\n\n    // Memento.nsh\n    {regex: /\\$\\{(?:Memento(?:Section(?:Done|End|Restore|Save)?|UnselectedSection))\\}/i, token: \"variable-2\", dedent: true},\n\n    // TextFunc.nsh\n    {regex: /\\$\\{(?:Config(?:Read|ReadS|Write|WriteS)|File(?:Join|ReadFromEnd|Recode)|Line(?:Find|Read|Sum)|Text(?:Compare|CompareS)|TrimNewLines)\\}/i, token: \"variable-2\", dedent: true},\n\n    // WinVer.nsh\n    {regex: /\\$\\{(?:(?:At(?:Least|Most)|Is)(?:ServicePack|Win(?:7|8|10|95|98|200(?:0|3|8(?:R2)?)|ME|NT4|Vista|XP))|Is(?:NT|Server))\\}/i, token: \"variable\", dedent: true},\n\n    // WordFunc.nsh\n    {regex: /\\$\\{(?:StrFilterS?|Version(?:Compare|Convert)|Word(?:AddS?|Find(?:(?:2|3)X)?S?|InsertS?|ReplaceS?))\\}/i, token: \"keyword\", dedent: true},\n\n    // x64.nsh\n    {regex: /\\$\\{(?:RunningX64)\\}/i, token: \"variable\", dedent: true},\n    {regex: /\\$\\{(?:Disable|Enable)X64FSRedirection\\}/i, token: \"keyword\", dedent: true},\n\n    // Line Comment\n    {regex: /(#|;).*/, token: \"comment\"},\n\n    // Block Comment\n    {regex: /\\/\\*/, token: \"comment\", next: \"comment\"},\n\n    // Operator\n    {regex: /[-+\\/*=<>!]+/, token: \"operator\"},\n\n    // Variable\n    {regex: /\\$\\w[\\w\\.]*/, token: \"variable\"},\n\n    // Constant\n    {regex: /\\${[\\!\\w\\.:-]+}/, token: \"variableName.constant\"},\n\n    // Language String\n    {regex: /\\$\\([\\!\\w\\.:-]+\\)/, token: \"atom\"}\n  ],\n  comment: [\n    {regex: /.*?\\*\\//, token: \"comment\", next: \"start\"},\n    {regex: /.*/, token: \"comment\"}\n  ],\n  languageData: {\n    name: \"nsis\",\n    indentOnInput: /^\\s*((Function|PageEx|Section|Section(Group)?)End|(\\!(endif|macroend))|\\$\\{(End(If|Unless|While)|Loop(Until)|Next)\\})$/i,\n    commentTokens: {line: \"#\", block: {open: \"/*\", close: \"*/\"}}\n  }\n});\n\n", "export function simpleMode(states) {\n  ensureState(states, \"start\");\n  var states_ = {}, meta = states.languageData || {}, hasIndentation = false;\n  for (var state in states) if (state != meta && states.hasOwnProperty(state)) {\n    var list = states_[state] = [], orig = states[state];\n    for (var i = 0; i < orig.length; i++) {\n      var data = orig[i];\n      list.push(new Rule(data, states));\n      if (data.indent || data.dedent) hasIndentation = true;\n    }\n  }\n  return {\n    name: meta.name,\n    startState: function() {\n      return {state: \"start\", pending: null, indent: hasIndentation ? [] : null};\n    },\n    copyState: function(state) {\n      var s = {state: state.state, pending: state.pending, indent: state.indent && state.indent.slice(0)};\n      if (state.stack)\n        s.stack = state.stack.slice(0);\n      return s;\n    },\n    token: tokenFunction(states_),\n    indent: indentFunction(states_, meta),\n    mergeTokens: meta.mergeTokens,\n    languageData: meta\n  }\n};\n\nfunction ensureState(states, name) {\n  if (!states.hasOwnProperty(name))\n    throw new Error(\"Undefined state \" + name + \" in simple mode\");\n}\n\nfunction toRegex(val, caret) {\n  if (!val) return /(?:)/;\n  var flags = \"\";\n  if (val instanceof RegExp) {\n    if (val.ignoreCase) flags = \"i\";\n    val = val.source;\n  } else {\n    val = String(val);\n  }\n  return new RegExp((caret === false ? \"\" : \"^\") + \"(?:\" + val + \")\", flags);\n}\n\nfunction asToken(val) {\n  if (!val) return null;\n  if (val.apply) return val\n  if (typeof val == \"string\") return val.replace(/\\./g, \" \");\n  var result = [];\n  for (var i = 0; i < val.length; i++)\n    result.push(val[i] && val[i].replace(/\\./g, \" \"));\n  return result;\n}\n\nfunction Rule(data, states) {\n  if (data.next || data.push) ensureState(states, data.next || data.push);\n  this.regex = toRegex(data.regex);\n  this.token = asToken(data.token);\n  this.data = data;\n}\n\nfunction tokenFunction(states) {\n  return function(stream, state) {\n    if (state.pending) {\n      var pend = state.pending.shift();\n      if (state.pending.length == 0) state.pending = null;\n      stream.pos += pend.text.length;\n      return pend.token;\n    }\n\n    var curState = states[state.state];\n    for (var i = 0; i < curState.length; i++) {\n      var rule = curState[i];\n      var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);\n      if (matches) {\n        if (rule.data.next) {\n          state.state = rule.data.next;\n        } else if (rule.data.push) {\n          (state.stack || (state.stack = [])).push(state.state);\n          state.state = rule.data.push;\n        } else if (rule.data.pop && state.stack && state.stack.length) {\n          state.state = state.stack.pop();\n        }\n\n        if (rule.data.indent)\n          state.indent.push(stream.indentation() + stream.indentUnit);\n        if (rule.data.dedent)\n          state.indent.pop();\n        var token = rule.token\n        if (token && token.apply) token = token(matches)\n        if (matches.length > 2 && rule.token && typeof rule.token != \"string\") {\n          state.pending = [];\n          for (var j = 2; j < matches.length; j++)\n            if (matches[j])\n              state.pending.push({text: matches[j], token: rule.token[j - 1]});\n          stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));\n          return token[0];\n        } else if (token && token.join) {\n          return token[0];\n        } else {\n          return token;\n        }\n      }\n    }\n    stream.next();\n    return null;\n  };\n}\n\nfunction indentFunction(states, meta) {\n  return function(state, textAfter) {\n    if (state.indent == null || meta.dontIndentStates && meta.dontIndentStates.indexOf(state.state) > -1)\n      return null\n\n    var pos = state.indent.length - 1, rules = states[state.state];\n    scan: for (;;) {\n      for (var i = 0; i < rules.length; i++) {\n        var rule = rules[i];\n        if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {\n          var m = rule.regex.exec(textAfter);\n          if (m && m[0]) {\n            pos--;\n            if (rule.next || rule.push) rules = states[rule.next || rule.push];\n            textAfter = textAfter.slice(m[0].length);\n            continue scan;\n          }\n        }\n      }\n      break;\n    }\n    return pos < 0 ? 0 : state.indent[pos];\n  };\n}\n"], "names": [], "sourceRoot": ""}