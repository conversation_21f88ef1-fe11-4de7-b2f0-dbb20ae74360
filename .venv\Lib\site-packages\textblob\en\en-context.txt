;;;   
;;;   The contextual rules are based on <PERSON><PERSON>'s rule based tagger v1.14,
;;;   trained on <PERSON> corpus and Penn Treebank.
;;;   
IN VB PREVTAG PRP
NN VB PREVTAG TO
VBP VB PREV1OR2OR3TAG MD
NN VB PREV1OR2TAG MD
VB NN PREV1OR2TAG DT
VBD VBN PREV1OR2OR3TAG VBZ
VBN VBD PREVTAG PRP
VBN VBD PREVTAG NNP
VBD VBN PREVTAG VBD
VBP VB PREVTAG TO
POS VBZ PREVTAG PRP
VB VBP PREVTAG NNS
IN RB WDAND2AFT as as
VBD VBN PREV1OR2WD have
IN WDT NEXT1OR2TAG VB
VB VBP PREVTAG PRP
VBP VB PREV1OR2WD n't
IN WDT NEXTTAG VBZ
JJ NNP NEXTTAG NNP
IN WDT NEXTTAG VBD
JJ NN NEXTWD of
VBD VBN PREV1OR2WD be
JJR RBR NEXTTAG JJ
IN WDT NEXTTAG VBP
JJS RBS WDNEXTTAG most JJ
VBN VBD SURROUNDTAG NN DT
NNS VBZ PREVTAG PRP
POS VBZ NEXT1OR2TAG DT
NNP NN SURROUNDTAG STAART NNS
VBD VBN NEXTWD by
VB NN PREV1OR2TAG IN
VB VBP PREVTAG WDT
VBG NN PREVTAG JJ
NNS VBZ NEXTTAG DT
VBN VBD PREVTAG WP
NN VBP PREVTAG NNS
VB NN PREVTAG NN
NN VB PREVWD n't
NN VBG NEXTTAG DT
RB JJ NEXTTAG NN
NN VBP PREVTAG PRP
VBN VBD SURROUNDTAG NNS DT
VB NN PREV1OR2TAG POS
JJ NN NEXTTAG VBD
RB RP WDNEXTTAG up DT
JJ VB PREVTAG TO
VBN VBD SURROUNDTAG , DT
VBN VBD PREVWD that
VB VBP PREVBIGRAM NNS RB
NNP JJ SURROUNDTAG STAART NN
VB VBN PREVTAG VBZ
NNP JJ WDNEXTTAG American NNS
JJ RB NEXTTAG JJR
NNS NN CURWD yen
IN WDT NEXTTAG VBD
DT IN WDAND2TAGAFT that NNS
POS VBZ PREVWD that
JJ VB PREVTAG MD
VB NN PREVTAG JJ
JJR RBR NEXTTAG RB
VBD VBN PREV1OR2WD are
NN JJ WDNEXTTAG executive NN
NNP JJ WDNEXTTAG American NN
VBN VBD PREVTAG WDT
VBD VBN PREVBIGRAM VBD RB
JJ NN SURROUNDTAG DT .
NNP JJ NEXTWD German
VBN VB PREVTAG TO
VBN VBD PREVBIGRAM NNP RB
RB IN RBIGRAM up to
VB VBP PREVTAG WP
JJ NN SURROUNDTAG DT IN
IN DT NEXTWD 's
VBD VBN WDNEXTTAG ended NNP
VBD VBN SURROUNDTAG DT NN
NNS NNP NEXTTAG NNP
NN NNP NEXTTAG NNP
VBG NN SURROUNDTAG DT IN
NNP JJ SURROUNDTAG STAART NNS
RB RP WDPREVTAG VB up
VBN VBD PREVBIGRAM PRP RB
JJ RB NEXTTAG VBN
NN VBP PREVTAG RB
NNS VBZ PREVTAG RB
POS VBZ PREVTAG WP
VB VBN PREVWD have
NN PDT WDNEXTTAG half DT
IN WDT NEXTTAG MD
POS VBZ PREVTAG DT
NN NNP CURWD Integrated
POS '' NEXT1OR2TAG ''
VBD VBN PREVTAG IN
JJR RBR NEXT1OR2TAG VBN
JJS RBS WDNEXTTAG most RB
JJ NN SURROUNDTAG JJ IN
VBZ NNS PREVTAG JJ
NNS VBZ WDPREVTAG JJ is
JJ NN NEXTTAG VBZ
VBP NN PREVTAG DT
JJ NN SURROUNDTAG JJ .
NNPS NNP NEXTTAG NNP
WDT DT PREVTAG CC
RB IN WDNEXTTAG so PRP
VBP NN PREVWD earnings
NN VBG PREVWD is
NNS VBZ PREV1OR2WD Mr.
VBZ NNS PREVWD the
RB RP WDPREVTAG VBN up
NNPS NNS PREVTAG STAART
VBN VBD SURROUNDTAG NN JJ
VBP VB PREV2TAG VB
RBR JJR NEXTTAG NNS
JJ NN SURROUNDTAG DT ,
JJ NN SURROUNDTAG IN .
NN VB PREVTAG TO
VB NN PREVTAG VB
NN VBP PREVWD who
RB RP WDPREVTAG VBG up
NN RB WDNEXTTAG right RB
VBZ POS WDPREVTAG NNP 's
JJ RP WDNEXTTAG up NN
VBN VBD SURROUNDTAG NN NN
VBN VBD SURROUNDTAG CC DT
JJ NN NEXTBIGRAM MD VB
JJ RB WDNEXTTAG early IN
JJ VBN SURROUNDTAG STAART IN
IN RB RBIGRAM though ,
VBD VBN PREV1OR2WD been
DT PDT WDNEXTTAG all DT
VBN VBD PREVBIGRAM NN RB
NN VB PREVWD help
VBP VB PREV1OR2WD not
VBP NN PREVTAG JJ
DT WDT PREVTAG NNS
NN VBP PREVTAG WDT
VB RB RBIGRAM close to
NNS VBZ PREVBIGRAM , WDT
IN RP WDNEXTTAG out DT
DT RB NEXTWD longer
IN JJ SURROUNDTAG DT NN
DT WDT SURROUNDTAG NN VBZ
IN VB NEXT2TAG VB
IN NN PREVTAG DT
VBN VBD SURROUNDTAG NNS NNS
IN RB RBIGRAM about $
EX RB NEXT1OR2TAG IN
NN VBG NEXTTAG PRP$
NN VBG CURWD living
VBZ NNS PREVTAG PRP$
RBR JJR NEXTTAG NN
RBR JJR CURWD higher
VB VBP PREVBIGRAM PRP RB
NN VB PREVTAG MD
VB NN PREV1OR2TAG PRP$
RP IN PREV1OR2TAG ,
VB JJ PREVTAG DT
DT IN PREVWD out
POS VBZ PREVTAG EX
JJ NN NEXTTAG POS
NN JJ CURWD first
VBD VBN PREVWD the
NNS VBZ WDPREVTAG NNP plans
NNP NNS SURROUNDTAG STAART IN
RB JJ NEXTTAG NNS
JJ RB CURWD just
VBP NN PREVWD sales
NNS NNPS PREVWD Orange
VB VBN PREVTAG VBD
WDT DT PREVTAG IN
NN JJ WDNEXTTAG right NN
NN VBG WDNEXTTAG operating IN
JJ VBN CURWD insured
JJ NNP LBIGRAM STAART U.S.
IN DT NEXTTAG STAART
POS '' PREV1OR2OR3TAG ``
NN JJ WDNEXTTAG official NN
NNP JJ CURWD Irish
JJ RB NEXTTAG RBR
VBG NN WDPREVTAG DT selling
VBP VB PREV1OR2OR3TAG MD
WDT IN NEXTTAG PRP
EX RB NEXTTAG .
VBN VBD SURROUNDTAG NNS PRP$
VBN VBD CURWD said
JJ RB PREVTAG MD
NN VBG NEXTBIGRAM JJ NNS
JJ RB WDNEXTTAG late IN
VBG NN PREVTAG PRP$
VBZ NNS NEXTTAG VBP
NN NNP WDPREVTAG DT CD
NN VBN PREVWD be
JJS RBS NEXTTAG VBN
VBN VBD SURROUNDTAG NN PRP$
VBN VBD SURROUNDTAG NNS JJ
VBN VBD SURROUNDTAG NNS NN
VBD VBN WDNEXTTAG increased NN
VBZ NNS NEXTWD of
IN RP WDAND2TAGAFT out NNS
JJ NNP NEXTTAG POS
RB RP WDNEXTTAG down DT
CD NNS CURWD 1970s
VBG NNP CURWD Working
VBN VB PREVTAG MD
JJ NN NEXTBIGRAM CC NN
NN JJ SURROUNDTAG STAART NNS
VBN VBD PREVBIGRAM , CC
IN RB NEXTBIGRAM . STAART
NN VBG PREVWD was
NNP NNPS CURWD Cowboys
VBZ NNS PREVWD phone
NNP NNS SURROUNDTAG STAART VBP
RBR JJR WDNEXTTAG lower JJ
PRP$ PRP NEXTTAG IN
VBD VB PREVTAG TO
JJ NN WDPREVTAG NN chief
JJ NN SURROUNDTAG JJ ,
NN JJ WDPREVTAG DT third
VBN VBD SURROUNDTAG NNS NNP
NNP NN SURROUNDTAG STAART NN
NNP NN CURWD HDTV
VBG NN SURROUNDTAG DT ,
VBG NN SURROUNDTAG DT .
NNS VBZ PREVTAG WP
NN VB SURROUNDTAG CC DT
NNPS NNP WDAND2TAGBFR IN Securities
RP IN PREVTAG NNS
VBP NN LBIGRAM funds rate
VBP NN WDPREVTAG NNS market
DT RB RBIGRAM either .
VBN NN SURROUNDTAG DT IN
VBD VB PREV1OR2OR3TAG MD
NN JJ NEXTWD oil
VBN VBD SURROUNDTAG , $
VBD VBN PREVBIGRAM DT RB
VBN JJ PREVWD by
NNP JJ WDNEXTTAG American JJ
NN VBG PREVTAG VBP
JJ RB LBIGRAM very much
NN VBG RBIGRAM operating officer
RB IN RBIGRAM up for
NNS VBZ NEXTBIGRAM JJ NNS
NNS VBZ SURROUNDTAG , IN
VB VBP PREVTAG NNPS
IN RP WDAND2TAGAFT out IN
NNPS NNP PREVBIGRAM CC NNP
NN RB RBIGRAM close to
RBR RB PREVWD no
JJ VBD NEXTTAG DT
RB NNP PREVTAG NNP
MD NN PREVWD good
JJ NN WDPREVTAG NN giant
NN JJ WDNEXTTAG official NNS
VBN VBD SURROUNDTAG , PRP$
VBN VBD SURROUNDTAG , RB
VBN VBD SURROUNDTAG NN PRP
NNP JJ WDNEXTTAG South JJ
NN VBG PREVTAG RB
NNS VBZ SURROUNDTAG , TO
VBZ NNS SURROUNDTAG NN .
NN VB NEXTTAG PRP$
VBP VB PREV1OR2WD do
VB JJ NEXTWD countries
IN WDT NEXTBIGRAM RB VBZ
JJ VB NEXTTAG DT
WDT DT NEXTBIGRAM VBZ ,
NNP RB RBIGRAM First ,
DT NNP WDNEXTTAG A VBZ
JJ RBR RBIGRAM further ,
CD PRP WDNEXTTAG one MD
POS '' PREV1OR2OR3TAG .
PRP NN PREVTAG -LRB-
VBN VBD SURROUNDTAG , PRP
VBN VBD SURROUNDTAG NN NNS
VBN VBD SURROUNDTAG NN RP
NNP NN LBIGRAM STAART Business
VBD VBN PREVTAG VBG
IN RB RBIGRAM before ,
IN RB WDAND2AFT As as
NNP JJ LBIGRAM New York-based
NNP JJ CURWD Mexican
NNP NNPS WDNEXTTAG Motors NNP
NNP NNPS WDPREVTAG NNP Enterprises
JJ RB WDNEXTTAG long IN
VBG JJ SURROUNDTAG DT NN
NN PRP PREVWD are mine
* IN CURWD with
* VB CURWD be
* JJ RBIGRAM such as
* IN LBIGRAM such as
* IN CURWD from