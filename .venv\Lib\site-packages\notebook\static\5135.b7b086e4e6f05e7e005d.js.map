{"version": 3, "file": "5135.b7b086e4e6f05e7e005d.js?v=b7b086e4e6f05e7e005d", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACsD;AACH;AACH;AACL;AACmB;AAC9D;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,2BAAQ;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,gCAAK;AAC/B,QAAQ,wCAAW;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,sBAAsB,2BAAQ;AAC9B,QAAQ,2BAAQ;AAChB;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,mCAAM;AACtC,kCAAkC,mCAAM;AACxC;AACA;AACA;AACA;AACA,gCAAgC,uCAAY;AAC5C;AACA;AACA,QAAQ,qCAAS;AACjB;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,iCAAM,GAAG,yBAAyB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,QAAQ,2BAAQ;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,2BAAQ;AACvB;AACA;AACA;AACA;AACA;AACA,eAAe,2BAAQ;AACvB;AACA;AACA;AACA;AACA;AACA,qBAAqB,2BAAI;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,2BAAQ;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,qBAAqB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,qBAAqB;AACpD;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,0BAA0B;;;ACvU3B;AACA;AACyD;AAChB;AAC2B;AACzB;AAC6B;AACR;AACR;AACxD;AACA;AACA;AACO,2BAA2B,oBAAK;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACO,4BAA4B,iCAAM;AACzC;AACA;AACA,2BAA2B,wCAAc;AACzC,mCAAmC,mCAAM;AACzC,qCAAqC,8BAAe;AACpD;AACA;AACA,+BAA+B,YAAY;AAC3C,gCAAgC,YAAY;AAC5C,gCAAgC,gBAAgB;AAChD,iCAAiC,gBAAgB;AACjD,yBAAyB,gCAAK;AAC9B,mDAAmD,gCAAK;AACxD,qDAAqD,gCAAK;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,iCAAM;AACrC;AACA,kCAAkC,iCAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,oCAAS;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,oCAAS;AAC1C;AACA;AACA,SAAS;AACT,QAAQ,oCAAS;AACjB,QAAQ,oCAAS;AACjB,QAAQ,oCAAS;AACjB,gCAAgC,gCAAK,GAAG,sBAAsB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,qCAAU;AAC1C;AACA;AACA;AACA,QAAQ,qCAAU;AAClB,8BAA8B,uCAAW;AACzC;AACA,SAAS;AACT;AACA;AACA;AACA,gCAAgC,qCAAU;AAC1C;AACA;AACA,QAAQ,oCAAS;AACjB,QAAQ,qCAAU;AAClB,QAAQ,qCAAU;AAClB,QAAQ,qCAAU;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,aAAO;AACvB,gEAAgE,SAAS;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,wCAAc;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,sBAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,2BAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,KAAK;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE,KAAK;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAI,aAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,iCAAM;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAE,aAAO,KAAK,aAAO,KAAK;;;AC9a3B;AACA;AACuE;AACV;AACuB;AACrB;AACZ;AACP;AACJ;AACxC;AACA;AACA;AACO,0BAA0B,yBAAe;AAChD;AACA;AACA;AACA;AACA;AACA,4BAA4B,WAAW,aAAa,IAAI;AACxD;AACA,gBAAgB,6EAA6E,aAAa,IAAI;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,2BAAS;AACnC;AACA;AACA;AACA,6BAA6B,kCAAU;AACvC,qBAAqB,oBAAU;AAC/B,8BAA8B,uBAAS;AACvC,YAAY,WAAO;AACnB,SAAS;AACT;AACA,6CAA6C,gCAAkB;AAC/D;AACA,iCAAiC,iDAAuB;AACxD;AACA;AACA;AACA;AACA,iCAAiC,oBAAU;AAC3C;AACA;AACA;AACA;AACA,SAAS,IAAI;AACb;AACA,uBAAuB,GAAG,oBAAU;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,kCAAU;AAChC,0BAA0B,kCAAU;AACpC,qBAAqB,kCAAU;AAC/B,wBAAwB,kCAAU;AAClC,0BAA0B,kCAAU;AACpC,wBAAwB,kCAAU;AAClC,qBAAqB,kCAAU;AAC/B,8BAA8B,kCAAU;AACxC,yBAAyB,kCAAU;AACnC,2BAA2B,kCAAU;AACrC,yBAAyB,kCAAU;AACnC,+BAA+B,kCAAU;AACzC,aAAa;AACb;AACA,6BAA6B,kCAAU;AACvC,yBAAyB,kCAAU;AACnC,wBAAwB,kCAAU;AAClC,2BAA2B,kCAAU;AACrC,wBAAwB,kCAAU;AAClC,8BAA8B,kCAAU;AACxC,4BAA4B,kCAAU;AACtC,4BAA4B,kCAAU;AACtC,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,IAAI,WAAO;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAE,WAAO,KAAK,WAAO,KAAK;;;AChK3B;AACA;AAC+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,+CAA+C;AAC/D,4BAA4B,8BAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACO;;;ACnBmC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACO,gCAAgC,oBAAK;;;ACP5C;AACA;AACsB;AACE;AACO;AACF;AACJ", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/lib/panelhandler.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/lib/shell.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/lib/app.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/lib/pathopener.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/lib/tokens.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/lib/index.js"], "sourcesContent": ["// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { closeIcon } from '@jupyterlab/ui-components';\nimport { ArrayExt, find } from '@lumino/algorithm';\nimport { MessageLoop } from '@lumino/messaging';\nimport { Signal } from '@lumino/signaling';\nimport { Panel, StackedPanel, Widget } from '@lumino/widgets';\n/**\n * A class which manages a panel and sorts its widgets by rank.\n */\nexport class PanelHandler {\n    constructor() {\n        /**\n         * A message hook for child remove messages on the panel handler.\n         */\n        this._panelChildHook = (handler, msg) => {\n            switch (msg.type) {\n                case 'child-removed':\n                    {\n                        const widget = msg.child;\n                        ArrayExt.removeFirstWhere(this._items, (v) => v.widget === widget);\n                    }\n                    break;\n                default:\n                    break;\n            }\n            return true;\n        };\n        this._items = new Array();\n        this._panel = new Panel();\n        MessageLoop.installMessageHook(this._panel, this._panelChildHook);\n    }\n    /**\n     * Get the panel managed by the handler.\n     */\n    get panel() {\n        return this._panel;\n    }\n    /**\n     * Add a widget to the panel.\n     *\n     * If the widget is already added, it will be moved.\n     */\n    addWidget(widget, rank) {\n        widget.parent = null;\n        const item = { widget, rank };\n        const index = ArrayExt.upperBound(this._items, item, Private.itemCmp);\n        ArrayExt.insert(this._items, index, item);\n        this._panel.insertWidget(index, widget);\n    }\n}\n/**\n * A class which manages a side panel that can show at most one widget at a time.\n */\nexport class SidePanelHandler extends PanelHandler {\n    /**\n     * Construct a new side panel handler.\n     */\n    constructor(area) {\n        super();\n        this._isHiddenByUser = false;\n        this._widgetAdded = new Signal(this);\n        this._widgetRemoved = new Signal(this);\n        this._area = area;\n        this._panel.hide();\n        this._currentWidget = null;\n        this._lastCurrentWidget = null;\n        this._widgetPanel = new StackedPanel();\n        this._widgetPanel.widgetRemoved.connect(this._onWidgetRemoved, this);\n        this._closeButton = document.createElement('button');\n        closeIcon.element({\n            container: this._closeButton,\n            height: '16px',\n            width: 'auto',\n        });\n        this._closeButton.onclick = () => {\n            this.collapse();\n            this.hide();\n        };\n        this._closeButton.className = 'jp-Button jp-SidePanel-collapse';\n        this._closeButton.title = 'Collapse side panel';\n        const icon = new Widget({ node: this._closeButton });\n        this._panel.addWidget(icon);\n        this._panel.addWidget(this._widgetPanel);\n    }\n    /**\n     * Get the current widget in the sidebar panel.\n     */\n    get currentWidget() {\n        return (this._currentWidget ||\n            this._lastCurrentWidget ||\n            (this._items.length > 0 ? this._items[0].widget : null));\n    }\n    /**\n     * Get the area of the side panel\n     */\n    get area() {\n        return this._area;\n    }\n    /**\n     * Whether the panel is visible\n     */\n    get isVisible() {\n        return this._panel.isVisible;\n    }\n    /**\n     * Get the stacked panel managed by the handler\n     */\n    get panel() {\n        return this._panel;\n    }\n    /**\n     * Get the widgets list.\n     */\n    get widgets() {\n        return this._items.map((obj) => obj.widget);\n    }\n    /**\n     * Signal fired when a widget is added to the panel\n     */\n    get widgetAdded() {\n        return this._widgetAdded;\n    }\n    /**\n     * Signal fired when a widget is removed from the panel\n     */\n    get widgetRemoved() {\n        return this._widgetRemoved;\n    }\n    /**\n     * Get the close button element.\n     */\n    get closeButton() {\n        return this._closeButton;\n    }\n    /**\n     * Expand the sidebar.\n     *\n     * #### Notes\n     * This will open the most recently used widget, or the first widget\n     * if there is no most recently used.\n     */\n    expand(id) {\n        if (id) {\n            if (this._currentWidget && this._currentWidget.id === id) {\n                this.collapse();\n                this.hide();\n            }\n            else {\n                this.collapse();\n                this.hide();\n                this.activate(id);\n                this.show();\n            }\n        }\n        else if (this.currentWidget) {\n            this._currentWidget = this.currentWidget;\n            this.activate(this._currentWidget.id);\n            this.show();\n        }\n    }\n    /**\n     * Activate a widget residing in the stacked panel by ID.\n     *\n     * @param id - The widget's unique ID.\n     */\n    activate(id) {\n        const widget = this._findWidgetByID(id);\n        if (widget) {\n            this._currentWidget = widget;\n            widget.show();\n            widget.activate();\n        }\n    }\n    /**\n     * Test whether the sidebar has the given widget by id.\n     */\n    has(id) {\n        return this._findWidgetByID(id) !== null;\n    }\n    /**\n     * Collapse the sidebar so no items are expanded.\n     */\n    collapse() {\n        var _a;\n        (_a = this._currentWidget) === null || _a === void 0 ? void 0 : _a.hide();\n        this._currentWidget = null;\n    }\n    /**\n     * Add a widget and its title to the stacked panel.\n     *\n     * If the widget is already added, it will be moved.\n     */\n    addWidget(widget, rank) {\n        widget.parent = null;\n        widget.hide();\n        const item = { widget, rank };\n        const index = this._findInsertIndex(item);\n        ArrayExt.insert(this._items, index, item);\n        this._widgetPanel.insertWidget(index, widget);\n        this._refreshVisibility();\n        this._widgetAdded.emit(widget);\n    }\n    /**\n     * Hide the side panel\n     */\n    hide() {\n        this._isHiddenByUser = true;\n        this._refreshVisibility();\n    }\n    /**\n     * Show the side panel\n     */\n    show() {\n        this._isHiddenByUser = false;\n        this._refreshVisibility();\n    }\n    /**\n     * Find the insertion index for a rank item.\n     */\n    _findInsertIndex(item) {\n        return ArrayExt.upperBound(this._items, item, Private.itemCmp);\n    }\n    /**\n     * Find the index of the item with the given widget, or `-1`.\n     */\n    _findWidgetIndex(widget) {\n        return ArrayExt.findFirstIndex(this._items, (i) => i.widget === widget);\n    }\n    /**\n     * Find the widget with the given id, or `null`.\n     */\n    _findWidgetByID(id) {\n        const item = find(this._items, (value) => value.widget.id === id);\n        return item ? item.widget : null;\n    }\n    /**\n     * Refresh the visibility of the stacked panel.\n     */\n    _refreshVisibility() {\n        this._panel.setHidden(this._isHiddenByUser);\n    }\n    /*\n     * Handle the `widgetRemoved` signal from the panel.\n     */\n    _onWidgetRemoved(sender, widget) {\n        if (widget === this._lastCurrentWidget) {\n            this._lastCurrentWidget = null;\n        }\n        ArrayExt.removeAt(this._items, this._findWidgetIndex(widget));\n        this._refreshVisibility();\n        this._widgetRemoved.emit(widget);\n    }\n}\n/**\n * A class to manages the palette entries associated to the side panels.\n */\nexport class SidePanelPalette {\n    /**\n     * Construct a new side panel palette.\n     */\n    constructor(options) {\n        this._items = [];\n        this._commandPalette = options.commandPalette;\n        this._command = options.command;\n    }\n    /**\n     * Get a command palette item from the widget id and the area.\n     */\n    getItem(widget, area) {\n        const itemList = this._items;\n        for (let i = 0; i < itemList.length; i++) {\n            const item = itemList[i];\n            if (item.widgetId === widget.id && item.area === area) {\n                return item;\n            }\n        }\n        return null;\n    }\n    /**\n     * Add an item to the command palette.\n     */\n    addItem(widget, area) {\n        // Check if the item does not already exist.\n        if (this.getItem(widget, area)) {\n            return;\n        }\n        // Add a new item in command palette.\n        const disposableDelegate = this._commandPalette.addItem({\n            command: this._command,\n            category: 'View',\n            args: {\n                side: area,\n                title: `Show ${widget.title.caption}`,\n                id: widget.id,\n            },\n        });\n        // Keep the disposableDelegate object to be able to dispose of the item if the widget\n        // is remove from the side panel.\n        this._items.push({\n            widgetId: widget.id,\n            area: area,\n            disposable: disposableDelegate,\n        });\n    }\n    /**\n     * Remove an item from the command palette.\n     */\n    removeItem(widget, area) {\n        const item = this.getItem(widget, area);\n        if (item) {\n            item.disposable.dispose();\n        }\n    }\n}\n/**\n * A namespace for private module data.\n */\nvar Private;\n(function (Private) {\n    /**\n     * A less-than comparison function for side bar rank items.\n     */\n    function itemCmp(first, second) {\n        return first.rank - second.rank;\n    }\n    Private.itemCmp = itemCmp;\n})(Private || (Private = {}));\n", "// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { nullTranslator } from '@jupyterlab/translation';\nimport { find } from '@lumino/algorithm';\nimport { JSONExt, PromiseDelegate, Token } from '@lumino/coreutils';\nimport { Signal } from '@lumino/signaling';\nimport { BoxLayout, Panel, SplitPanel, Widget, } from '@lumino/widgets';\nimport { PanelHandler, SidePanelHandler } from './panelhandler';\nimport { TabPanelSvg } from '@jupyterlab/ui-components';\n/**\n * The Jupyter Notebook application shell token.\n */\nexport const INotebookShell = new Token('@jupyter-notebook/application:INotebookShell');\n/**\n * The default rank for ranked panels.\n */\nconst DEFAULT_RANK = 900;\n/**\n * The application shell.\n */\nexport class NotebookShell extends Widget {\n    constructor() {\n        super();\n        this._translator = nullTranslator;\n        this._currentChanged = new Signal(this);\n        this._mainWidgetLoaded = new PromiseDelegate();\n        this.id = 'main';\n        this._userLayout = {};\n        this._topHandler = new PanelHandler();\n        this._menuHandler = new PanelHandler();\n        this._leftHandler = new SidePanelHandler('left');\n        this._rightHandler = new SidePanelHandler('right');\n        this._main = new Panel();\n        const topWrapper = (this._topWrapper = new Panel());\n        const menuWrapper = (this._menuWrapper = new Panel());\n        this._topHandler.panel.id = 'top-panel';\n        this._topHandler.panel.node.setAttribute('role', 'banner');\n        this._menuHandler.panel.id = 'menu-panel';\n        this._menuHandler.panel.node.setAttribute('role', 'navigation');\n        this._main.id = 'main-panel';\n        this._main.node.setAttribute('role', 'main');\n        this._spacer_top = new Widget();\n        this._spacer_top.id = 'spacer-widget-top';\n        this._spacer_bottom = new Widget();\n        this._spacer_bottom.id = 'spacer-widget-bottom';\n        // create wrappers around the top and menu areas\n        topWrapper.id = 'top-panel-wrapper';\n        topWrapper.addWidget(this._topHandler.panel);\n        menuWrapper.id = 'menu-panel-wrapper';\n        menuWrapper.addWidget(this._menuHandler.panel);\n        const rootLayout = new BoxLayout();\n        const leftHandler = this._leftHandler;\n        const rightHandler = this._rightHandler;\n        leftHandler.panel.id = 'jp-left-stack';\n        leftHandler.panel.node.setAttribute('role', 'complementary');\n        rightHandler.panel.id = 'jp-right-stack';\n        rightHandler.panel.node.setAttribute('role', 'complementary');\n        // Hide the side panels by default.\n        leftHandler.hide();\n        rightHandler.hide();\n        const middleLayout = new BoxLayout({\n            spacing: 0,\n            direction: 'top-to-bottom',\n        });\n        BoxLayout.setStretch(this._topWrapper, 0);\n        BoxLayout.setStretch(this._menuWrapper, 0);\n        BoxLayout.setStretch(this._main, 1);\n        const middlePanel = new Panel({ layout: middleLayout });\n        middlePanel.addWidget(this._topWrapper);\n        middlePanel.addWidget(this._menuWrapper);\n        middlePanel.addWidget(this._spacer_top);\n        middlePanel.addWidget(this._main);\n        middlePanel.addWidget(this._spacer_bottom);\n        middlePanel.layout = middleLayout;\n        const vsplitPanel = new SplitPanel();\n        vsplitPanel.id = 'jp-main-vsplit-panel';\n        vsplitPanel.spacing = 1;\n        vsplitPanel.orientation = 'vertical';\n        SplitPanel.setStretch(vsplitPanel, 1);\n        const downPanel = new TabPanelSvg({\n            tabsMovable: true,\n        });\n        this._downPanel = downPanel;\n        this._downPanel.id = 'jp-down-stack';\n        // TODO: Consider storing this as an attribute this._hsplitPanel if saving/restoring layout needed\n        const hsplitPanel = new SplitPanel();\n        hsplitPanel.id = 'main-split-panel';\n        hsplitPanel.spacing = 1;\n        BoxLayout.setStretch(hsplitPanel, 1);\n        SplitPanel.setStretch(leftHandler.panel, 0);\n        SplitPanel.setStretch(rightHandler.panel, 0);\n        SplitPanel.setStretch(middlePanel, 1);\n        hsplitPanel.addWidget(leftHandler.panel);\n        hsplitPanel.addWidget(middlePanel);\n        hsplitPanel.addWidget(rightHandler.panel);\n        // Use relative sizing to set the width of the side panels.\n        // This will still respect the min-size of children widget in the stacked\n        // panel.\n        hsplitPanel.setRelativeSizes([1, 2.5, 1]);\n        vsplitPanel.addWidget(hsplitPanel);\n        vsplitPanel.addWidget(downPanel);\n        rootLayout.spacing = 0;\n        rootLayout.addWidget(vsplitPanel);\n        // initially hiding the down panel\n        this._downPanel.hide();\n        // Connect down panel change listeners\n        this._downPanel.tabBar.tabMoved.connect(this._onTabPanelChanged, this);\n        this._downPanel.stackedPanel.widgetRemoved.connect(this._onTabPanelChanged, this);\n        this.layout = rootLayout;\n        // Added Skip to Main Link\n        const skipLinkWidgetHandler = (this._skipLinkWidgetHandler =\n            new Private.SkipLinkWidgetHandler(this));\n        this.add(skipLinkWidgetHandler.skipLinkWidget, 'top', { rank: 0 });\n        this._skipLinkWidgetHandler.show();\n    }\n    /**\n     * A signal emitted when the current widget changes.\n     */\n    get currentChanged() {\n        return this._currentChanged;\n    }\n    /**\n     * The current widget in the shell's main area.\n     */\n    get currentWidget() {\n        var _a;\n        return (_a = this._main.widgets[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    /**\n     * Get the top area wrapper panel\n     */\n    get top() {\n        return this._topWrapper;\n    }\n    /**\n     * Get the menu area wrapper panel\n     */\n    get menu() {\n        return this._menuWrapper;\n    }\n    /**\n     * Get the left area handler\n     */\n    get leftHandler() {\n        return this._leftHandler;\n    }\n    /**\n     * Get the right area handler\n     */\n    get rightHandler() {\n        return this._rightHandler;\n    }\n    /**\n     * Is the left sidebar visible?\n     */\n    get leftCollapsed() {\n        return !(this._leftHandler.isVisible && this._leftHandler.panel.isVisible);\n    }\n    /**\n     * Is the right sidebar visible?\n     */\n    get rightCollapsed() {\n        return !(this._rightHandler.isVisible && this._rightHandler.panel.isVisible);\n    }\n    /**\n     * Promise that resolves when the main widget is loaded\n     */\n    get restored() {\n        return this._mainWidgetLoaded.promise;\n    }\n    /**\n     * Getter and setter for the translator.\n     */\n    get translator() {\n        var _a;\n        return (_a = this._translator) !== null && _a !== void 0 ? _a : nullTranslator;\n    }\n    set translator(value) {\n        if (value !== this._translator) {\n            this._translator = value;\n            const trans = value.load('notebook');\n            this._leftHandler.closeButton.title = trans.__('Collapse %1 side panel', this._leftHandler.area);\n            this._rightHandler.closeButton.title = trans.__('Collapse %1 side panel', this._rightHandler.area);\n        }\n    }\n    /**\n     * User custom shell layout.\n     */\n    get userLayout() {\n        return JSONExt.deepCopy(this._userLayout);\n    }\n    /**\n     * Activate a widget in its area.\n     */\n    activateById(id) {\n        // Search all areas that can have widgets for this widget, starting with main.\n        for (const area of ['main', 'top', 'left', 'right', 'menu', 'down']) {\n            const widget = find(this.widgets(area), (w) => w.id === id);\n            if (widget) {\n                if (area === 'left') {\n                    this.expandLeft(id);\n                }\n                else if (area === 'right') {\n                    this.expandRight(id);\n                }\n                else if (area === 'down') {\n                    this._downPanel.show();\n                    widget.activate();\n                }\n                else {\n                    widget.activate();\n                }\n            }\n        }\n    }\n    /**\n     * Add a widget to the application shell.\n     *\n     * @param widget - The widget being added.\n     *\n     * @param area - Optional region in the shell into which the widget should\n     * be added.\n     *\n     * @param options - Optional open options.\n     *\n     */\n    add(widget, area, options) {\n        var _a, _b;\n        let userPosition;\n        if ((options === null || options === void 0 ? void 0 : options.type) && this._userLayout[options.type]) {\n            userPosition = this._userLayout[options.type];\n        }\n        else {\n            userPosition = this._userLayout[widget.id];\n        }\n        area = (_a = userPosition === null || userPosition === void 0 ? void 0 : userPosition.area) !== null && _a !== void 0 ? _a : area;\n        options =\n            options || (userPosition === null || userPosition === void 0 ? void 0 : userPosition.options)\n                ? {\n                    ...options,\n                    ...userPosition === null || userPosition === void 0 ? void 0 : userPosition.options,\n                }\n                : undefined;\n        const rank = (_b = options === null || options === void 0 ? void 0 : options.rank) !== null && _b !== void 0 ? _b : DEFAULT_RANK;\n        switch (area) {\n            case 'top':\n                return this._topHandler.addWidget(widget, rank);\n            case 'menu':\n                return this._menuHandler.addWidget(widget, rank);\n            case 'main':\n            case undefined: {\n                if (this._main.widgets.length > 0) {\n                    // do not add the widget if there is already one\n                    return;\n                }\n                const previousWidget = this.currentWidget;\n                this._main.addWidget(widget);\n                this._main.update();\n                this._currentChanged.emit({\n                    newValue: widget,\n                    oldValue: previousWidget,\n                });\n                this._mainWidgetLoaded.resolve();\n                break;\n            }\n            case 'left':\n                return this._leftHandler.addWidget(widget, rank);\n            case 'right':\n                return this._rightHandler.addWidget(widget, rank);\n            case 'down':\n                return this._downPanel.addWidget(widget);\n            default:\n                console.warn(`Cannot add widget to area: ${area}`);\n        }\n    }\n    /**\n     * Collapse the top area and the spacer to make the view more compact.\n     */\n    collapseTop() {\n        this._topWrapper.setHidden(true);\n        this._spacer_top.setHidden(true);\n    }\n    /**\n     * Expand the top area to show the header and the spacer.\n     */\n    expandTop() {\n        this._topWrapper.setHidden(false);\n        this._spacer_top.setHidden(false);\n    }\n    /**\n     * Return the list of widgets for the given area.\n     *\n     * @param area The area\n     */\n    *widgets(area) {\n        switch (area !== null && area !== void 0 ? area : 'main') {\n            case 'top':\n                yield* this._topHandler.panel.widgets;\n                return;\n            case 'menu':\n                yield* this._menuHandler.panel.widgets;\n                return;\n            case 'main':\n                yield* this._main.widgets;\n                return;\n            case 'left':\n                yield* this._leftHandler.widgets;\n                return;\n            case 'right':\n                yield* this._rightHandler.widgets;\n                return;\n            case 'down':\n                yield* this._downPanel.widgets;\n                return;\n            default:\n                console.error(`This shell has no area called \"${area}\"`);\n                return;\n        }\n    }\n    /**\n     * Expand the left panel to show the sidebar with its widget.\n     */\n    expandLeft(id) {\n        this._leftHandler.panel.show();\n        this._leftHandler.expand(id); // Show the current widget, if any\n    }\n    /**\n     * Collapse the left panel\n     */\n    collapseLeft() {\n        this._leftHandler.collapse();\n        this._leftHandler.panel.hide();\n    }\n    /**\n     * Expand the right panel to show the sidebar with its widget.\n     */\n    expandRight(id) {\n        this._rightHandler.panel.show();\n        this._rightHandler.expand(id); // Show the current widget, if any\n    }\n    /**\n     * Collapse the right panel\n     */\n    collapseRight() {\n        this._rightHandler.collapse();\n        this._rightHandler.panel.hide();\n    }\n    /**\n     * Restore the layout state and configuration for the application shell.\n     */\n    async restoreLayout(configuration) {\n        this._userLayout = configuration;\n    }\n    /**\n     * Handle a change on the down panel widgets\n     */\n    _onTabPanelChanged() {\n        if (this._downPanel.stackedPanel.widgets.length === 0) {\n            this._downPanel.hide();\n        }\n    }\n}\nexport var Private;\n(function (Private) {\n    class SkipLinkWidgetHandler {\n        /**\n         * Construct a new skipLink widget handler.\n         */\n        constructor(shell) {\n            this._isDisposed = false;\n            const skipLinkWidget = (this._skipLinkWidget = new Widget());\n            const skipToMain = document.createElement('a');\n            skipToMain.href = '#first-cell';\n            skipToMain.tabIndex = 1;\n            skipToMain.text = 'Skip to Main';\n            skipToMain.className = 'skip-link';\n            skipToMain.addEventListener('click', this);\n            skipLinkWidget.addClass('jp-skiplink');\n            skipLinkWidget.id = 'jp-skiplink';\n            skipLinkWidget.node.appendChild(skipToMain);\n        }\n        handleEvent(event) {\n            switch (event.type) {\n                case 'click':\n                    this._focusMain();\n                    break;\n            }\n        }\n        _focusMain() {\n            const input = document.querySelector('#main-panel .jp-InputArea-editor');\n            input.tabIndex = 1;\n            input.focus();\n        }\n        /**\n         * Get the input element managed by the handler.\n         */\n        get skipLinkWidget() {\n            return this._skipLinkWidget;\n        }\n        /**\n         * Dispose of the handler and the resources it holds.\n         */\n        dispose() {\n            if (this.isDisposed) {\n                return;\n            }\n            this._isDisposed = true;\n            this._skipLinkWidget.node.removeEventListener('click', this);\n            this._skipLinkWidget.dispose();\n        }\n        /**\n         * Hide the skipLink widget.\n         */\n        hide() {\n            this._skipLinkWidget.hide();\n        }\n        /**\n         * Show the skipLink widget.\n         */\n        show() {\n            this._skipLinkWidget.show();\n        }\n        /**\n         * Test whether the handler has been disposed.\n         */\n        get isDisposed() {\n            return this._isDisposed;\n        }\n    }\n    Private.SkipLinkWidgetHandler = SkipLinkWidgetHandler;\n})(Private || (Private = {}));\n", "// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { JupyterLab, JupyterFrontEnd, } from '@jupyterlab/application';\nimport { Base64ModelFactory } from '@jupyterlab/docregistry';\nimport { createRendermimePlugins } from '@jupyterlab/application/lib/mimerenderers';\nimport { LabStatus } from '@jupyterlab/application/lib/status';\nimport { PageConfig } from '@jupyterlab/coreutils';\nimport { Throttler } from '@lumino/polling';\nimport { NotebookShell } from './shell';\n/**\n * App is the main application class. It is instantiated once and shared.\n */\nexport class NotebookApp extends JupyterFrontEnd {\n    /**\n     * Construct a new NotebookApp object.\n     *\n     * @param options The instantiation options for an application.\n     */\n    constructor(options = { shell: new NotebookShell() }) {\n        var _a, _b;\n        super({ ...options, shell: (_a = options.shell) !== null && _a !== void 0 ? _a : new NotebookShell() });\n        /**\n         * The name of the application.\n         */\n        this.name = 'Jupyter Notebook';\n        /**\n         * A namespace/prefix plugins may use to denote their provenance.\n         */\n        this.namespace = this.name;\n        /**\n         * The application busy and dirty status signals and flags.\n         */\n        this.status = new LabStatus(this);\n        /**\n         * The version of the application.\n         */\n        this.version = (_b = PageConfig.getOption('appVersion')) !== null && _b !== void 0 ? _b : 'unknown';\n        this._info = JupyterLab.defaultInfo;\n        this._formatter = new Throttler(() => {\n            Private.setFormat(this);\n        }, 250);\n        // Add initial model factory.\n        this.docRegistry.addModelFactory(new Base64ModelFactory());\n        if (options.mimeExtensions) {\n            for (const plugin of createRendermimePlugins(options.mimeExtensions)) {\n                this.registerPlugin(plugin);\n            }\n        }\n        // Create an IInfo dictionary from the options to override the defaults.\n        const info = Object.keys(JupyterLab.defaultInfo).reduce((acc, val) => {\n            if (val in options) {\n                acc[val] = JSON.parse(JSON.stringify(options[val]));\n            }\n            return acc;\n        }, {});\n        // Populate application info.\n        this._info = { ...JupyterLab.defaultInfo, ...info };\n        this.restored = this.shell.restored;\n        this.restored.then(() => this._formatter.invoke());\n    }\n    /**\n     * The NotebookApp application information dictionary.\n     */\n    get info() {\n        return this._info;\n    }\n    /**\n     * The JupyterLab application paths dictionary.\n     */\n    get paths() {\n        return {\n            urls: {\n                base: PageConfig.getOption('baseUrl'),\n                notFound: PageConfig.getOption('notFoundUrl'),\n                app: PageConfig.getOption('appUrl'),\n                static: PageConfig.getOption('staticUrl'),\n                settings: PageConfig.getOption('settingsUrl'),\n                themes: PageConfig.getOption('themesUrl'),\n                doc: PageConfig.getOption('docUrl'),\n                translations: PageConfig.getOption('translationsApiUrl'),\n                hubHost: PageConfig.getOption('hubHost') || undefined,\n                hubPrefix: PageConfig.getOption('hubPrefix') || undefined,\n                hubUser: PageConfig.getOption('hubUser') || undefined,\n                hubServerName: PageConfig.getOption('hubServerName') || undefined,\n            },\n            directories: {\n                appSettings: PageConfig.getOption('appSettingsDir'),\n                schemas: PageConfig.getOption('schemasDir'),\n                static: PageConfig.getOption('staticDir'),\n                templates: PageConfig.getOption('templatesDir'),\n                themes: PageConfig.getOption('themesDir'),\n                userSettings: PageConfig.getOption('userSettingsDir'),\n                serverRoot: PageConfig.getOption('serverRoot'),\n                workspaces: PageConfig.getOption('workspacesDir'),\n            },\n        };\n    }\n    /**\n     * Handle the DOM events for the application.\n     *\n     * @param event - The DOM event sent to the application.\n     */\n    handleEvent(event) {\n        super.handleEvent(event);\n        if (event.type === 'resize') {\n            void this._formatter.invoke();\n        }\n    }\n    /**\n     * Register plugins from a plugin module.\n     *\n     * @param mod - The plugin module to register.\n     */\n    registerPluginModule(mod) {\n        let data = mod.default;\n        // Handle commonjs exports.\n        if (!Object.prototype.hasOwnProperty.call(mod, '__esModule')) {\n            data = mod;\n        }\n        if (!Array.isArray(data)) {\n            data = [data];\n        }\n        data.forEach((item) => {\n            try {\n                this.registerPlugin(item);\n            }\n            catch (error) {\n                console.error(error);\n            }\n        });\n    }\n    /**\n     * Register the plugins from multiple plugin modules.\n     *\n     * @param mods - The plugin modules to register.\n     */\n    registerPluginModules(mods) {\n        mods.forEach((mod) => {\n            this.registerPluginModule(mod);\n        });\n    }\n}\n/**\n * A namespace for module-private functionality.\n */\nvar Private;\n(function (Private) {\n    /**\n     * Media query for mobile devices.\n     */\n    const MOBILE_QUERY = 'only screen and (max-width: 760px)';\n    /**\n     * Sets the `format` of a Jupyter front-end application.\n     *\n     * @param app The front-end application whose format is set.\n     */\n    function setFormat(app) {\n        app.format = window.matchMedia(MOBILE_QUERY).matches ? 'mobile' : 'desktop';\n    }\n    Private.setFormat = setFormat;\n})(Private || (Private = {}));\n", "// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { URLExt } from '@jupyterlab/coreutils';\n/**\n * A class to open paths in new browser tabs in the Notebook application.\n */\nclass DefaultNotebookPathOpener {\n    /**\n     * Open a path in a new browser tab.\n     */\n    open(options) {\n        const { prefix, path, searchParams, target, features } = options;\n        const url = new URL(URLExt.join(prefix, path !== null && path !== void 0 ? path : ''), window.location.origin);\n        if (searchParams) {\n            url.search = searchParams.toString();\n        }\n        return window.open(url, target, features);\n    }\n}\nexport const defaultNotebookPathOpener = new DefaultNotebookPathOpener();\n", "import { Token } from '@lumino/coreutils';\n/**\n * The INotebookPathOpener token.\n * The main purpose of this token is to allow other extensions or downstream applications\n * to override the default behavior of opening a notebook in a new tab.\n * It also allows passing the path as a URL search parameter, or other options to the window.open call.\n */\nexport const INotebookPathOpener = new Token('@jupyter-notebook/application:INotebookPathOpener');\n", "// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nexport * from './app';\nexport * from './shell';\nexport * from './panelhandler';\nexport * from './pathopener';\nexport * from './tokens';\n"], "names": [], "sourceRoot": ""}