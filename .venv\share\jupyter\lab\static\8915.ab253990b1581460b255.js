(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[8915],{87799:function(t,e,r){(function e(i,n){if(true)t.exports=n(r(23143));else{}})(this,(function(t){return function(t){var e={};function r(i){if(e[i]){return e[i].exports}var n=e[i]={i,l:false,exports:{}};t[i].call(n.exports,n,n.exports,r);n.l=true;return n.exports}r.m=t;r.c=e;r.i=function(t){return t};r.d=function(t,e,i){if(!r.o(t,e)){Object.defineProperty(t,e,{configurable:false,enumerable:true,get:i})}};r.n=function(t){var e=t&&t.__esModule?function e(){return t["default"]}:function e(){return t};r.d(e,"a",e);return e};r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};r.p="";return r(r.s=7)}([function(e,r){e.exports=t},function(t,e,r){"use strict";var i=r(0).FDLayoutConstants;function n(){}for(var o in i){n[o]=i[o]}n.DEFAULT_USE_MULTI_LEVEL_SCALING=false;n.DEFAULT_RADIAL_SEPARATION=i.DEFAULT_EDGE_LENGTH;n.DEFAULT_COMPONENT_SEPERATION=60;n.TILE=true;n.TILING_PADDING_VERTICAL=10;n.TILING_PADDING_HORIZONTAL=10;n.TREE_REDUCTION_ON_INCREMENTAL=false;t.exports=n},function(t,e,r){"use strict";var i=r(0).FDLayoutEdge;function n(t,e,r){i.call(this,t,e,r)}n.prototype=Object.create(i.prototype);for(var o in i){n[o]=i[o]}t.exports=n},function(t,e,r){"use strict";var i=r(0).LGraph;function n(t,e,r){i.call(this,t,e,r)}n.prototype=Object.create(i.prototype);for(var o in i){n[o]=i[o]}t.exports=n},function(t,e,r){"use strict";var i=r(0).LGraphManager;function n(t){i.call(this,t)}n.prototype=Object.create(i.prototype);for(var o in i){n[o]=i[o]}t.exports=n},function(t,e,r){"use strict";var i=r(0).FDLayoutNode;var n=r(0).IMath;function o(t,e,r,n){i.call(this,t,e,r,n)}o.prototype=Object.create(i.prototype);for(var a in i){o[a]=i[a]}o.prototype.move=function(){var t=this.graphManager.getLayout();this.displacementX=t.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren;this.displacementY=t.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren;if(Math.abs(this.displacementX)>t.coolingFactor*t.maxNodeDisplacement){this.displacementX=t.coolingFactor*t.maxNodeDisplacement*n.sign(this.displacementX)}if(Math.abs(this.displacementY)>t.coolingFactor*t.maxNodeDisplacement){this.displacementY=t.coolingFactor*t.maxNodeDisplacement*n.sign(this.displacementY)}if(this.child==null){this.moveBy(this.displacementX,this.displacementY)}else if(this.child.getNodes().length==0){this.moveBy(this.displacementX,this.displacementY)}else{this.propogateDisplacementToChildren(this.displacementX,this.displacementY)}t.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY);this.springForceX=0;this.springForceY=0;this.repulsionForceX=0;this.repulsionForceY=0;this.gravitationForceX=0;this.gravitationForceY=0;this.displacementX=0;this.displacementY=0};o.prototype.propogateDisplacementToChildren=function(t,e){var r=this.getChild().getNodes();var i;for(var n=0;n<r.length;n++){i=r[n];if(i.getChild()==null){i.moveBy(t,e);i.displacementX+=t;i.displacementY+=e}else{i.propogateDisplacementToChildren(t,e)}}};o.prototype.setPred1=function(t){this.pred1=t};o.prototype.getPred1=function(){return pred1};o.prototype.getPred2=function(){return pred2};o.prototype.setNext=function(t){this.next=t};o.prototype.getNext=function(){return next};o.prototype.setProcessed=function(t){this.processed=t};o.prototype.isProcessed=function(){return processed};t.exports=o},function(t,e,r){"use strict";var i=r(0).FDLayout;var n=r(4);var o=r(3);var a=r(5);var s=r(2);var h=r(1);var l=r(0).FDLayoutConstants;var c=r(0).LayoutConstants;var u=r(0).Point;var g=r(0).PointD;var d=r(0).Layout;var p=r(0).Integer;var f=r(0).IGeometry;var v=r(0).LGraph;var y=r(0).Transform;function E(){i.call(this);this.toBeTiled={}}E.prototype=Object.create(i.prototype);for(var _ in i){E[_]=i[_]}E.prototype.newGraphManager=function(){var t=new n(this);this.graphManager=t;return t};E.prototype.newGraph=function(t){return new o(null,this.graphManager,t)};E.prototype.newNode=function(t){return new a(this.graphManager,t)};E.prototype.newEdge=function(t){return new s(null,null,t)};E.prototype.initParameters=function(){i.prototype.initParameters.call(this,arguments);if(!this.isSubLayout){if(h.DEFAULT_EDGE_LENGTH<10){this.idealEdgeLength=10}else{this.idealEdgeLength=h.DEFAULT_EDGE_LENGTH}this.useSmartIdealEdgeLengthCalculation=h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;this.springConstant=l.DEFAULT_SPRING_STRENGTH;this.repulsionConstant=l.DEFAULT_REPULSION_STRENGTH;this.gravityConstant=l.DEFAULT_GRAVITY_STRENGTH;this.compoundGravityConstant=l.DEFAULT_COMPOUND_GRAVITY_STRENGTH;this.gravityRangeFactor=l.DEFAULT_GRAVITY_RANGE_FACTOR;this.compoundGravityRangeFactor=l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;this.prunedNodesAll=[];this.growTreeIterations=0;this.afterGrowthIterations=0;this.isTreeGrowing=false;this.isGrowthFinished=false;this.coolingCycle=0;this.maxCoolingCycle=this.maxIterations/l.CONVERGENCE_CHECK_PERIOD;this.finalTemperature=l.CONVERGENCE_CHECK_PERIOD/this.maxIterations;this.coolingAdjuster=1}};E.prototype.layout=function(){var t=c.DEFAULT_CREATE_BENDS_AS_NEEDED;if(t){this.createBendpoints();this.graphManager.resetAllEdges()}this.level=0;return this.classicLayout()};E.prototype.classicLayout=function(){this.nodesWithGravity=this.calculateNodesToApplyGravitationTo();this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity);this.calcNoOfChildrenForAllNodes();this.graphManager.calcLowestCommonAncestors();this.graphManager.calcInclusionTreeDepths();this.graphManager.getRoot().calcEstimatedSize();this.calcIdealEdgeLengths();if(!this.incremental){var t=this.getFlatForest();if(t.length>0){this.positionNodesRadially(t)}else{this.reduceTrees();this.graphManager.resetAllNodesToApplyGravitation();var e=new Set(this.getAllNodes());var r=this.nodesWithGravity.filter((function(t){return e.has(t)}));this.graphManager.setAllNodesToApplyGravitation(r);this.positionNodesRandomly()}}else{if(h.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees();this.graphManager.resetAllNodesToApplyGravitation();var e=new Set(this.getAllNodes());var r=this.nodesWithGravity.filter((function(t){return e.has(t)}));this.graphManager.setAllNodesToApplyGravitation(r)}}this.initSpringEmbedder();this.runSpringEmbedder();return true};E.prototype.tick=function(){this.totalIterations++;if(this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.prunedNodesAll.length>0){this.isTreeGrowing=true}else{return true}}if(this.totalIterations%l.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged()){if(this.prunedNodesAll.length>0){this.isTreeGrowing=true}else{return true}}this.coolingCycle++;if(this.layoutQuality==0){this.coolingAdjuster=this.coolingCycle}else if(this.layoutQuality==1){this.coolingAdjuster=this.coolingCycle/3}this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature);this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0){if(this.prunedNodesAll.length>0){this.graphManager.updateBounds();this.updateGrid();this.growTree(this.prunedNodesAll);this.graphManager.resetAllNodesToApplyGravitation();var t=new Set(this.getAllNodes());var e=this.nodesWithGravity.filter((function(e){return t.has(e)}));this.graphManager.setAllNodesToApplyGravitation(e);this.graphManager.updateBounds();this.updateGrid();this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL}else{this.isTreeGrowing=false;this.isGrowthFinished=true}}this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged()){return true}if(this.afterGrowthIterations%10==0){this.graphManager.updateBounds();this.updateGrid()}this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100);this.afterGrowthIterations++}var r=!this.isTreeGrowing&&!this.isGrowthFinished;var i=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;this.totalDisplacement=0;this.graphManager.updateBounds();this.calcSpringForces();this.calcRepulsionForces(r,i);this.calcGravitationalForces();this.moveNodes();this.animate();return false};E.prototype.getPositionsData=function(){var t=this.graphManager.getAllNodes();var e={};for(var r=0;r<t.length;r++){var i=t[r].rect;var n=t[r].id;e[n]={id:n,x:i.getCenterX(),y:i.getCenterY(),w:i.width,h:i.height}}return e};E.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25;this.animationPeriod=this.initialAnimationPeriod;var t=false;if(l.ANIMATE==="during"){this.emit("layoutstarted")}else{while(!t){t=this.tick()}this.graphManager.updateBounds()}};E.prototype.calculateNodesToApplyGravitationTo=function(){var t=[];var e;var r=this.graphManager.getGraphs();var i=r.length;var n;for(n=0;n<i;n++){e=r[n];e.updateConnected();if(!e.isConnected){t=t.concat(e.getNodes())}}return t};E.prototype.createBendpoints=function(){var t=[];t=t.concat(this.graphManager.getAllEdges());var e=new Set;var r;for(r=0;r<t.length;r++){var i=t[r];if(!e.has(i)){var n=i.getSource();var o=i.getTarget();if(n==o){i.getBendpoints().push(new g);i.getBendpoints().push(new g);this.createDummyNodesForBendpoints(i);e.add(i)}else{var a=[];a=a.concat(n.getEdgeListToNode(o));a=a.concat(o.getEdgeListToNode(n));if(!e.has(a[0])){if(a.length>1){var s;for(s=0;s<a.length;s++){var h=a[s];h.getBendpoints().push(new g);this.createDummyNodesForBendpoints(h)}}a.forEach((function(t){e.add(t)}))}}}if(e.size==t.length){break}}};E.prototype.positionNodesRadially=function(t){var e=new u(0,0);var r=Math.ceil(Math.sqrt(t.length));var i=0;var n=0;var o=0;var a=new g(0,0);for(var s=0;s<t.length;s++){if(s%r==0){o=0;n=i;if(s!=0){n+=h.DEFAULT_COMPONENT_SEPERATION}i=0}var l=t[s];var p=d.findCenterOfTree(l);e.x=o;e.y=n;a=E.radialLayout(l,p,e);if(a.y>i){i=Math.floor(a.y)}o=Math.floor(a.x+h.DEFAULT_COMPONENT_SEPERATION)}this.transform(new g(c.WORLD_CENTER_X-a.x/2,c.WORLD_CENTER_Y-a.y/2))};E.radialLayout=function(t,e,r){var i=Math.max(this.maxDiagonalInTree(t),h.DEFAULT_RADIAL_SEPARATION);E.branchRadialLayout(e,null,0,359,0,i);var n=v.calculateBounds(t);var o=new y;o.setDeviceOrgX(n.getMinX());o.setDeviceOrgY(n.getMinY());o.setWorldOrgX(r.x);o.setWorldOrgY(r.y);for(var a=0;a<t.length;a++){var s=t[a];s.transform(o)}var l=new g(n.getMaxX(),n.getMaxY());return o.inverseTransformPoint(l)};E.branchRadialLayout=function(t,e,r,i,n,o){var a=(i-r+1)/2;if(a<0){a+=180}var s=(a+r)%360;var h=s*f.TWO_PI/360;var l=Math.cos(h);var c=n*Math.cos(h);var u=n*Math.sin(h);t.setCenter(c,u);var g=[];g=g.concat(t.getEdges());var d=g.length;if(e!=null){d--}var p=0;var v=g.length;var y;var _=t.getEdgesBetween(e);while(_.length>1){var m=_[0];_.splice(0,1);var N=g.indexOf(m);if(N>=0){g.splice(N,1)}v--;d--}if(e!=null){y=(g.indexOf(_[0])+1)%v}else{y=0}var A=Math.abs(i-r)/d;for(var L=y;p!=d;L=++L%v){var T=g[L].getOtherEnd(t);if(T==e){continue}var O=(r+p*A)%360;var D=(O+A)%360;E.branchRadialLayout(T,t,O,D,n+o,o);p++}};E.maxDiagonalInTree=function(t){var e=p.MIN_VALUE;for(var r=0;r<t.length;r++){var i=t[r];var n=i.getDiagonal();if(n>e){e=n}}return e};E.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength};E.prototype.groupZeroDegreeMembers=function(){var t=this;var e={};this.memberGroups={};this.idToDummyNode={};var r=[];var i=this.graphManager.getAllNodes();for(var n=0;n<i.length;n++){var o=i[n];var s=o.getParent();if(this.getNodeDegreeWithChildren(o)===0&&(s.id==undefined||!this.getToBeTiled(s))){r.push(o)}}for(var n=0;n<r.length;n++){var o=r[n];var h=o.getParent().id;if(typeof e[h]==="undefined")e[h]=[];e[h]=e[h].concat(o)}Object.keys(e).forEach((function(r){if(e[r].length>1){var i="DummyCompound_"+r;t.memberGroups[i]=e[r];var n=e[r][0].getParent();var o=new a(t.graphManager);o.id=i;o.paddingLeft=n.paddingLeft||0;o.paddingRight=n.paddingRight||0;o.paddingBottom=n.paddingBottom||0;o.paddingTop=n.paddingTop||0;t.idToDummyNode[i]=o;var s=t.getGraphManager().add(t.newGraph(),o);var h=n.getChild();h.add(o);for(var l=0;l<e[r].length;l++){var c=e[r][l];h.remove(c);s.add(c)}}}))};E.prototype.clearCompounds=function(){var t={};var e={};this.performDFSOnCompounds();for(var r=0;r<this.compoundOrder.length;r++){e[this.compoundOrder[r].id]=this.compoundOrder[r];t[this.compoundOrder[r].id]=[].concat(this.compoundOrder[r].getChild().getNodes());this.graphManager.remove(this.compoundOrder[r].getChild());this.compoundOrder[r].child=null}this.graphManager.resetAllNodes();this.tileCompoundMembers(t,e)};E.prototype.clearZeroDegreeMembers=function(){var t=this;var e=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach((function(r){var i=t.idToDummyNode[r];e[r]=t.tileNodes(t.memberGroups[r],i.paddingLeft+i.paddingRight);i.rect.width=e[r].width;i.rect.height=e[r].height}))};E.prototype.repopulateCompounds=function(){for(var t=this.compoundOrder.length-1;t>=0;t--){var e=this.compoundOrder[t];var r=e.id;var i=e.paddingLeft;var n=e.paddingTop;this.adjustLocations(this.tiledMemberPack[r],e.rect.x,e.rect.y,i,n)}};E.prototype.repopulateZeroDegreeMembers=function(){var t=this;var e=this.tiledZeroDegreePack;Object.keys(e).forEach((function(r){var i=t.idToDummyNode[r];var n=i.paddingLeft;var o=i.paddingTop;t.adjustLocations(e[r],i.rect.x,i.rect.y,n,o)}))};E.prototype.getToBeTiled=function(t){var e=t.id;if(this.toBeTiled[e]!=null){return this.toBeTiled[e]}var r=t.getChild();if(r==null){this.toBeTiled[e]=false;return false}var i=r.getNodes();for(var n=0;n<i.length;n++){var o=i[n];if(this.getNodeDegree(o)>0){this.toBeTiled[e]=false;return false}if(o.getChild()==null){this.toBeTiled[o.id]=false;continue}if(!this.getToBeTiled(o)){this.toBeTiled[e]=false;return false}}this.toBeTiled[e]=true;return true};E.prototype.getNodeDegree=function(t){var e=t.id;var r=t.getEdges();var i=0;for(var n=0;n<r.length;n++){var o=r[n];if(o.getSource().id!==o.getTarget().id){i=i+1}}return i};E.prototype.getNodeDegreeWithChildren=function(t){var e=this.getNodeDegree(t);if(t.getChild()==null){return e}var r=t.getChild().getNodes();for(var i=0;i<r.length;i++){var n=r[i];e+=this.getNodeDegreeWithChildren(n)}return e};E.prototype.performDFSOnCompounds=function(){this.compoundOrder=[];this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())};E.prototype.fillCompexOrderByDFS=function(t){for(var e=0;e<t.length;e++){var r=t[e];if(r.getChild()!=null){this.fillCompexOrderByDFS(r.getChild().getNodes())}if(this.getToBeTiled(r)){this.compoundOrder.push(r)}}};E.prototype.adjustLocations=function(t,e,r,i,n){e+=i;r+=n;var o=e;for(var a=0;a<t.rows.length;a++){var s=t.rows[a];e=o;var h=0;for(var l=0;l<s.length;l++){var c=s[l];c.rect.x=e;c.rect.y=r;e+=c.rect.width+t.horizontalPadding;if(c.rect.height>h)h=c.rect.height}r+=h+t.verticalPadding}};E.prototype.tileCompoundMembers=function(t,e){var r=this;this.tiledMemberPack=[];Object.keys(t).forEach((function(i){var n=e[i];r.tiledMemberPack[i]=r.tileNodes(t[i],n.paddingLeft+n.paddingRight);n.rect.width=r.tiledMemberPack[i].width;n.rect.height=r.tiledMemberPack[i].height}))};E.prototype.tileNodes=function(t,e){var r=h.TILING_PADDING_VERTICAL;var i=h.TILING_PADDING_HORIZONTAL;var n={rows:[],rowWidth:[],rowHeight:[],width:0,height:e,verticalPadding:r,horizontalPadding:i};t.sort((function(t,e){if(t.rect.width*t.rect.height>e.rect.width*e.rect.height)return-1;if(t.rect.width*t.rect.height<e.rect.width*e.rect.height)return 1;return 0}));for(var o=0;o<t.length;o++){var a=t[o];if(n.rows.length==0){this.insertNodeToRow(n,a,0,e)}else if(this.canAddHorizontal(n,a.rect.width,a.rect.height)){this.insertNodeToRow(n,a,this.getShortestRowIndex(n),e)}else{this.insertNodeToRow(n,a,n.rows.length,e)}this.shiftToLastRow(n)}return n};E.prototype.insertNodeToRow=function(t,e,r,i){var n=i;if(r==t.rows.length){var o=[];t.rows.push(o);t.rowWidth.push(n);t.rowHeight.push(0)}var a=t.rowWidth[r]+e.rect.width;if(t.rows[r].length>0){a+=t.horizontalPadding}t.rowWidth[r]=a;if(t.width<a){t.width=a}var s=e.rect.height;if(r>0)s+=t.verticalPadding;var h=0;if(s>t.rowHeight[r]){h=t.rowHeight[r];t.rowHeight[r]=s;h=t.rowHeight[r]-h}t.height+=h;t.rows[r].push(e)};E.prototype.getShortestRowIndex=function(t){var e=-1;var r=Number.MAX_VALUE;for(var i=0;i<t.rows.length;i++){if(t.rowWidth[i]<r){e=i;r=t.rowWidth[i]}}return e};E.prototype.getLongestRowIndex=function(t){var e=-1;var r=Number.MIN_VALUE;for(var i=0;i<t.rows.length;i++){if(t.rowWidth[i]>r){e=i;r=t.rowWidth[i]}}return e};E.prototype.canAddHorizontal=function(t,e,r){var i=this.getShortestRowIndex(t);if(i<0){return true}var n=t.rowWidth[i];if(n+t.horizontalPadding+e<=t.width)return true;var o=0;if(t.rowHeight[i]<r){if(i>0)o=r+t.verticalPadding-t.rowHeight[i]}var a;if(t.width-n>=e+t.horizontalPadding){a=(t.height+o)/(n+e+t.horizontalPadding)}else{a=(t.height+o)/t.width}o=r+t.verticalPadding;var s;if(t.width<e){s=(t.height+o)/e}else{s=(t.height+o)/t.width}if(s<1)s=1/s;if(a<1)a=1/a;return a<s};E.prototype.shiftToLastRow=function(t){var e=this.getLongestRowIndex(t);var r=t.rowWidth.length-1;var i=t.rows[e];var n=i[i.length-1];var o=n.width+t.horizontalPadding;if(t.width-t.rowWidth[r]>o&&e!=r){i.splice(-1,1);t.rows[r].push(n);t.rowWidth[e]=t.rowWidth[e]-o;t.rowWidth[r]=t.rowWidth[r]+o;t.width=t.rowWidth[instance.getLongestRowIndex(t)];var a=Number.MIN_VALUE;for(var s=0;s<i.length;s++){if(i[s].height>a)a=i[s].height}if(e>0)a+=t.verticalPadding;var h=t.rowHeight[e]+t.rowHeight[r];t.rowHeight[e]=a;if(t.rowHeight[r]<n.height+t.verticalPadding)t.rowHeight[r]=n.height+t.verticalPadding;var l=t.rowHeight[e]+t.rowHeight[r];t.height+=l-h;this.shiftToLastRow(t)}};E.prototype.tilingPreLayout=function(){if(h.TILE){this.groupZeroDegreeMembers();this.clearCompounds();this.clearZeroDegreeMembers()}};E.prototype.tilingPostLayout=function(){if(h.TILE){this.repopulateZeroDegreeMembers();this.repopulateCompounds()}};E.prototype.reduceTrees=function(){var t=[];var e=true;var r;while(e){var i=this.graphManager.getAllNodes();var n=[];e=false;for(var o=0;o<i.length;o++){r=i[o];if(r.getEdges().length==1&&!r.getEdges()[0].isInterGraph&&r.getChild()==null){n.push([r,r.getEdges()[0],r.getOwner()]);e=true}}if(e==true){var a=[];for(var s=0;s<n.length;s++){if(n[s][0].getEdges().length==1){a.push(n[s]);n[s][0].getOwner().remove(n[s][0])}}t.push(a);this.graphManager.resetAllNodes();this.graphManager.resetAllEdges()}}this.prunedNodesAll=t};E.prototype.growTree=function(t){var e=t.length;var r=t[e-1];var i;for(var n=0;n<r.length;n++){i=r[n];this.findPlaceforPrunedNode(i);i[2].add(i[0]);i[2].add(i[1],i[1].source,i[1].target)}t.splice(t.length-1,1);this.graphManager.resetAllNodes();this.graphManager.resetAllEdges()};E.prototype.findPlaceforPrunedNode=function(t){var e;var r;var i=t[0];if(i==t[1].source){r=t[1].target}else{r=t[1].source}var n=r.startX;var o=r.finishX;var a=r.startY;var s=r.finishY;var h=0;var c=0;var u=0;var g=0;var d=[h,u,c,g];if(a>0){for(var f=n;f<=o;f++){d[0]+=this.grid[f][a-1].length+this.grid[f][a].length-1}}if(o<this.grid.length-1){for(var f=a;f<=s;f++){d[1]+=this.grid[o+1][f].length+this.grid[o][f].length-1}}if(s<this.grid[0].length-1){for(var f=n;f<=o;f++){d[2]+=this.grid[f][s+1].length+this.grid[f][s].length-1}}if(n>0){for(var f=a;f<=s;f++){d[3]+=this.grid[n-1][f].length+this.grid[n][f].length-1}}var v=p.MAX_VALUE;var y;var E;for(var _=0;_<d.length;_++){if(d[_]<v){v=d[_];y=1;E=_}else if(d[_]==v){y++}}if(y==3&&v==0){if(d[0]==0&&d[1]==0&&d[2]==0){e=1}else if(d[0]==0&&d[1]==0&&d[3]==0){e=0}else if(d[0]==0&&d[2]==0&&d[3]==0){e=3}else if(d[1]==0&&d[2]==0&&d[3]==0){e=2}}else if(y==2&&v==0){var m=Math.floor(Math.random()*2);if(d[0]==0&&d[1]==0){if(m==0){e=0}else{e=1}}else if(d[0]==0&&d[2]==0){if(m==0){e=0}else{e=2}}else if(d[0]==0&&d[3]==0){if(m==0){e=0}else{e=3}}else if(d[1]==0&&d[2]==0){if(m==0){e=1}else{e=2}}else if(d[1]==0&&d[3]==0){if(m==0){e=1}else{e=3}}else{if(m==0){e=2}else{e=3}}}else if(y==4&&v==0){var m=Math.floor(Math.random()*4);e=m}else{e=E}if(e==0){i.setCenter(r.getCenterX(),r.getCenterY()-r.getHeight()/2-l.DEFAULT_EDGE_LENGTH-i.getHeight()/2)}else if(e==1){i.setCenter(r.getCenterX()+r.getWidth()/2+l.DEFAULT_EDGE_LENGTH+i.getWidth()/2,r.getCenterY())}else if(e==2){i.setCenter(r.getCenterX(),r.getCenterY()+r.getHeight()/2+l.DEFAULT_EDGE_LENGTH+i.getHeight()/2)}else{i.setCenter(r.getCenterX()-r.getWidth()/2-l.DEFAULT_EDGE_LENGTH-i.getWidth()/2,r.getCenterY())}};t.exports=E},function(t,e,r){"use strict";var i={};i.layoutBase=r(0);i.CoSEConstants=r(1);i.CoSEEdge=r(2);i.CoSEGraph=r(3);i.CoSEGraphManager=r(4);i.CoSELayout=r(6);i.CoSENode=r(5);t.exports=i}])}))},43457:function(t,e,r){(function e(i,n){if(true)t.exports=n(r(87799));else{}})(this,(function(t){return function(t){var e={};function r(i){if(e[i]){return e[i].exports}var n=e[i]={i,l:false,exports:{}};t[i].call(n.exports,n,n.exports,r);n.l=true;return n.exports}r.m=t;r.c=e;r.i=function(t){return t};r.d=function(t,e,i){if(!r.o(t,e)){Object.defineProperty(t,e,{configurable:false,enumerable:true,get:i})}};r.n=function(t){var e=t&&t.__esModule?function e(){return t["default"]}:function e(){return t};r.d(e,"a",e);return e};r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};r.p="";return r(r.s=1)}([function(e,r){e.exports=t},function(t,e,r){"use strict";var i=r(0).layoutBase.LayoutConstants;var n=r(0).layoutBase.FDLayoutConstants;var o=r(0).CoSEConstants;var a=r(0).CoSELayout;var s=r(0).CoSENode;var h=r(0).layoutBase.PointD;var l=r(0).layoutBase.DimensionD;var c={ready:function t(){},stop:function t(){},quality:"default",nodeDimensionsIncludeLabels:false,refresh:30,fit:true,padding:10,randomize:true,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:true,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function u(t,e){var r={};for(var i in t){r[i]=t[i]}for(var i in e){r[i]=e[i]}return r}function g(t){this.options=u(c,t);d(this.options)}var d=function t(e){if(e.nodeRepulsion!=null)o.DEFAULT_REPULSION_STRENGTH=n.DEFAULT_REPULSION_STRENGTH=e.nodeRepulsion;if(e.idealEdgeLength!=null)o.DEFAULT_EDGE_LENGTH=n.DEFAULT_EDGE_LENGTH=e.idealEdgeLength;if(e.edgeElasticity!=null)o.DEFAULT_SPRING_STRENGTH=n.DEFAULT_SPRING_STRENGTH=e.edgeElasticity;if(e.nestingFactor!=null)o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=n.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=e.nestingFactor;if(e.gravity!=null)o.DEFAULT_GRAVITY_STRENGTH=n.DEFAULT_GRAVITY_STRENGTH=e.gravity;if(e.numIter!=null)o.MAX_ITERATIONS=n.MAX_ITERATIONS=e.numIter;if(e.gravityRange!=null)o.DEFAULT_GRAVITY_RANGE_FACTOR=n.DEFAULT_GRAVITY_RANGE_FACTOR=e.gravityRange;if(e.gravityCompound!=null)o.DEFAULT_COMPOUND_GRAVITY_STRENGTH=n.DEFAULT_COMPOUND_GRAVITY_STRENGTH=e.gravityCompound;if(e.gravityRangeCompound!=null)o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=n.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=e.gravityRangeCompound;if(e.initialEnergyOnIncremental!=null)o.DEFAULT_COOLING_FACTOR_INCREMENTAL=n.DEFAULT_COOLING_FACTOR_INCREMENTAL=e.initialEnergyOnIncremental;if(e.quality=="draft")i.QUALITY=0;else if(e.quality=="proof")i.QUALITY=2;else i.QUALITY=1;o.NODE_DIMENSIONS_INCLUDE_LABELS=n.NODE_DIMENSIONS_INCLUDE_LABELS=i.NODE_DIMENSIONS_INCLUDE_LABELS=e.nodeDimensionsIncludeLabels;o.DEFAULT_INCREMENTAL=n.DEFAULT_INCREMENTAL=i.DEFAULT_INCREMENTAL=!e.randomize;o.ANIMATE=n.ANIMATE=i.ANIMATE=e.animate;o.TILE=e.tile;o.TILING_PADDING_VERTICAL=typeof e.tilingPaddingVertical==="function"?e.tilingPaddingVertical.call():e.tilingPaddingVertical;o.TILING_PADDING_HORIZONTAL=typeof e.tilingPaddingHorizontal==="function"?e.tilingPaddingHorizontal.call():e.tilingPaddingHorizontal};g.prototype.run=function(){var t;var e;var r=this.options;var i=this.idToLNode={};var n=this.layout=new a;var o=this;o.stopped=false;this.cy=this.options.cy;this.cy.trigger({type:"layoutstart",layout:this});var s=n.newGraphManager();this.gm=s;var h=this.options.eles.nodes();var l=this.options.eles.edges();this.root=s.addRoot();this.processChildrenList(this.root,this.getTopMostNodes(h),n);for(var c=0;c<l.length;c++){var u=l[c];var g=this.idToLNode[u.data("source")];var d=this.idToLNode[u.data("target")];if(g!==d&&g.getEdgesBetween(d).length==0){var p=s.add(n.newEdge(),g,d);p.id=u.id()}}var f=function t(e,r){if(typeof e==="number"){e=r}var i=e.data("id");var n=o.idToLNode[i];return{x:n.getRect().getCenterX(),y:n.getRect().getCenterY()}};var v=function i(){var a=function e(){if(r.fit){r.cy.fit(r.eles,r.padding)}if(!t){t=true;o.cy.one("layoutready",r.ready);o.cy.trigger({type:"layoutready",layout:o})}};var s=o.options.refresh;var h;for(var l=0;l<s&&!h;l++){h=o.stopped||o.layout.tick()}if(h){if(n.checkLayoutSuccess()&&!n.isSubLayout){n.doPostLayout()}if(n.tilingPostLayout){n.tilingPostLayout()}n.isLayoutFinished=true;o.options.eles.nodes().positions(f);a();o.cy.one("layoutstop",o.options.stop);o.cy.trigger({type:"layoutstop",layout:o});if(e){cancelAnimationFrame(e)}t=false;return}var c=o.layout.getPositionsData();r.eles.nodes().positions((function(t,e){if(typeof t==="number"){t=e}if(!t.isParent()){var r=t.id();var i=c[r];var n=t;while(i==null){i=c[n.data("parent")]||c["DummyCompound_"+n.data("parent")];c[r]=i;n=n.parent()[0];if(n==undefined){break}}if(i!=null){return{x:i.x,y:i.y}}else{return{x:t.position("x"),y:t.position("y")}}}}));a();e=requestAnimationFrame(i)};n.addListener("layoutstarted",(function(){if(o.options.animate==="during"){e=requestAnimationFrame(v)}}));n.runLayout();if(this.options.animate!=="during"){o.options.eles.nodes().not(":parent").layoutPositions(o,o.options,f);t=false}return this};g.prototype.getTopMostNodes=function(t){var e={};for(var r=0;r<t.length;r++){e[t[r].id()]=true}var i=t.filter((function(t,r){if(typeof t==="number"){t=r}var i=t.parent()[0];while(i!=null){if(e[i.id()]){return false}i=i.parent()[0]}return true}));return i};g.prototype.processChildrenList=function(t,e,r){var i=e.length;for(var n=0;n<i;n++){var o=e[n];var a=o.children();var c;var u=o.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if(o.outerWidth()!=null&&o.outerHeight()!=null){c=t.add(new s(r.graphManager,new h(o.position("x")-u.w/2,o.position("y")-u.h/2),new l(parseFloat(u.w),parseFloat(u.h))))}else{c=t.add(new s(this.graphManager))}c.id=o.data("id");c.paddingLeft=parseInt(o.css("padding"));c.paddingTop=parseInt(o.css("padding"));c.paddingRight=parseInt(o.css("padding"));c.paddingBottom=parseInt(o.css("padding"));if(this.options.nodeDimensionsIncludeLabels){if(o.isParent()){var g=o.boundingBox({includeLabels:true,includeNodes:false}).w;var d=o.boundingBox({includeLabels:true,includeNodes:false}).h;var p=o.css("text-halign");c.labelWidth=g;c.labelHeight=d;c.labelPos=p}}this.idToLNode[o.data("id")]=c;if(isNaN(c.rect.x)){c.rect.x=0}if(isNaN(c.rect.y)){c.rect.y=0}if(a!=null&&a.length>0){var f;f=r.getGraphManager().add(r.newGraph(),c);this.processChildrenList(f,a,r)}}};g.prototype.stop=function(){this.stopped=true;return this};var p=function t(e){e("layout","cose-bilkent",g)};if(typeof cytoscape!=="undefined"){p(cytoscape)}t.exports=p}])}))},23143:function(t){(function e(r,i){if(true)t.exports=i();else{}})(this,(function(){return function(t){var e={};function r(i){if(e[i]){return e[i].exports}var n=e[i]={i,l:false,exports:{}};t[i].call(n.exports,n,n.exports,r);n.l=true;return n.exports}r.m=t;r.c=e;r.i=function(t){return t};r.d=function(t,e,i){if(!r.o(t,e)){Object.defineProperty(t,e,{configurable:false,enumerable:true,get:i})}};r.n=function(t){var e=t&&t.__esModule?function e(){return t["default"]}:function e(){return t};r.d(e,"a",e);return e};r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};r.p="";return r(r.s=26)}([function(t,e,r){"use strict";function i(){}i.QUALITY=1;i.DEFAULT_CREATE_BENDS_AS_NEEDED=false;i.DEFAULT_INCREMENTAL=false;i.DEFAULT_ANIMATION_ON_LAYOUT=true;i.DEFAULT_ANIMATION_DURING_LAYOUT=false;i.DEFAULT_ANIMATION_PERIOD=50;i.DEFAULT_UNIFORM_LEAF_NODE_SIZES=false;i.DEFAULT_GRAPH_MARGIN=15;i.NODE_DIMENSIONS_INCLUDE_LABELS=false;i.SIMPLE_NODE_SIZE=40;i.SIMPLE_NODE_HALF_SIZE=i.SIMPLE_NODE_SIZE/2;i.EMPTY_COMPOUND_NODE_SIZE=40;i.MIN_EDGE_LENGTH=1;i.WORLD_BOUNDARY=1e6;i.INITIAL_WORLD_BOUNDARY=i.WORLD_BOUNDARY/1e3;i.WORLD_CENTER_X=1200;i.WORLD_CENTER_Y=900;t.exports=i},function(t,e,r){"use strict";var i=r(2);var n=r(8);var o=r(9);function a(t,e,r){i.call(this,r);this.isOverlapingSourceAndTarget=false;this.vGraphObject=r;this.bendpoints=[];this.source=t;this.target=e}a.prototype=Object.create(i.prototype);for(var s in i){a[s]=i[s]}a.prototype.getSource=function(){return this.source};a.prototype.getTarget=function(){return this.target};a.prototype.isInterGraph=function(){return this.isInterGraph};a.prototype.getLength=function(){return this.length};a.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget};a.prototype.getBendpoints=function(){return this.bendpoints};a.prototype.getLca=function(){return this.lca};a.prototype.getSourceInLca=function(){return this.sourceInLca};a.prototype.getTargetInLca=function(){return this.targetInLca};a.prototype.getOtherEnd=function(t){if(this.source===t){return this.target}else if(this.target===t){return this.source}else{throw"Node is not incident with this edge"}};a.prototype.getOtherEndInGraph=function(t,e){var r=this.getOtherEnd(t);var i=e.getGraphManager().getRoot();while(true){if(r.getOwner()==e){return r}if(r.getOwner()==i){break}r=r.getOwner().getParent()}return null};a.prototype.updateLength=function(){var t=new Array(4);this.isOverlapingSourceAndTarget=n.getIntersection(this.target.getRect(),this.source.getRect(),t);if(!this.isOverlapingSourceAndTarget){this.lengthX=t[0]-t[2];this.lengthY=t[1]-t[3];if(Math.abs(this.lengthX)<1){this.lengthX=o.sign(this.lengthX)}if(Math.abs(this.lengthY)<1){this.lengthY=o.sign(this.lengthY)}this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)}};a.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX();this.lengthY=this.target.getCenterY()-this.source.getCenterY();if(Math.abs(this.lengthX)<1){this.lengthX=o.sign(this.lengthX)}if(Math.abs(this.lengthY)<1){this.lengthY=o.sign(this.lengthY)}this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)};t.exports=a},function(t,e,r){"use strict";function i(t){this.vGraphObject=t}t.exports=i},function(t,e,r){"use strict";var i=r(2);var n=r(10);var o=r(13);var a=r(0);var s=r(16);var h=r(4);function l(t,e,r,a){if(r==null&&a==null){a=e}i.call(this,a);if(t.graphManager!=null)t=t.graphManager;this.estimatedSize=n.MIN_VALUE;this.inclusionTreeDepth=n.MAX_VALUE;this.vGraphObject=a;this.edges=[];this.graphManager=t;if(r!=null&&e!=null)this.rect=new o(e.x,e.y,r.width,r.height);else this.rect=new o}l.prototype=Object.create(i.prototype);for(var c in i){l[c]=i[c]}l.prototype.getEdges=function(){return this.edges};l.prototype.getChild=function(){return this.child};l.prototype.getOwner=function(){return this.owner};l.prototype.getWidth=function(){return this.rect.width};l.prototype.setWidth=function(t){this.rect.width=t};l.prototype.getHeight=function(){return this.rect.height};l.prototype.setHeight=function(t){this.rect.height=t};l.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2};l.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2};l.prototype.getCenter=function(){return new h(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)};l.prototype.getLocation=function(){return new h(this.rect.x,this.rect.y)};l.prototype.getRect=function(){return this.rect};l.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)};l.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2};l.prototype.setRect=function(t,e){this.rect.x=t.x;this.rect.y=t.y;this.rect.width=e.width;this.rect.height=e.height};l.prototype.setCenter=function(t,e){this.rect.x=t-this.rect.width/2;this.rect.y=e-this.rect.height/2};l.prototype.setLocation=function(t,e){this.rect.x=t;this.rect.y=e};l.prototype.moveBy=function(t,e){this.rect.x+=t;this.rect.y+=e};l.prototype.getEdgeListToNode=function(t){var e=[];var r;var i=this;i.edges.forEach((function(r){if(r.target==t){if(r.source!=i)throw"Incorrect edge source!";e.push(r)}}));return e};l.prototype.getEdgesBetween=function(t){var e=[];var r;var i=this;i.edges.forEach((function(r){if(!(r.source==i||r.target==i))throw"Incorrect edge source and/or target";if(r.target==t||r.source==t){e.push(r)}}));return e};l.prototype.getNeighborsList=function(){var t=new Set;var e=this;e.edges.forEach((function(r){if(r.source==e){t.add(r.target)}else{if(r.target!=e){throw"Incorrect incidency!"}t.add(r.source)}}));return t};l.prototype.withChildren=function(){var t=new Set;var e;var r;t.add(this);if(this.child!=null){var i=this.child.getNodes();for(var n=0;n<i.length;n++){e=i[n];r=e.withChildren();r.forEach((function(e){t.add(e)}))}}return t};l.prototype.getNoOfChildren=function(){var t=0;var e;if(this.child==null){t=1}else{var r=this.child.getNodes();for(var i=0;i<r.length;i++){e=r[i];t+=e.getNoOfChildren()}}if(t==0){t=1}return t};l.prototype.getEstimatedSize=function(){if(this.estimatedSize==n.MIN_VALUE){throw"assert failed"}return this.estimatedSize};l.prototype.calcEstimatedSize=function(){if(this.child==null){return this.estimatedSize=(this.rect.width+this.rect.height)/2}else{this.estimatedSize=this.child.calcEstimatedSize();this.rect.width=this.estimatedSize;this.rect.height=this.estimatedSize;return this.estimatedSize}};l.prototype.scatter=function(){var t;var e;var r=-a.INITIAL_WORLD_BOUNDARY;var i=a.INITIAL_WORLD_BOUNDARY;t=a.WORLD_CENTER_X+s.nextDouble()*(i-r)+r;var n=-a.INITIAL_WORLD_BOUNDARY;var o=a.INITIAL_WORLD_BOUNDARY;e=a.WORLD_CENTER_Y+s.nextDouble()*(o-n)+n;this.rect.x=t;this.rect.y=e};l.prototype.updateBounds=function(){if(this.getChild()==null){throw"assert failed"}if(this.getChild().getNodes().length!=0){var t=this.getChild();t.updateBounds(true);this.rect.x=t.getLeft();this.rect.y=t.getTop();this.setWidth(t.getRight()-t.getLeft());this.setHeight(t.getBottom()-t.getTop());if(a.NODE_DIMENSIONS_INCLUDE_LABELS){var e=t.getRight()-t.getLeft();var r=t.getBottom()-t.getTop();if(this.labelWidth>e){this.rect.x-=(this.labelWidth-e)/2;this.setWidth(this.labelWidth)}if(this.labelHeight>r){if(this.labelPos=="center"){this.rect.y-=(this.labelHeight-r)/2}else if(this.labelPos=="top"){this.rect.y-=this.labelHeight-r}this.setHeight(this.labelHeight)}}}};l.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==n.MAX_VALUE){throw"assert failed"}return this.inclusionTreeDepth};l.prototype.transform=function(t){var e=this.rect.x;if(e>a.WORLD_BOUNDARY){e=a.WORLD_BOUNDARY}else if(e<-a.WORLD_BOUNDARY){e=-a.WORLD_BOUNDARY}var r=this.rect.y;if(r>a.WORLD_BOUNDARY){r=a.WORLD_BOUNDARY}else if(r<-a.WORLD_BOUNDARY){r=-a.WORLD_BOUNDARY}var i=new h(e,r);var n=t.inverseTransformPoint(i);this.setLocation(n.x,n.y)};l.prototype.getLeft=function(){return this.rect.x};l.prototype.getRight=function(){return this.rect.x+this.rect.width};l.prototype.getTop=function(){return this.rect.y};l.prototype.getBottom=function(){return this.rect.y+this.rect.height};l.prototype.getParent=function(){if(this.owner==null){return null}return this.owner.getParent()};t.exports=l},function(t,e,r){"use strict";function i(t,e){if(t==null&&e==null){this.x=0;this.y=0}else{this.x=t;this.y=e}}i.prototype.getX=function(){return this.x};i.prototype.getY=function(){return this.y};i.prototype.setX=function(t){this.x=t};i.prototype.setY=function(t){this.y=t};i.prototype.getDifference=function(t){return new DimensionD(this.x-t.x,this.y-t.y)};i.prototype.getCopy=function(){return new i(this.x,this.y)};i.prototype.translate=function(t){this.x+=t.width;this.y+=t.height;return this};t.exports=i},function(t,e,r){"use strict";var i=r(2);var n=r(10);var o=r(0);var a=r(6);var s=r(3);var h=r(1);var l=r(13);var c=r(12);var u=r(11);function g(t,e,r){i.call(this,r);this.estimatedSize=n.MIN_VALUE;this.margin=o.DEFAULT_GRAPH_MARGIN;this.edges=[];this.nodes=[];this.isConnected=false;this.parent=t;if(e!=null&&e instanceof a){this.graphManager=e}else if(e!=null&&e instanceof Layout){this.graphManager=e.graphManager}}g.prototype=Object.create(i.prototype);for(var d in i){g[d]=i[d]}g.prototype.getNodes=function(){return this.nodes};g.prototype.getEdges=function(){return this.edges};g.prototype.getGraphManager=function(){return this.graphManager};g.prototype.getParent=function(){return this.parent};g.prototype.getLeft=function(){return this.left};g.prototype.getRight=function(){return this.right};g.prototype.getTop=function(){return this.top};g.prototype.getBottom=function(){return this.bottom};g.prototype.isConnected=function(){return this.isConnected};g.prototype.add=function(t,e,r){if(e==null&&r==null){var i=t;if(this.graphManager==null){throw"Graph has no graph mgr!"}if(this.getNodes().indexOf(i)>-1){throw"Node already in graph!"}i.owner=this;this.getNodes().push(i);return i}else{var n=t;if(!(this.getNodes().indexOf(e)>-1&&this.getNodes().indexOf(r)>-1)){throw"Source or target not in graph!"}if(!(e.owner==r.owner&&e.owner==this)){throw"Both owners must be this graph!"}if(e.owner!=r.owner){return null}n.source=e;n.target=r;n.isInterGraph=false;this.getEdges().push(n);e.edges.push(n);if(r!=e){r.edges.push(n)}return n}};g.prototype.remove=function(t){var e=t;if(t instanceof s){if(e==null){throw"Node is null!"}if(!(e.owner!=null&&e.owner==this)){throw"Owner graph is invalid!"}if(this.graphManager==null){throw"Owner graph manager is invalid!"}var r=e.edges.slice();var i;var n=r.length;for(var o=0;o<n;o++){i=r[o];if(i.isInterGraph){this.graphManager.remove(i)}else{i.source.owner.remove(i)}}var a=this.nodes.indexOf(e);if(a==-1){throw"Node not in owner node list!"}this.nodes.splice(a,1)}else if(t instanceof h){var i=t;if(i==null){throw"Edge is null!"}if(!(i.source!=null&&i.target!=null)){throw"Source and/or target is null!"}if(!(i.source.owner!=null&&i.target.owner!=null&&i.source.owner==this&&i.target.owner==this)){throw"Source and/or target owner is invalid!"}var l=i.source.edges.indexOf(i);var c=i.target.edges.indexOf(i);if(!(l>-1&&c>-1)){throw"Source and/or target doesn't know this edge!"}i.source.edges.splice(l,1);if(i.target!=i.source){i.target.edges.splice(c,1)}var a=i.source.owner.getEdges().indexOf(i);if(a==-1){throw"Not in owner's edge list!"}i.source.owner.getEdges().splice(a,1)}};g.prototype.updateLeftTop=function(){var t=n.MAX_VALUE;var e=n.MAX_VALUE;var r;var i;var o;var a=this.getNodes();var s=a.length;for(var h=0;h<s;h++){var l=a[h];r=l.getTop();i=l.getLeft();if(t>r){t=r}if(e>i){e=i}}if(t==n.MAX_VALUE){return null}if(a[0].getParent().paddingLeft!=undefined){o=a[0].getParent().paddingLeft}else{o=this.margin}this.left=e-o;this.top=t-o;return new c(this.left,this.top)};g.prototype.updateBounds=function(t){var e=n.MAX_VALUE;var r=-n.MAX_VALUE;var i=n.MAX_VALUE;var o=-n.MAX_VALUE;var a;var s;var h;var c;var u;var g=this.nodes;var d=g.length;for(var p=0;p<d;p++){var f=g[p];if(t&&f.child!=null){f.updateBounds()}a=f.getLeft();s=f.getRight();h=f.getTop();c=f.getBottom();if(e>a){e=a}if(r<s){r=s}if(i>h){i=h}if(o<c){o=c}}var v=new l(e,i,r-e,o-i);if(e==n.MAX_VALUE){this.left=this.parent.getLeft();this.right=this.parent.getRight();this.top=this.parent.getTop();this.bottom=this.parent.getBottom()}if(g[0].getParent().paddingLeft!=undefined){u=g[0].getParent().paddingLeft}else{u=this.margin}this.left=v.x-u;this.right=v.x+v.width+u;this.top=v.y-u;this.bottom=v.y+v.height+u};g.calculateBounds=function(t){var e=n.MAX_VALUE;var r=-n.MAX_VALUE;var i=n.MAX_VALUE;var o=-n.MAX_VALUE;var a;var s;var h;var c;var u=t.length;for(var g=0;g<u;g++){var d=t[g];a=d.getLeft();s=d.getRight();h=d.getTop();c=d.getBottom();if(e>a){e=a}if(r<s){r=s}if(i>h){i=h}if(o<c){o=c}}var p=new l(e,i,r-e,o-i);return p};g.prototype.getInclusionTreeDepth=function(){if(this==this.graphManager.getRoot()){return 1}else{return this.parent.getInclusionTreeDepth()}};g.prototype.getEstimatedSize=function(){if(this.estimatedSize==n.MIN_VALUE){throw"assert failed"}return this.estimatedSize};g.prototype.calcEstimatedSize=function(){var t=0;var e=this.nodes;var r=e.length;for(var i=0;i<r;i++){var n=e[i];t+=n.calcEstimatedSize()}if(t==0){this.estimatedSize=o.EMPTY_COMPOUND_NODE_SIZE}else{this.estimatedSize=t/Math.sqrt(this.nodes.length)}return this.estimatedSize};g.prototype.updateConnected=function(){var t=this;if(this.nodes.length==0){this.isConnected=true;return}var e=new u;var r=new Set;var i=this.nodes[0];var n;var o;var a=i.withChildren();a.forEach((function(t){e.push(t);r.add(t)}));while(e.length!==0){i=e.shift();n=i.getEdges();var s=n.length;for(var h=0;h<s;h++){var l=n[h];o=l.getOtherEndInGraph(i,this);if(o!=null&&!r.has(o)){var c=o.withChildren();c.forEach((function(t){e.push(t);r.add(t)}))}}}this.isConnected=false;if(r.size>=this.nodes.length){var g=0;r.forEach((function(e){if(e.owner==t){g++}}));if(g==this.nodes.length){this.isConnected=true}}};t.exports=g},function(t,e,r){"use strict";var i;var n=r(1);function o(t){i=r(5);this.layout=t;this.graphs=[];this.edges=[]}o.prototype.addRoot=function(){var t=this.layout.newGraph();var e=this.layout.newNode(null);var r=this.add(t,e);this.setRootGraph(r);return this.rootGraph};o.prototype.add=function(t,e,r,i,n){if(r==null&&i==null&&n==null){if(t==null){throw"Graph is null!"}if(e==null){throw"Parent node is null!"}if(this.graphs.indexOf(t)>-1){throw"Graph already in this graph mgr!"}this.graphs.push(t);if(t.parent!=null){throw"Already has a parent!"}if(e.child!=null){throw"Already has a child!"}t.parent=e;e.child=t;return t}else{n=r;i=e;r=t;var o=i.getOwner();var a=n.getOwner();if(!(o!=null&&o.getGraphManager()==this)){throw"Source not in this graph mgr!"}if(!(a!=null&&a.getGraphManager()==this)){throw"Target not in this graph mgr!"}if(o==a){r.isInterGraph=false;return o.add(r,i,n)}else{r.isInterGraph=true;r.source=i;r.target=n;if(this.edges.indexOf(r)>-1){throw"Edge already in inter-graph edge list!"}this.edges.push(r);if(!(r.source!=null&&r.target!=null)){throw"Edge source and/or target is null!"}if(!(r.source.edges.indexOf(r)==-1&&r.target.edges.indexOf(r)==-1)){throw"Edge already in source and/or target incidency list!"}r.source.edges.push(r);r.target.edges.push(r);return r}}};o.prototype.remove=function(t){if(t instanceof i){var e=t;if(e.getGraphManager()!=this){throw"Graph not in this graph mgr"}if(!(e==this.rootGraph||e.parent!=null&&e.parent.graphManager==this)){throw"Invalid parent node!"}var r=[];r=r.concat(e.getEdges());var o;var a=r.length;for(var s=0;s<a;s++){o=r[s];e.remove(o)}var h=[];h=h.concat(e.getNodes());var l;a=h.length;for(var s=0;s<a;s++){l=h[s];e.remove(l)}if(e==this.rootGraph){this.setRootGraph(null)}var c=this.graphs.indexOf(e);this.graphs.splice(c,1);e.parent=null}else if(t instanceof n){o=t;if(o==null){throw"Edge is null!"}if(!o.isInterGraph){throw"Not an inter-graph edge!"}if(!(o.source!=null&&o.target!=null)){throw"Source and/or target is null!"}if(!(o.source.edges.indexOf(o)!=-1&&o.target.edges.indexOf(o)!=-1)){throw"Source and/or target doesn't know this edge!"}var c=o.source.edges.indexOf(o);o.source.edges.splice(c,1);c=o.target.edges.indexOf(o);o.target.edges.splice(c,1);if(!(o.source.owner!=null&&o.source.owner.getGraphManager()!=null)){throw"Edge owner graph or owner graph manager is null!"}if(o.source.owner.getGraphManager().edges.indexOf(o)==-1){throw"Not in owner graph manager's edge list!"}var c=o.source.owner.getGraphManager().edges.indexOf(o);o.source.owner.getGraphManager().edges.splice(c,1)}};o.prototype.updateBounds=function(){this.rootGraph.updateBounds(true)};o.prototype.getGraphs=function(){return this.graphs};o.prototype.getAllNodes=function(){if(this.allNodes==null){var t=[];var e=this.getGraphs();var r=e.length;for(var i=0;i<r;i++){t=t.concat(e[i].getNodes())}this.allNodes=t}return this.allNodes};o.prototype.resetAllNodes=function(){this.allNodes=null};o.prototype.resetAllEdges=function(){this.allEdges=null};o.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null};o.prototype.getAllEdges=function(){if(this.allEdges==null){var t=[];var e=this.getGraphs();var r=e.length;for(var i=0;i<e.length;i++){t=t.concat(e[i].getEdges())}t=t.concat(this.edges);this.allEdges=t}return this.allEdges};o.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation};o.prototype.setAllNodesToApplyGravitation=function(t){if(this.allNodesToApplyGravitation!=null){throw"assert failed"}this.allNodesToApplyGravitation=t};o.prototype.getRoot=function(){return this.rootGraph};o.prototype.setRootGraph=function(t){if(t.getGraphManager()!=this){throw"Root not in this graph mgr!"}this.rootGraph=t;if(t.parent==null){t.parent=this.layout.newNode("Root node")}};o.prototype.getLayout=function(){return this.layout};o.prototype.isOneAncestorOfOther=function(t,e){if(!(t!=null&&e!=null)){throw"assert failed"}if(t==e){return true}var r=t.getOwner();var i;do{i=r.getParent();if(i==null){break}if(i==e){return true}r=i.getOwner();if(r==null){break}}while(true);r=e.getOwner();do{i=r.getParent();if(i==null){break}if(i==t){return true}r=i.getOwner();if(r==null){break}}while(true);return false};o.prototype.calcLowestCommonAncestors=function(){var t;var e;var r;var i;var n;var o=this.getAllEdges();var a=o.length;for(var s=0;s<a;s++){t=o[s];e=t.source;r=t.target;t.lca=null;t.sourceInLca=e;t.targetInLca=r;if(e==r){t.lca=e.getOwner();continue}i=e.getOwner();while(t.lca==null){t.targetInLca=r;n=r.getOwner();while(t.lca==null){if(n==i){t.lca=n;break}if(n==this.rootGraph){break}if(t.lca!=null){throw"assert failed"}t.targetInLca=n.getParent();n=t.targetInLca.getOwner()}if(i==this.rootGraph){break}if(t.lca==null){t.sourceInLca=i.getParent();i=t.sourceInLca.getOwner()}}if(t.lca==null){throw"assert failed"}}};o.prototype.calcLowestCommonAncestor=function(t,e){if(t==e){return t.getOwner()}var r=t.getOwner();do{if(r==null){break}var i=e.getOwner();do{if(i==null){break}if(i==r){return i}i=i.getParent().getOwner()}while(true);r=r.getParent().getOwner()}while(true);return r};o.prototype.calcInclusionTreeDepths=function(t,e){if(t==null&&e==null){t=this.rootGraph;e=1}var r;var i=t.getNodes();var n=i.length;for(var o=0;o<n;o++){r=i[o];r.inclusionTreeDepth=e;if(r.child!=null){this.calcInclusionTreeDepths(r.child,e+1)}}};o.prototype.includesInvalidEdge=function(){var t;var e=this.edges.length;for(var r=0;r<e;r++){t=this.edges[r];if(this.isOneAncestorOfOther(t.source,t.target)){return true}}return false};t.exports=o},function(t,e,r){"use strict";var i=r(0);function n(){}for(var o in i){n[o]=i[o]}n.MAX_ITERATIONS=2500;n.DEFAULT_EDGE_LENGTH=50;n.DEFAULT_SPRING_STRENGTH=.45;n.DEFAULT_REPULSION_STRENGTH=4500;n.DEFAULT_GRAVITY_STRENGTH=.4;n.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1;n.DEFAULT_GRAVITY_RANGE_FACTOR=3.8;n.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5;n.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=true;n.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=true;n.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3;n.COOLING_ADAPTATION_FACTOR=.33;n.ADAPTATION_LOWER_NODE_LIMIT=1e3;n.ADAPTATION_UPPER_NODE_LIMIT=5e3;n.MAX_NODE_DISPLACEMENT_INCREMENTAL=100;n.MAX_NODE_DISPLACEMENT=n.MAX_NODE_DISPLACEMENT_INCREMENTAL*3;n.MIN_REPULSION_DIST=n.DEFAULT_EDGE_LENGTH/10;n.CONVERGENCE_CHECK_PERIOD=100;n.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1;n.MIN_EDGE_LENGTH=1;n.GRID_CALCULATION_CHECK_PERIOD=10;t.exports=n},function(t,e,r){"use strict";var i=r(12);function n(){}n.calcSeparationAmount=function(t,e,r,i){if(!t.intersects(e)){throw"assert failed"}var n=new Array(2);this.decideDirectionsForOverlappingNodes(t,e,n);r[0]=Math.min(t.getRight(),e.getRight())-Math.max(t.x,e.x);r[1]=Math.min(t.getBottom(),e.getBottom())-Math.max(t.y,e.y);if(t.getX()<=e.getX()&&t.getRight()>=e.getRight()){r[0]+=Math.min(e.getX()-t.getX(),t.getRight()-e.getRight())}else if(e.getX()<=t.getX()&&e.getRight()>=t.getRight()){r[0]+=Math.min(t.getX()-e.getX(),e.getRight()-t.getRight())}if(t.getY()<=e.getY()&&t.getBottom()>=e.getBottom()){r[1]+=Math.min(e.getY()-t.getY(),t.getBottom()-e.getBottom())}else if(e.getY()<=t.getY()&&e.getBottom()>=t.getBottom()){r[1]+=Math.min(t.getY()-e.getY(),e.getBottom()-t.getBottom())}var o=Math.abs((e.getCenterY()-t.getCenterY())/(e.getCenterX()-t.getCenterX()));if(e.getCenterY()===t.getCenterY()&&e.getCenterX()===t.getCenterX()){o=1}var a=o*r[0];var s=r[1]/o;if(r[0]<s){s=r[0]}else{a=r[1]}r[0]=-1*n[0]*(s/2+i);r[1]=-1*n[1]*(a/2+i)};n.decideDirectionsForOverlappingNodes=function(t,e,r){if(t.getCenterX()<e.getCenterX()){r[0]=-1}else{r[0]=1}if(t.getCenterY()<e.getCenterY()){r[1]=-1}else{r[1]=1}};n.getIntersection2=function(t,e,r){var i=t.getCenterX();var n=t.getCenterY();var o=e.getCenterX();var a=e.getCenterY();if(t.intersects(e)){r[0]=i;r[1]=n;r[2]=o;r[3]=a;return true}var s=t.getX();var h=t.getY();var l=t.getRight();var c=t.getX();var u=t.getBottom();var g=t.getRight();var d=t.getWidthHalf();var p=t.getHeightHalf();var f=e.getX();var v=e.getY();var y=e.getRight();var E=e.getX();var _=e.getBottom();var m=e.getRight();var N=e.getWidthHalf();var A=e.getHeightHalf();var L=false;var T=false;if(i===o){if(n>a){r[0]=i;r[1]=h;r[2]=o;r[3]=_;return false}else if(n<a){r[0]=i;r[1]=u;r[2]=o;r[3]=v;return false}else{}}else if(n===a){if(i>o){r[0]=s;r[1]=n;r[2]=y;r[3]=a;return false}else if(i<o){r[0]=l;r[1]=n;r[2]=f;r[3]=a;return false}else{}}else{var O=t.height/t.width;var D=e.height/e.width;var I=(a-n)/(o-i);var w=void 0;var R=void 0;var C=void 0;var M=void 0;var x=void 0;var b=void 0;if(-O===I){if(i>o){r[0]=c;r[1]=u;L=true}else{r[0]=l;r[1]=h;L=true}}else if(O===I){if(i>o){r[0]=s;r[1]=h;L=true}else{r[0]=g;r[1]=u;L=true}}if(-D===I){if(o>i){r[2]=E;r[3]=_;T=true}else{r[2]=y;r[3]=v;T=true}}else if(D===I){if(o>i){r[2]=f;r[3]=v;T=true}else{r[2]=m;r[3]=_;T=true}}if(L&&T){return false}if(i>o){if(n>a){w=this.getCardinalDirection(O,I,4);R=this.getCardinalDirection(D,I,2)}else{w=this.getCardinalDirection(-O,I,3);R=this.getCardinalDirection(-D,I,1)}}else{if(n>a){w=this.getCardinalDirection(-O,I,1);R=this.getCardinalDirection(-D,I,3)}else{w=this.getCardinalDirection(O,I,2);R=this.getCardinalDirection(D,I,4)}}if(!L){switch(w){case 1:M=h;C=i+-p/I;r[0]=C;r[1]=M;break;case 2:C=g;M=n+d*I;r[0]=C;r[1]=M;break;case 3:M=u;C=i+p/I;r[0]=C;r[1]=M;break;case 4:C=c;M=n+-d*I;r[0]=C;r[1]=M;break}}if(!T){switch(R){case 1:b=v;x=o+-A/I;r[2]=x;r[3]=b;break;case 2:x=m;b=a+N*I;r[2]=x;r[3]=b;break;case 3:b=_;x=o+A/I;r[2]=x;r[3]=b;break;case 4:x=E;b=a+-N*I;r[2]=x;r[3]=b;break}}}return false};n.getCardinalDirection=function(t,e,r){if(t>e){return r}else{return 1+r%4}};n.getIntersection=function(t,e,r,n){if(n==null){return this.getIntersection2(t,e,r)}var o=t.x;var a=t.y;var s=e.x;var h=e.y;var l=r.x;var c=r.y;var u=n.x;var g=n.y;var d=void 0,p=void 0;var f=void 0,v=void 0,y=void 0,E=void 0,_=void 0,m=void 0;var N=void 0;f=h-a;y=o-s;_=s*a-o*h;v=g-c;E=l-u;m=u*c-l*g;N=f*E-v*y;if(N===0){return null}d=(y*m-E*_)/N;p=(v*_-f*m)/N;return new i(d,p)};n.angleOfVector=function(t,e,r,i){var n=void 0;if(t!==r){n=Math.atan((i-e)/(r-t));if(r<t){n+=Math.PI}else if(i<e){n+=this.TWO_PI}}else if(i<e){n=this.ONE_AND_HALF_PI}else{n=this.HALF_PI}return n};n.doIntersect=function(t,e,r,i){var n=t.x;var o=t.y;var a=e.x;var s=e.y;var h=r.x;var l=r.y;var c=i.x;var u=i.y;var g=(a-n)*(u-l)-(c-h)*(s-o);if(g===0){return false}else{var d=((u-l)*(c-n)+(h-c)*(u-o))/g;var p=((o-s)*(c-n)+(a-n)*(u-o))/g;return 0<d&&d<1&&0<p&&p<1}};n.HALF_PI=.5*Math.PI;n.ONE_AND_HALF_PI=1.5*Math.PI;n.TWO_PI=2*Math.PI;n.THREE_PI=3*Math.PI;t.exports=n},function(t,e,r){"use strict";function i(){}i.sign=function(t){if(t>0){return 1}else if(t<0){return-1}else{return 0}};i.floor=function(t){return t<0?Math.ceil(t):Math.floor(t)};i.ceil=function(t){return t<0?Math.floor(t):Math.ceil(t)};t.exports=i},function(t,e,r){"use strict";function i(){}i.MAX_VALUE=2147483647;i.MIN_VALUE=-2147483648;t.exports=i},function(t,e,r){"use strict";var i=function(){function t(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(t,i.key,i)}}return function(e,r,i){if(r)t(e.prototype,r);if(i)t(e,i);return e}}();function n(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}var o=function t(e){return{value:e,next:null,prev:null}};var a=function t(e,r,i,n){if(e!==null){e.next=r}else{n.head=r}if(i!==null){i.prev=r}else{n.tail=r}r.prev=e;r.next=i;n.length++;return r};var s=function t(e,r){var i=e.prev,n=e.next;if(i!==null){i.next=n}else{r.head=n}if(n!==null){n.prev=i}else{r.tail=i}e.prev=e.next=null;r.length--;return e};var h=function(){function t(e){var r=this;n(this,t);this.length=0;this.head=null;this.tail=null;if(e!=null){e.forEach((function(t){return r.push(t)}))}}i(t,[{key:"size",value:function t(){return this.length}},{key:"insertBefore",value:function t(e,r){return a(r.prev,o(e),r,this)}},{key:"insertAfter",value:function t(e,r){return a(r,o(e),r.next,this)}},{key:"insertNodeBefore",value:function t(e,r){return a(r.prev,e,r,this)}},{key:"insertNodeAfter",value:function t(e,r){return a(r,e,r.next,this)}},{key:"push",value:function t(e){return a(this.tail,o(e),null,this)}},{key:"unshift",value:function t(e){return a(null,o(e),this.head,this)}},{key:"remove",value:function t(e){return s(e,this)}},{key:"pop",value:function t(){return s(this.tail,this).value}},{key:"popNode",value:function t(){return s(this.tail,this)}},{key:"shift",value:function t(){return s(this.head,this).value}},{key:"shiftNode",value:function t(){return s(this.head,this)}},{key:"get_object_at",value:function t(e){if(e<=this.length()){var r=1;var i=this.head;while(r<e){i=i.next;r++}return i.value}}},{key:"set_object_at",value:function t(e,r){if(e<=this.length()){var i=1;var n=this.head;while(i<e){n=n.next;i++}n.value=r}}}]);return t}();t.exports=h},function(t,e,r){"use strict";function i(t,e,r){this.x=null;this.y=null;if(t==null&&e==null&&r==null){this.x=0;this.y=0}else if(typeof t=="number"&&typeof e=="number"&&r==null){this.x=t;this.y=e}else if(t.constructor.name=="Point"&&e==null&&r==null){r=t;this.x=r.x;this.y=r.y}}i.prototype.getX=function(){return this.x};i.prototype.getY=function(){return this.y};i.prototype.getLocation=function(){return new i(this.x,this.y)};i.prototype.setLocation=function(t,e,r){if(t.constructor.name=="Point"&&e==null&&r==null){r=t;this.setLocation(r.x,r.y)}else if(typeof t=="number"&&typeof e=="number"&&r==null){if(parseInt(t)==t&&parseInt(e)==e){this.move(t,e)}else{this.x=Math.floor(t+.5);this.y=Math.floor(e+.5)}}};i.prototype.move=function(t,e){this.x=t;this.y=e};i.prototype.translate=function(t,e){this.x+=t;this.y+=e};i.prototype.equals=function(t){if(t.constructor.name=="Point"){var e=t;return this.x==e.x&&this.y==e.y}return this==t};i.prototype.toString=function(){return(new i).constructor.name+"[x="+this.x+",y="+this.y+"]"};t.exports=i},function(t,e,r){"use strict";function i(t,e,r,i){this.x=0;this.y=0;this.width=0;this.height=0;if(t!=null&&e!=null&&r!=null&&i!=null){this.x=t;this.y=e;this.width=r;this.height=i}}i.prototype.getX=function(){return this.x};i.prototype.setX=function(t){this.x=t};i.prototype.getY=function(){return this.y};i.prototype.setY=function(t){this.y=t};i.prototype.getWidth=function(){return this.width};i.prototype.setWidth=function(t){this.width=t};i.prototype.getHeight=function(){return this.height};i.prototype.setHeight=function(t){this.height=t};i.prototype.getRight=function(){return this.x+this.width};i.prototype.getBottom=function(){return this.y+this.height};i.prototype.intersects=function(t){if(this.getRight()<t.x){return false}if(this.getBottom()<t.y){return false}if(t.getRight()<this.x){return false}if(t.getBottom()<this.y){return false}return true};i.prototype.getCenterX=function(){return this.x+this.width/2};i.prototype.getMinX=function(){return this.getX()};i.prototype.getMaxX=function(){return this.getX()+this.width};i.prototype.getCenterY=function(){return this.y+this.height/2};i.prototype.getMinY=function(){return this.getY()};i.prototype.getMaxY=function(){return this.getY()+this.height};i.prototype.getWidthHalf=function(){return this.width/2};i.prototype.getHeightHalf=function(){return this.height/2};t.exports=i},function(t,e,r){"use strict";var i=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol==="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function n(){}n.lastID=0;n.createID=function(t){if(n.isPrimitive(t)){return t}if(t.uniqueID!=null){return t.uniqueID}t.uniqueID=n.getString();n.lastID++;return t.uniqueID};n.getString=function(t){if(t==null)t=n.lastID;return"Object#"+t+""};n.isPrimitive=function(t){var e=typeof t==="undefined"?"undefined":i(t);return t==null||e!="object"&&e!="function"};t.exports=n},function(t,e,r){"use strict";function i(t){if(Array.isArray(t)){for(var e=0,r=Array(t.length);e<t.length;e++){r[e]=t[e]}return r}else{return Array.from(t)}}var n=r(0);var o=r(6);var a=r(3);var s=r(1);var h=r(5);var l=r(4);var c=r(17);var u=r(27);function g(t){u.call(this);this.layoutQuality=n.QUALITY;this.createBendsAsNeeded=n.DEFAULT_CREATE_BENDS_AS_NEEDED;this.incremental=n.DEFAULT_INCREMENTAL;this.animationOnLayout=n.DEFAULT_ANIMATION_ON_LAYOUT;this.animationDuringLayout=n.DEFAULT_ANIMATION_DURING_LAYOUT;this.animationPeriod=n.DEFAULT_ANIMATION_PERIOD;this.uniformLeafNodeSizes=n.DEFAULT_UNIFORM_LEAF_NODE_SIZES;this.edgeToDummyNodes=new Map;this.graphManager=new o(this);this.isLayoutFinished=false;this.isSubLayout=false;this.isRemoteUse=false;if(t!=null){this.isRemoteUse=t}}g.RANDOM_SEED=1;g.prototype=Object.create(u.prototype);g.prototype.getGraphManager=function(){return this.graphManager};g.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()};g.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()};g.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()};g.prototype.newGraphManager=function(){var t=new o(this);this.graphManager=t;return t};g.prototype.newGraph=function(t){return new h(null,this.graphManager,t)};g.prototype.newNode=function(t){return new a(this.graphManager,t)};g.prototype.newEdge=function(t){return new s(null,null,t)};g.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()};g.prototype.runLayout=function(){this.isLayoutFinished=false;if(this.tilingPreLayout){this.tilingPreLayout()}this.initParameters();var t;if(this.checkLayoutSuccess()){t=false}else{t=this.layout()}if(n.ANIMATE==="during"){return false}if(t){if(!this.isSubLayout){this.doPostLayout()}}if(this.tilingPostLayout){this.tilingPostLayout()}this.isLayoutFinished=true;return t};g.prototype.doPostLayout=function(){if(!this.incremental){this.transform()}this.update()};g.prototype.update2=function(){if(this.createBendsAsNeeded){this.createBendpointsFromDummyNodes();this.graphManager.resetAllEdges()}if(!this.isRemoteUse){var t;var e=this.graphManager.getAllEdges();for(var r=0;r<e.length;r++){t=e[r]}var i;var n=this.graphManager.getRoot().getNodes();for(var r=0;r<n.length;r++){i=n[r]}this.update(this.graphManager.getRoot())}};g.prototype.update=function(t){if(t==null){this.update2()}else if(t instanceof a){var e=t;if(e.getChild()!=null){var r=e.getChild().getNodes();for(var i=0;i<r.length;i++){update(r[i])}}if(e.vGraphObject!=null){var n=e.vGraphObject;n.update(e)}}else if(t instanceof s){var o=t;if(o.vGraphObject!=null){var l=o.vGraphObject;l.update(o)}}else if(t instanceof h){var c=t;if(c.vGraphObject!=null){var u=c.vGraphObject;u.update(c)}}};g.prototype.initParameters=function(){if(!this.isSubLayout){this.layoutQuality=n.QUALITY;this.animationDuringLayout=n.DEFAULT_ANIMATION_DURING_LAYOUT;this.animationPeriod=n.DEFAULT_ANIMATION_PERIOD;this.animationOnLayout=n.DEFAULT_ANIMATION_ON_LAYOUT;this.incremental=n.DEFAULT_INCREMENTAL;this.createBendsAsNeeded=n.DEFAULT_CREATE_BENDS_AS_NEEDED;this.uniformLeafNodeSizes=n.DEFAULT_UNIFORM_LEAF_NODE_SIZES}if(this.animationDuringLayout){this.animationOnLayout=false}};g.prototype.transform=function(t){if(t==undefined){this.transform(new l(0,0))}else{var e=new c;var r=this.graphManager.getRoot().updateLeftTop();if(r!=null){e.setWorldOrgX(t.x);e.setWorldOrgY(t.y);e.setDeviceOrgX(r.x);e.setDeviceOrgY(r.y);var i=this.getAllNodes();var n;for(var o=0;o<i.length;o++){n=i[o];n.transform(e)}}}};g.prototype.positionNodesRandomly=function(t){if(t==undefined){this.positionNodesRandomly(this.getGraphManager().getRoot());this.getGraphManager().getRoot().updateBounds(true)}else{var e;var r;var i=t.getNodes();for(var n=0;n<i.length;n++){e=i[n];r=e.getChild();if(r==null){e.scatter()}else if(r.getNodes().length==0){e.scatter()}else{this.positionNodesRandomly(r);e.updateBounds()}}}};g.prototype.getFlatForest=function(){var t=[];var e=true;var r=this.graphManager.getRoot().getNodes();var n=true;for(var o=0;o<r.length;o++){if(r[o].getChild()!=null){n=false}}if(!n){return t}var a=new Set;var s=[];var h=new Map;var l=[];l=l.concat(r);while(l.length>0&&e){s.push(l[0]);while(s.length>0&&e){var c=s[0];s.splice(0,1);a.add(c);var u=c.getEdges();for(var o=0;o<u.length;o++){var g=u[o].getOtherEnd(c);if(h.get(c)!=g){if(!a.has(g)){s.push(g);h.set(g,c)}else{e=false;break}}}}if(!e){t=[]}else{var d=[].concat(i(a));t.push(d);for(var o=0;o<d.length;o++){var p=d[o];var f=l.indexOf(p);if(f>-1){l.splice(f,1)}}a=new Set;h=new Map}}return t};g.prototype.createDummyNodesForBendpoints=function(t){var e=[];var r=t.source;var i=this.graphManager.calcLowestCommonAncestor(t.source,t.target);for(var n=0;n<t.bendpoints.length;n++){var o=this.newNode(null);o.setRect(new Point(0,0),new Dimension(1,1));i.add(o);var a=this.newEdge(null);this.graphManager.add(a,r,o);e.add(o);r=o}var a=this.newEdge(null);this.graphManager.add(a,r,t.target);this.edgeToDummyNodes.set(t,e);if(t.isInterGraph()){this.graphManager.remove(t)}else{i.remove(t)}return e};g.prototype.createBendpointsFromDummyNodes=function(){var t=[];t=t.concat(this.graphManager.getAllEdges());t=[].concat(i(this.edgeToDummyNodes.keys())).concat(t);for(var e=0;e<t.length;e++){var r=t[e];if(r.bendpoints.length>0){var n=this.edgeToDummyNodes.get(r);for(var o=0;o<n.length;o++){var a=n[o];var s=new l(a.getCenterX(),a.getCenterY());var h=r.bendpoints.get(o);h.x=s.x;h.y=s.y;a.getOwner().remove(a)}this.graphManager.add(r,r.source,r.target)}}};g.transform=function(t,e,r,i){if(r!=undefined&&i!=undefined){var n=e;if(t<=50){var o=e/r;n-=(e-o)/50*(50-t)}else{var a=e*i;n+=(a-e)/50*(t-50)}return n}else{var s,h;if(t<=50){s=9*e/500;h=e/10}else{s=9*e/50;h=-8*e}return s*t+h}};g.findCenterOfTree=function(t){var e=[];e=e.concat(t);var r=[];var i=new Map;var n=false;var o=null;if(e.length==1||e.length==2){n=true;o=e[0]}for(var a=0;a<e.length;a++){var s=e[a];var h=s.getNeighborsList().size;i.set(s,s.getNeighborsList().size);if(h==1){r.push(s)}}var l=[];l=l.concat(r);while(!n){var c=[];c=c.concat(l);l=[];for(var a=0;a<e.length;a++){var s=e[a];var u=e.indexOf(s);if(u>=0){e.splice(u,1)}var g=s.getNeighborsList();g.forEach((function(t){if(r.indexOf(t)<0){var e=i.get(t);var n=e-1;if(n==1){l.push(t)}i.set(t,n)}}))}r=r.concat(l);if(e.length==1||e.length==2){n=true;o=e[0]}}return o};g.prototype.setGraphManager=function(t){this.graphManager=t};t.exports=g},function(t,e,r){"use strict";function i(){}i.seed=1;i.x=0;i.nextDouble=function(){i.x=Math.sin(i.seed++)*1e4;return i.x-Math.floor(i.x)};t.exports=i},function(t,e,r){"use strict";var i=r(4);function n(t,e){this.lworldOrgX=0;this.lworldOrgY=0;this.ldeviceOrgX=0;this.ldeviceOrgY=0;this.lworldExtX=1;this.lworldExtY=1;this.ldeviceExtX=1;this.ldeviceExtY=1}n.prototype.getWorldOrgX=function(){return this.lworldOrgX};n.prototype.setWorldOrgX=function(t){this.lworldOrgX=t};n.prototype.getWorldOrgY=function(){return this.lworldOrgY};n.prototype.setWorldOrgY=function(t){this.lworldOrgY=t};n.prototype.getWorldExtX=function(){return this.lworldExtX};n.prototype.setWorldExtX=function(t){this.lworldExtX=t};n.prototype.getWorldExtY=function(){return this.lworldExtY};n.prototype.setWorldExtY=function(t){this.lworldExtY=t};n.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX};n.prototype.setDeviceOrgX=function(t){this.ldeviceOrgX=t};n.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY};n.prototype.setDeviceOrgY=function(t){this.ldeviceOrgY=t};n.prototype.getDeviceExtX=function(){return this.ldeviceExtX};n.prototype.setDeviceExtX=function(t){this.ldeviceExtX=t};n.prototype.getDeviceExtY=function(){return this.ldeviceExtY};n.prototype.setDeviceExtY=function(t){this.ldeviceExtY=t};n.prototype.transformX=function(t){var e=0;var r=this.lworldExtX;if(r!=0){e=this.ldeviceOrgX+(t-this.lworldOrgX)*this.ldeviceExtX/r}return e};n.prototype.transformY=function(t){var e=0;var r=this.lworldExtY;if(r!=0){e=this.ldeviceOrgY+(t-this.lworldOrgY)*this.ldeviceExtY/r}return e};n.prototype.inverseTransformX=function(t){var e=0;var r=this.ldeviceExtX;if(r!=0){e=this.lworldOrgX+(t-this.ldeviceOrgX)*this.lworldExtX/r}return e};n.prototype.inverseTransformY=function(t){var e=0;var r=this.ldeviceExtY;if(r!=0){e=this.lworldOrgY+(t-this.ldeviceOrgY)*this.lworldExtY/r}return e};n.prototype.inverseTransformPoint=function(t){var e=new i(this.inverseTransformX(t.x),this.inverseTransformY(t.y));return e};t.exports=n},function(t,e,r){"use strict";function i(t){if(Array.isArray(t)){for(var e=0,r=Array(t.length);e<t.length;e++){r[e]=t[e]}return r}else{return Array.from(t)}}var n=r(15);var o=r(7);var a=r(0);var s=r(8);var h=r(9);function l(){n.call(this);this.useSmartIdealEdgeLengthCalculation=o.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;this.idealEdgeLength=o.DEFAULT_EDGE_LENGTH;this.springConstant=o.DEFAULT_SPRING_STRENGTH;this.repulsionConstant=o.DEFAULT_REPULSION_STRENGTH;this.gravityConstant=o.DEFAULT_GRAVITY_STRENGTH;this.compoundGravityConstant=o.DEFAULT_COMPOUND_GRAVITY_STRENGTH;this.gravityRangeFactor=o.DEFAULT_GRAVITY_RANGE_FACTOR;this.compoundGravityRangeFactor=o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;this.displacementThresholdPerNode=3*o.DEFAULT_EDGE_LENGTH/100;this.coolingFactor=o.DEFAULT_COOLING_FACTOR_INCREMENTAL;this.initialCoolingFactor=o.DEFAULT_COOLING_FACTOR_INCREMENTAL;this.totalDisplacement=0;this.oldTotalDisplacement=0;this.maxIterations=o.MAX_ITERATIONS}l.prototype=Object.create(n.prototype);for(var c in n){l[c]=n[c]}l.prototype.initParameters=function(){n.prototype.initParameters.call(this,arguments);this.totalIterations=0;this.notAnimatedIterations=0;this.useFRGridVariant=o.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;this.grid=[]};l.prototype.calcIdealEdgeLengths=function(){var t;var e;var r;var i;var n;var s;var h=this.getGraphManager().getAllEdges();for(var l=0;l<h.length;l++){t=h[l];t.idealLength=this.idealEdgeLength;if(t.isInterGraph){r=t.getSource();i=t.getTarget();n=t.getSourceInLca().getEstimatedSize();s=t.getTargetInLca().getEstimatedSize();if(this.useSmartIdealEdgeLengthCalculation){t.idealLength+=n+s-2*a.SIMPLE_NODE_SIZE}e=t.getLca().getInclusionTreeDepth();t.idealLength+=o.DEFAULT_EDGE_LENGTH*o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(r.getInclusionTreeDepth()+i.getInclusionTreeDepth()-2*e)}}};l.prototype.initSpringEmbedder=function(){var t=this.getAllNodes().length;if(this.incremental){if(t>o.ADAPTATION_LOWER_NODE_LIMIT){this.coolingFactor=Math.max(this.coolingFactor*o.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(t-o.ADAPTATION_LOWER_NODE_LIMIT)/(o.ADAPTATION_UPPER_NODE_LIMIT-o.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-o.COOLING_ADAPTATION_FACTOR))}this.maxNodeDisplacement=o.MAX_NODE_DISPLACEMENT_INCREMENTAL}else{if(t>o.ADAPTATION_LOWER_NODE_LIMIT){this.coolingFactor=Math.max(o.COOLING_ADAPTATION_FACTOR,1-(t-o.ADAPTATION_LOWER_NODE_LIMIT)/(o.ADAPTATION_UPPER_NODE_LIMIT-o.ADAPTATION_LOWER_NODE_LIMIT)*(1-o.COOLING_ADAPTATION_FACTOR))}else{this.coolingFactor=1}this.initialCoolingFactor=this.coolingFactor;this.maxNodeDisplacement=o.MAX_NODE_DISPLACEMENT}this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations);this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length;this.repulsionRange=this.calcRepulsionRange()};l.prototype.calcSpringForces=function(){var t=this.getAllEdges();var e;for(var r=0;r<t.length;r++){e=t[r];this.calcSpringForce(e,e.idealLength)}};l.prototype.calcRepulsionForces=function(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;var e=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;var r,i;var n,a;var s=this.getAllNodes();var h;if(this.useFRGridVariant){if(this.totalIterations%o.GRID_CALCULATION_CHECK_PERIOD==1&&t){this.updateGrid()}h=new Set;for(r=0;r<s.length;r++){n=s[r];this.calculateRepulsionForceOfANode(n,h,t,e);h.add(n)}}else{for(r=0;r<s.length;r++){n=s[r];for(i=r+1;i<s.length;i++){a=s[i];if(n.getOwner()!=a.getOwner()){continue}this.calcRepulsionForce(n,a)}}}};l.prototype.calcGravitationalForces=function(){var t;var e=this.getAllNodesToApplyGravitation();for(var r=0;r<e.length;r++){t=e[r];this.calcGravitationalForce(t)}};l.prototype.moveNodes=function(){var t=this.getAllNodes();var e;for(var r=0;r<t.length;r++){e=t[r];e.move()}};l.prototype.calcSpringForce=function(t,e){var r=t.getSource();var i=t.getTarget();var n;var o;var a;var s;if(this.uniformLeafNodeSizes&&r.getChild()==null&&i.getChild()==null){t.updateLengthSimple()}else{t.updateLength();if(t.isOverlapingSourceAndTarget){return}}n=t.getLength();if(n==0)return;o=this.springConstant*(n-e);a=o*(t.lengthX/n);s=o*(t.lengthY/n);r.springForceX+=a;r.springForceY+=s;i.springForceX-=a;i.springForceY-=s};l.prototype.calcRepulsionForce=function(t,e){var r=t.getRect();var i=e.getRect();var n=new Array(2);var a=new Array(4);var l;var c;var u;var g;var d;var p;var f;if(r.intersects(i)){s.calcSeparationAmount(r,i,n,o.DEFAULT_EDGE_LENGTH/2);p=2*n[0];f=2*n[1];var v=t.noOfChildren*e.noOfChildren/(t.noOfChildren+e.noOfChildren);t.repulsionForceX-=v*p;t.repulsionForceY-=v*f;e.repulsionForceX+=v*p;e.repulsionForceY+=v*f}else{if(this.uniformLeafNodeSizes&&t.getChild()==null&&e.getChild()==null){l=i.getCenterX()-r.getCenterX();c=i.getCenterY()-r.getCenterY()}else{s.getIntersection(r,i,a);l=a[2]-a[0];c=a[3]-a[1]}if(Math.abs(l)<o.MIN_REPULSION_DIST){l=h.sign(l)*o.MIN_REPULSION_DIST}if(Math.abs(c)<o.MIN_REPULSION_DIST){c=h.sign(c)*o.MIN_REPULSION_DIST}u=l*l+c*c;g=Math.sqrt(u);d=this.repulsionConstant*t.noOfChildren*e.noOfChildren/u;p=d*l/g;f=d*c/g;t.repulsionForceX-=p;t.repulsionForceY-=f;e.repulsionForceX+=p;e.repulsionForceY+=f}};l.prototype.calcGravitationalForce=function(t){var e;var r;var i;var n;var o;var a;var s;var h;e=t.getOwner();r=(e.getRight()+e.getLeft())/2;i=(e.getTop()+e.getBottom())/2;n=t.getCenterX()-r;o=t.getCenterY()-i;a=Math.abs(n)+t.getWidth()/2;s=Math.abs(o)+t.getHeight()/2;if(t.getOwner()==this.graphManager.getRoot()){h=e.getEstimatedSize()*this.gravityRangeFactor;if(a>h||s>h){t.gravitationForceX=-this.gravityConstant*n;t.gravitationForceY=-this.gravityConstant*o}}else{h=e.getEstimatedSize()*this.compoundGravityRangeFactor;if(a>h||s>h){t.gravitationForceX=-this.gravityConstant*n*this.compoundGravityConstant;t.gravitationForceY=-this.gravityConstant*o*this.compoundGravityConstant}}};l.prototype.isConverged=function(){var t;var e=false;if(this.totalIterations>this.maxIterations/3){e=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2}t=this.totalDisplacement<this.totalDisplacementThreshold;this.oldTotalDisplacement=this.totalDisplacement;return t||e};l.prototype.animate=function(){if(this.animationDuringLayout&&!this.isSubLayout){if(this.notAnimatedIterations==this.animationPeriod){this.update();this.notAnimatedIterations=0}else{this.notAnimatedIterations++}}};l.prototype.calcNoOfChildrenForAllNodes=function(){var t;var e=this.graphManager.getAllNodes();for(var r=0;r<e.length;r++){t=e[r];t.noOfChildren=t.getNoOfChildren()}};l.prototype.calcGrid=function(t){var e=0;var r=0;e=parseInt(Math.ceil((t.getRight()-t.getLeft())/this.repulsionRange));r=parseInt(Math.ceil((t.getBottom()-t.getTop())/this.repulsionRange));var i=new Array(e);for(var n=0;n<e;n++){i[n]=new Array(r)}for(var n=0;n<e;n++){for(var o=0;o<r;o++){i[n][o]=new Array}}return i};l.prototype.addNodeToGrid=function(t,e,r){var i=0;var n=0;var o=0;var a=0;i=parseInt(Math.floor((t.getRect().x-e)/this.repulsionRange));n=parseInt(Math.floor((t.getRect().width+t.getRect().x-e)/this.repulsionRange));o=parseInt(Math.floor((t.getRect().y-r)/this.repulsionRange));a=parseInt(Math.floor((t.getRect().height+t.getRect().y-r)/this.repulsionRange));for(var s=i;s<=n;s++){for(var h=o;h<=a;h++){this.grid[s][h].push(t);t.setGridCoordinates(i,n,o,a)}}};l.prototype.updateGrid=function(){var t;var e;var r=this.getAllNodes();this.grid=this.calcGrid(this.graphManager.getRoot());for(t=0;t<r.length;t++){e=r[t];this.addNodeToGrid(e,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())}};l.prototype.calculateRepulsionForceOfANode=function(t,e,r,n){if(this.totalIterations%o.GRID_CALCULATION_CHECK_PERIOD==1&&r||n){var a=new Set;t.surrounding=new Array;var s;var h=this.grid;for(var l=t.startX-1;l<t.finishX+2;l++){for(var c=t.startY-1;c<t.finishY+2;c++){if(!(l<0||c<0||l>=h.length||c>=h[0].length)){for(var u=0;u<h[l][c].length;u++){s=h[l][c][u];if(t.getOwner()!=s.getOwner()||t==s){continue}if(!e.has(s)&&!a.has(s)){var g=Math.abs(t.getCenterX()-s.getCenterX())-(t.getWidth()/2+s.getWidth()/2);var d=Math.abs(t.getCenterY()-s.getCenterY())-(t.getHeight()/2+s.getHeight()/2);if(g<=this.repulsionRange&&d<=this.repulsionRange){a.add(s)}}}}}}t.surrounding=[].concat(i(a))}for(l=0;l<t.surrounding.length;l++){this.calcRepulsionForce(t,t.surrounding[l])}};l.prototype.calcRepulsionRange=function(){return 0};t.exports=l},function(t,e,r){"use strict";var i=r(1);var n=r(7);function o(t,e,r){i.call(this,t,e,r);this.idealLength=n.DEFAULT_EDGE_LENGTH}o.prototype=Object.create(i.prototype);for(var a in i){o[a]=i[a]}t.exports=o},function(t,e,r){"use strict";var i=r(3);function n(t,e,r,n){i.call(this,t,e,r,n);this.springForceX=0;this.springForceY=0;this.repulsionForceX=0;this.repulsionForceY=0;this.gravitationForceX=0;this.gravitationForceY=0;this.displacementX=0;this.displacementY=0;this.startX=0;this.finishX=0;this.startY=0;this.finishY=0;this.surrounding=[]}n.prototype=Object.create(i.prototype);for(var o in i){n[o]=i[o]}n.prototype.setGridCoordinates=function(t,e,r,i){this.startX=t;this.finishX=e;this.startY=r;this.finishY=i};t.exports=n},function(t,e,r){"use strict";function i(t,e){this.width=0;this.height=0;if(t!==null&&e!==null){this.height=e;this.width=t}}i.prototype.getWidth=function(){return this.width};i.prototype.setWidth=function(t){this.width=t};i.prototype.getHeight=function(){return this.height};i.prototype.setHeight=function(t){this.height=t};t.exports=i},function(t,e,r){"use strict";var i=r(14);function n(){this.map={};this.keys=[]}n.prototype.put=function(t,e){var r=i.createID(t);if(!this.contains(r)){this.map[r]=e;this.keys.push(t)}};n.prototype.contains=function(t){var e=i.createID(t);return this.map[t]!=null};n.prototype.get=function(t){var e=i.createID(t);return this.map[e]};n.prototype.keySet=function(){return this.keys};t.exports=n},function(t,e,r){"use strict";var i=r(14);function n(){this.set={}}n.prototype.add=function(t){var e=i.createID(t);if(!this.contains(e))this.set[e]=t};n.prototype.remove=function(t){delete this.set[i.createID(t)]};n.prototype.clear=function(){this.set={}};n.prototype.contains=function(t){return this.set[i.createID(t)]==t};n.prototype.isEmpty=function(){return this.size()===0};n.prototype.size=function(){return Object.keys(this.set).length};n.prototype.addAllTo=function(t){var e=Object.keys(this.set);var r=e.length;for(var i=0;i<r;i++){t.push(this.set[e[i]])}};n.prototype.size=function(){return Object.keys(this.set).length};n.prototype.addAll=function(t){var e=t.length;for(var r=0;r<e;r++){var i=t[r];this.add(i)}};t.exports=n},function(t,e,r){"use strict";var i=function(){function t(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(t,i.key,i)}}return function(e,r,i){if(r)t(e.prototype,r);if(i)t(e,i);return e}}();function n(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}var o=r(11);var a=function(){function t(e,r){n(this,t);if(r!==null||r!==undefined)this.compareFunction=this._defaultCompareFunction;var i=void 0;if(e instanceof o)i=e.size();else i=e.length;this._quicksort(e,0,i-1)}i(t,[{key:"_quicksort",value:function t(e,r,i){if(r<i){var n=this._partition(e,r,i);this._quicksort(e,r,n);this._quicksort(e,n+1,i)}}},{key:"_partition",value:function t(e,r,i){var n=this._get(e,r);var o=r;var a=i;while(true){while(this.compareFunction(n,this._get(e,a))){a--}while(this.compareFunction(this._get(e,o),n)){o++}if(o<a){this._swap(e,o,a);o++;a--}else return a}}},{key:"_get",value:function t(e,r){if(e instanceof o)return e.get_object_at(r);else return e[r]}},{key:"_set",value:function t(e,r,i){if(e instanceof o)e.set_object_at(r,i);else e[r]=i}},{key:"_swap",value:function t(e,r,i){var n=this._get(e,r);this._set(e,r,this._get(e,i));this._set(e,i,n)}},{key:"_defaultCompareFunction",value:function t(e,r){return r>e}}]);return t}();t.exports=a},function(t,e,r){"use strict";var i=function(){function t(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(t,i.key,i)}}return function(e,r,i){if(r)t(e.prototype,r);if(i)t(e,i);return e}}();function n(t,e){if(!(t instanceof e)){throw new TypeError("Cannot call a class as a function")}}var o=function(){function t(e,r){var i=arguments.length>2&&arguments[2]!==undefined?arguments[2]:1;var o=arguments.length>3&&arguments[3]!==undefined?arguments[3]:-1;var a=arguments.length>4&&arguments[4]!==undefined?arguments[4]:-1;n(this,t);this.sequence1=e;this.sequence2=r;this.match_score=i;this.mismatch_penalty=o;this.gap_penalty=a;this.iMax=e.length+1;this.jMax=r.length+1;this.grid=new Array(this.iMax);for(var s=0;s<this.iMax;s++){this.grid[s]=new Array(this.jMax);for(var h=0;h<this.jMax;h++){this.grid[s][h]=0}}this.tracebackGrid=new Array(this.iMax);for(var l=0;l<this.iMax;l++){this.tracebackGrid[l]=new Array(this.jMax);for(var c=0;c<this.jMax;c++){this.tracebackGrid[l][c]=[null,null,null]}}this.alignments=[];this.score=-1;this.computeGrids()}i(t,[{key:"getScore",value:function t(){return this.score}},{key:"getAlignments",value:function t(){return this.alignments}},{key:"computeGrids",value:function t(){for(var e=1;e<this.jMax;e++){this.grid[0][e]=this.grid[0][e-1]+this.gap_penalty;this.tracebackGrid[0][e]=[false,false,true]}for(var r=1;r<this.iMax;r++){this.grid[r][0]=this.grid[r-1][0]+this.gap_penalty;this.tracebackGrid[r][0]=[false,true,false]}for(var i=1;i<this.iMax;i++){for(var n=1;n<this.jMax;n++){var o=void 0;if(this.sequence1[i-1]===this.sequence2[n-1])o=this.grid[i-1][n-1]+this.match_score;else o=this.grid[i-1][n-1]+this.mismatch_penalty;var a=this.grid[i-1][n]+this.gap_penalty;var s=this.grid[i][n-1]+this.gap_penalty;var h=[o,a,s];var l=this.arrayAllMaxIndexes(h);this.grid[i][n]=h[l[0]];this.tracebackGrid[i][n]=[l.includes(0),l.includes(1),l.includes(2)]}}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function t(){var e=[];e.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});while(e[0]){var r=e[0];var i=this.tracebackGrid[r.pos[0]][r.pos[1]];if(i[0]){e.push({pos:[r.pos[0]-1,r.pos[1]-1],seq1:this.sequence1[r.pos[0]-1]+r.seq1,seq2:this.sequence2[r.pos[1]-1]+r.seq2})}if(i[1]){e.push({pos:[r.pos[0]-1,r.pos[1]],seq1:this.sequence1[r.pos[0]-1]+r.seq1,seq2:"-"+r.seq2})}if(i[2]){e.push({pos:[r.pos[0],r.pos[1]-1],seq1:"-"+r.seq1,seq2:this.sequence2[r.pos[1]-1]+r.seq2})}if(r.pos[0]===0&&r.pos[1]===0)this.alignments.push({sequence1:r.seq1,sequence2:r.seq2});e.shift()}return this.alignments}},{key:"getAllIndexes",value:function t(e,r){var i=[],n=-1;while((n=e.indexOf(r,n+1))!==-1){i.push(n)}return i}},{key:"arrayAllMaxIndexes",value:function t(e){return this.getAllIndexes(e,Math.max.apply(null,e))}}]);return t}();t.exports=o},function(t,e,r){"use strict";var i=function t(){return};i.FDLayout=r(18);i.FDLayoutConstants=r(7);i.FDLayoutEdge=r(19);i.FDLayoutNode=r(20);i.DimensionD=r(21);i.HashMap=r(22);i.HashSet=r(23);i.IGeometry=r(8);i.IMath=r(9);i.Integer=r(10);i.Point=r(12);i.PointD=r(4);i.RandomSeed=r(16);i.RectangleD=r(13);i.Transform=r(17);i.UniqueIDGeneretor=r(14);i.Quicksort=r(24);i.LinkedList=r(11);i.LGraphObject=r(2);i.LGraph=r(5);i.LEdge=r(1);i.LGraphManager=r(6);i.LNode=r(3);i.Layout=r(15);i.LayoutConstants=r(0);i.NeedlemanWunsch=r(25);t.exports=i},function(t,e,r){"use strict";function i(){this.listeners=[]}var n=i.prototype;n.addListener=function(t,e){this.listeners.push({event:t,callback:e})};n.removeListener=function(t,e){for(var r=this.listeners.length;r>=0;r--){var i=this.listeners[r];if(i.event===t&&i.callback===e){this.listeners.splice(r,1)}}};n.emit=function(t,e){for(var r=0;r<this.listeners.length;r++){var i=this.listeners[r];if(t===i.event){i.callback(e)}}};t.exports=i}])}))},18915:(t,e,r)=>{"use strict";r.d(e,{diagram:()=>J});var i=r(76261);var n=r(96049);var o=r(93113);var a=r(75905);var s=r(76405);var h=r(43457);var l=r.n(h);var c=r(24982);var u=r(63170);var g=r(77470);var d=r(48750);var p=function(){var t=(0,a.K2)((function(t,e,r,i){for(r=r||{},i=t.length;i--;r[t[i]]=e);return r}),"o"),e=[1,4],r=[1,13],i=[1,12],n=[1,15],o=[1,16],s=[1,20],h=[1,19],l=[6,7,8],c=[1,26],u=[1,24],g=[1,25],d=[6,7,11],p=[1,6,13,15,16,19,22],f=[1,33],v=[1,34],y=[1,6,7,11,13,15,16,19,22];var E={trace:(0,a.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:(0,a.K2)((function t(e,r,i,n,o,a,s){var h=a.length-1;switch(o){case 6:case 7:return n;break;case 8:n.getLogger().trace("Stop NL ");break;case 9:n.getLogger().trace("Stop EOF ");break;case 11:n.getLogger().trace("Stop NL2 ");break;case 12:n.getLogger().trace("Stop EOF2 ");break;case 15:n.getLogger().info("Node: ",a[h].id);n.addNode(a[h-1].length,a[h].id,a[h].descr,a[h].type);break;case 16:n.getLogger().trace("Icon: ",a[h]);n.decorateNode({icon:a[h]});break;case 17:case 21:n.decorateNode({class:a[h]});break;case 18:n.getLogger().trace("SPACELIST");break;case 19:n.getLogger().trace("Node: ",a[h].id);n.addNode(0,a[h].id,a[h].descr,a[h].type);break;case 20:n.decorateNode({icon:a[h]});break;case 25:n.getLogger().trace("node found ..",a[h-2]);this.$={id:a[h-1],descr:a[h-1],type:n.getType(a[h-2],a[h])};break;case 26:this.$={id:a[h],descr:a[h],type:n.nodeType.DEFAULT};break;case 27:n.getLogger().trace("node found ..",a[h-3]);this.$={id:a[h-3],descr:a[h-1],type:n.getType(a[h-2],a[h])};break}}),"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:e},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:e},{6:r,7:[1,10],9:9,12:11,13:i,14:14,15:n,16:o,17:17,18:18,19:s,22:h},t(l,[2,3]),{1:[2,2]},t(l,[2,4]),t(l,[2,5]),{1:[2,6],6:r,12:21,13:i,14:14,15:n,16:o,17:17,18:18,19:s,22:h},{6:r,9:22,12:11,13:i,14:14,15:n,16:o,17:17,18:18,19:s,22:h},{6:c,7:u,10:23,11:g},t(d,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:s,22:h}),t(d,[2,18]),t(d,[2,19]),t(d,[2,20]),t(d,[2,21]),t(d,[2,23]),t(d,[2,24]),t(d,[2,26],{19:[1,30]}),{20:[1,31]},{6:c,7:u,10:32,11:g},{1:[2,7],6:r,12:21,13:i,14:14,15:n,16:o,17:17,18:18,19:s,22:h},t(p,[2,14],{7:f,11:v}),t(y,[2,8]),t(y,[2,9]),t(y,[2,10]),t(d,[2,15]),t(d,[2,16]),t(d,[2,17]),{20:[1,35]},{21:[1,36]},t(p,[2,13],{7:f,11:v}),t(y,[2,11]),t(y,[2,12]),{21:[1,37]},t(d,[2,25]),t(d,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:(0,a.K2)((function t(e,r){if(r.recoverable){this.trace(e)}else{var i=new Error(e);i.hash=r;throw i}}),"parseError"),parse:(0,a.K2)((function t(e){var r=this,i=[0],n=[],o=[null],s=[],h=this.table,l="",c=0,u=0,g=0,d=2,p=1;var f=s.slice.call(arguments,1);var v=Object.create(this.lexer);var y={yy:{}};for(var E in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,E)){y.yy[E]=this.yy[E]}}v.setInput(e,y.yy);y.yy.lexer=v;y.yy.parser=this;if(typeof v.yylloc=="undefined"){v.yylloc={}}var _=v.yylloc;s.push(_);var m=v.options&&v.options.ranges;if(typeof y.yy.parseError==="function"){this.parseError=y.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function N(t){i.length=i.length-2*t;o.length=o.length-t;s.length=s.length-t}(0,a.K2)(N,"popStack");function A(){var t;t=n.pop()||v.lex()||p;if(typeof t!=="number"){if(t instanceof Array){n=t;t=n.pop()}t=r.symbols_[t]||t}return t}(0,a.K2)(A,"lex");var L,T,O,D,I,w,R={},C,M,x,b;while(true){O=i[i.length-1];if(this.defaultActions[O]){D=this.defaultActions[O]}else{if(L===null||typeof L=="undefined"){L=A()}D=h[O]&&h[O][L]}if(typeof D==="undefined"||!D.length||!D[0]){var G="";b=[];for(C in h[O]){if(this.terminals_[C]&&C>d){b.push("'"+this.terminals_[C]+"'")}}if(v.showPosition){G="Parse error on line "+(c+1)+":\n"+v.showPosition()+"\nExpecting "+b.join(", ")+", got '"+(this.terminals_[L]||L)+"'"}else{G="Parse error on line "+(c+1)+": Unexpected "+(L==p?"end of input":"'"+(this.terminals_[L]||L)+"'")}this.parseError(G,{text:v.match,token:this.terminals_[L]||L,line:v.yylineno,loc:_,expected:b})}if(D[0]instanceof Array&&D.length>1){throw new Error("Parse Error: multiple actions possible at state: "+O+", token: "+L)}switch(D[0]){case 1:i.push(L);o.push(v.yytext);s.push(v.yylloc);i.push(D[1]);L=null;if(!T){u=v.yyleng;l=v.yytext;c=v.yylineno;_=v.yylloc;if(g>0){g--}}else{L=T;T=null}break;case 2:M=this.productions_[D[1]][1];R.$=o[o.length-M];R._$={first_line:s[s.length-(M||1)].first_line,last_line:s[s.length-1].last_line,first_column:s[s.length-(M||1)].first_column,last_column:s[s.length-1].last_column};if(m){R._$.range=[s[s.length-(M||1)].range[0],s[s.length-1].range[1]]}w=this.performAction.apply(R,[l,u,c,y.yy,D[1],o,s].concat(f));if(typeof w!=="undefined"){return w}if(M){i=i.slice(0,-1*M*2);o=o.slice(0,-1*M);s=s.slice(0,-1*M)}i.push(this.productions_[D[1]][0]);o.push(R.$);s.push(R._$);x=h[i[i.length-2]][i[i.length-1]];i.push(x);break;case 3:return true}}return true}),"parse")};var _=function(){var t={EOF:1,parseError:(0,a.K2)((function t(e,r){if(this.yy.parser){this.yy.parser.parseError(e,r)}else{throw new Error(e)}}),"parseError"),setInput:(0,a.K2)((function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,a.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,a.K2)((function(t){var e=t.length;var r=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(r.length-1){this.yylineno-=r.length-1}var n=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:r?(r.length===i.length?this.yylloc.first_column:0)+i[i.length-r.length].length-r[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[n[0],n[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,a.K2)((function(){this._more=true;return this}),"more"),reject:(0,a.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,a.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,a.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,a.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,a.K2)((function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,a.K2)((function(t,e){var r,i,n;if(this.options.backtrack_lexer){n={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){n.yylloc.range=this.yylloc.range.slice(0)}}i=t[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];r=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(r){return r}else if(this._backtrack){for(var o in n){this[o]=n[o]}return false}return false}),"test_match"),next:(0,a.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,r,i;if(!this._more){this.yytext="";this.match=""}var n=this._currentRules();for(var o=0;o<n.length;o++){r=this._input.match(this.rules[n[o]]);if(r&&(!e||r[0].length>e[0].length)){e=r;i=o;if(this.options.backtrack_lexer){t=this.test_match(r,n[o]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,n[i]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,a.K2)((function t(){var e=this.next();if(e){return e}else{return this.lex()}}),"lex"),begin:(0,a.K2)((function t(e){this.conditionStack.push(e)}),"begin"),popState:(0,a.K2)((function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,a.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,a.K2)((function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}}),"topState"),pushState:(0,a.K2)((function t(e){this.begin(e)}),"pushState"),stateStackSize:(0,a.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,a.K2)((function t(e,r,i,n){var o=n;switch(i){case 0:e.getLogger().trace("Found comment",r.yytext);return 6;break;case 1:return 8;break;case 2:this.begin("CLASS");break;case 3:this.popState();return 16;break;case 4:this.popState();break;case 5:e.getLogger().trace("Begin icon");this.begin("ICON");break;case 6:e.getLogger().trace("SPACELINE");return 6;break;case 7:return 7;break;case 8:return 15;break;case 9:e.getLogger().trace("end icon");this.popState();break;case 10:e.getLogger().trace("Exploding node");this.begin("NODE");return 19;break;case 11:e.getLogger().trace("Cloud");this.begin("NODE");return 19;break;case 12:e.getLogger().trace("Explosion Bang");this.begin("NODE");return 19;break;case 13:e.getLogger().trace("Cloud Bang");this.begin("NODE");return 19;break;case 14:this.begin("NODE");return 19;break;case 15:this.begin("NODE");return 19;break;case 16:this.begin("NODE");return 19;break;case 17:this.begin("NODE");return 19;break;case 18:return 13;break;case 19:return 22;break;case 20:return 11;break;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";break;case 23:this.popState();break;case 24:e.getLogger().trace("Starting NSTR");this.begin("NSTR");break;case 25:e.getLogger().trace("description:",r.yytext);return"NODE_DESCR";break;case 26:this.popState();break;case 27:this.popState();e.getLogger().trace("node end ))");return"NODE_DEND";break;case 28:this.popState();e.getLogger().trace("node end )");return"NODE_DEND";break;case 29:this.popState();e.getLogger().trace("node end ...",r.yytext);return"NODE_DEND";break;case 30:this.popState();e.getLogger().trace("node end ((");return"NODE_DEND";break;case 31:this.popState();e.getLogger().trace("node end (-");return"NODE_DEND";break;case 32:this.popState();e.getLogger().trace("node end (-");return"NODE_DEND";break;case 33:this.popState();e.getLogger().trace("node end ((");return"NODE_DEND";break;case 34:this.popState();e.getLogger().trace("node end ((");return"NODE_DEND";break;case 35:e.getLogger().trace("Long description:",r.yytext);return 20;break;case 36:e.getLogger().trace("Long description:",r.yytext);return 20;break}}),"anonymous"),rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:false},ICON:{rules:[8,9],inclusive:false},NSTR2:{rules:[22,23],inclusive:false},NSTR:{rules:[25,26],inclusive:false},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:false},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:true}}};return t}();E.lexer=_;function m(){this.yy={}}(0,a.K2)(m,"Parser");m.prototype=E;E.Parser=m;return new m}();p.parser=p;var f=p;var v=[];var y=0;var E={};var _=(0,a.K2)((()=>{v=[];y=0;E={}}),"clear");var m=(0,a.K2)((function(t){for(let e=v.length-1;e>=0;e--){if(v[e].level<t){return v[e]}}return null}),"getParent");var N=(0,a.K2)((()=>v.length>0?v[0]:null),"getMindmap");var A=(0,a.K2)(((t,e,r,i)=>{a.Rm.info("addNode",t,e,r,i);const n=(0,a.D7)();let o=n.mindmap?.padding??a.UI.mindmap.padding;switch(i){case L.ROUNDED_RECT:case L.RECT:case L.HEXAGON:o*=2}const s={id:y++,nodeId:(0,a.jZ)(e,n),level:t,descr:(0,a.jZ)(r,n),type:i,children:[],width:n.mindmap?.maxNodeWidth??a.UI.mindmap.maxNodeWidth,padding:o};const h=m(t);if(h){h.children.push(s);v.push(s)}else{if(v.length===0){v.push(s)}else{throw new Error('There can be only one root. No parent could be found for ("'+s.descr+'")')}}}),"addNode");var L={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6};var T=(0,a.K2)(((t,e)=>{a.Rm.debug("In get type",t,e);switch(t){case"[":return L.RECT;case"(":return e===")"?L.ROUNDED_RECT:L.CLOUD;case"((":return L.CIRCLE;case")":return L.CLOUD;case"))":return L.BANG;case"{{":return L.HEXAGON;default:return L.DEFAULT}}),"getType");var O=(0,a.K2)(((t,e)=>{E[t]=e}),"setElementForId");var D=(0,a.K2)((t=>{if(!t){return}const e=(0,a.D7)();const r=v[v.length-1];if(t.icon){r.icon=(0,a.jZ)(t.icon,e)}if(t.class){r.class=(0,a.jZ)(t.class,e)}}),"decorateNode");var I=(0,a.K2)((t=>{switch(t){case L.DEFAULT:return"no-border";case L.RECT:return"rect";case L.ROUNDED_RECT:return"rounded-rect";case L.CIRCLE:return"circle";case L.CLOUD:return"cloud";case L.BANG:return"bang";case L.HEXAGON:return"hexgon";default:return"no-border"}}),"type2Str");var w=(0,a.K2)((()=>a.Rm),"getLogger");var R=(0,a.K2)((t=>E[t]),"getElementById");var C={clear:_,addNode:A,getMindmap:N,nodeType:L,getType:T,setElementForId:O,decorateNode:D,type2Str:I,getLogger:w,getElementById:R};var M=C;var x=12;var b=(0,a.K2)((function(t,e,r,i){const n=5;e.append("path").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("d",`M0 ${r.height-n} v${-r.height+2*n} q0,-5 5,-5 h${r.width-2*n} q5,0 5,5 v${r.height-n} H0 Z`);e.append("line").attr("class","node-line-"+i).attr("x1",0).attr("y1",r.height).attr("x2",r.width).attr("y2",r.height)}),"defaultBkg");var G=(0,a.K2)((function(t,e,r){e.append("rect").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("height",r.height).attr("width",r.width)}),"rectBkg");var S=(0,a.K2)((function(t,e,r){const i=r.width;const n=r.height;const o=.15*i;const a=.25*i;const s=.35*i;const h=.2*i;e.append("path").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("d",`M0 0 a${o},${o} 0 0,1 ${i*.25},${-1*i*.1}\n      a${s},${s} 1 0,1 ${i*.4},${-1*i*.1}\n      a${a},${a} 1 0,1 ${i*.35},${1*i*.2}\n\n      a${o},${o} 1 0,1 ${i*.15},${1*n*.35}\n      a${h},${h} 1 0,1 ${-1*i*.15},${1*n*.65}\n\n      a${a},${o} 1 0,1 ${-1*i*.25},${i*.15}\n      a${s},${s} 1 0,1 ${-1*i*.5},${0}\n      a${o},${o} 1 0,1 ${-1*i*.25},${-1*i*.15}\n\n      a${o},${o} 1 0,1 ${-1*i*.1},${-1*n*.35}\n      a${h},${h} 1 0,1 ${i*.1},${-1*n*.65}\n\n    H0 V0 Z`)}),"cloudBkg");var F=(0,a.K2)((function(t,e,r){const i=r.width;const n=r.height;const o=.15*i;e.append("path").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("d",`M0 0 a${o},${o} 1 0,0 ${i*.25},${-1*n*.1}\n      a${o},${o} 1 0,0 ${i*.25},${0}\n      a${o},${o} 1 0,0 ${i*.25},${0}\n      a${o},${o} 1 0,0 ${i*.25},${1*n*.1}\n\n      a${o},${o} 1 0,0 ${i*.15},${1*n*.33}\n      a${o*.8},${o*.8} 1 0,0 ${0},${1*n*.34}\n      a${o},${o} 1 0,0 ${-1*i*.15},${1*n*.33}\n\n      a${o},${o} 1 0,0 ${-1*i*.25},${n*.15}\n      a${o},${o} 1 0,0 ${-1*i*.25},${0}\n      a${o},${o} 1 0,0 ${-1*i*.25},${0}\n      a${o},${o} 1 0,0 ${-1*i*.25},${-1*n*.15}\n\n      a${o},${o} 1 0,0 ${-1*i*.1},${-1*n*.33}\n      a${o*.8},${o*.8} 1 0,0 ${0},${-1*n*.34}\n      a${o},${o} 1 0,0 ${i*.1},${-1*n*.33}\n\n    H0 V0 Z`)}),"bangBkg");var P=(0,a.K2)((function(t,e,r){e.append("circle").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("r",r.width/2)}),"circleBkg");function k(t,e,r,i,n){return t.insert("polygon",":first-child").attr("points",i.map((function(t){return t.x+","+t.y})).join(" ")).attr("transform","translate("+(n.width-e)/2+", "+r+")")}(0,a.K2)(k,"insertPolygonShape");var U=(0,a.K2)((function(t,e,r){const i=r.height;const n=4;const o=i/n;const a=r.width-r.padding+2*o;const s=[{x:o,y:0},{x:a-o,y:0},{x:a,y:-i/2},{x:a-o,y:-i},{x:o,y:-i},{x:0,y:-i/2}];k(e,a,i,s,r)}),"hexagonBkg");var Y=(0,a.K2)((function(t,e,r){e.append("rect").attr("id","node-"+r.id).attr("class","node-bkg node-"+t.type2Str(r.type)).attr("height",r.height).attr("rx",r.padding).attr("ry",r.padding).attr("width",r.width)}),"roundedRectBkg");var X=(0,a.K2)((async function(t,e,r,o,a){const s=a.htmlLabels;const h=o%(x-1);const l=e.append("g");r.section=h;let c="section-"+h;if(h<0){c+=" section-root"}l.attr("class",(r.class?r.class+" ":"")+"mindmap-node "+c);const u=l.append("g");const g=l.append("g");const d=r.descr.replace(/(<br\/*>)/g,"\n");await(0,i.GZ)(g,d,{useHtmlLabels:s,width:r.width,classes:"mindmap-node-label"},a);if(!s){g.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle")}const p=g.node().getBBox();const[f]=(0,n.I5)(a.fontSize);r.height=p.height+f*1.1*.5+r.padding;r.width=p.width+2*r.padding;if(r.icon){if(r.type===t.nodeType.CIRCLE){r.height+=50;r.width+=50;const t=l.append("foreignObject").attr("height","50px").attr("width",r.width).attr("style","text-align: center;");t.append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+h+" "+r.icon);g.attr("transform","translate("+r.width/2+", "+(r.height/2-1.5*r.padding)+")")}else{r.width+=50;const t=r.height;r.height=Math.max(t,60);const e=Math.abs(r.height-t);const i=l.append("foreignObject").attr("width","60px").attr("height",r.height).attr("style","text-align: center;margin-top:"+e/2+"px;");i.append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+h+" "+r.icon);g.attr("transform","translate("+(25+r.width/2)+", "+(e/2+r.padding/2)+")")}}else{if(!s){const t=r.width/2;const e=r.padding/2;g.attr("transform","translate("+t+", "+e+")")}else{const t=(r.width-p.width)/2;const e=(r.height-p.height)/2;g.attr("transform","translate("+t+", "+e+")")}}switch(r.type){case t.nodeType.DEFAULT:b(t,u,r,h);break;case t.nodeType.ROUNDED_RECT:Y(t,u,r,h);break;case t.nodeType.RECT:G(t,u,r,h);break;case t.nodeType.CIRCLE:u.attr("transform","translate("+r.width/2+", "+ +r.height/2+")");P(t,u,r,h);break;case t.nodeType.CLOUD:S(t,u,r,h);break;case t.nodeType.BANG:F(t,u,r,h);break;case t.nodeType.HEXAGON:U(t,u,r,h);break}t.setElementForId(r.id,l);return r.height}),"drawNode");var $=(0,a.K2)((function(t,e){const r=t.getElementById(e.id);const i=e.x||0;const n=e.y||0;r.attr("transform","translate("+i+","+n+")")}),"positionNode");s.A.use(l());async function B(t,e,r,i,n){await X(t,e,r,i,n);if(r.children){await Promise.all(r.children.map(((r,o)=>B(t,e,r,i<0?o:i,n))))}}(0,a.K2)(B,"drawNodes");function H(t,e){e.edges().map(((e,r)=>{const i=e.data();if(e[0]._private.bodyBounds){const n=e[0]._private.rscratch;a.Rm.trace("Edge: ",r,i);t.insert("path").attr("d",`M ${n.startX},${n.startY} L ${n.midX},${n.midY} L${n.endX},${n.endY} `).attr("class","edge section-edge-"+i.section+" edge-depth-"+i.depth)}}))}(0,a.K2)(H,"drawEdges");function W(t,e,r,i){e.add({group:"nodes",data:{id:t.id.toString(),labelText:t.descr,height:t.height,width:t.width,level:i,nodeId:t.id,padding:t.padding,type:t.type},position:{x:t.x,y:t.y}});if(t.children){t.children.forEach((n=>{W(n,e,r,i+1);e.add({group:"edges",data:{id:`${t.id}_${n.id}`,source:t.id,target:n.id,depth:i,section:n.section}})}))}}(0,a.K2)(W,"addNodes");function j(t,e){return new Promise((r=>{const i=(0,c.Ltv)("body").append("div").attr("id","cy").attr("style","display:none");const n=(0,s.A)({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});i.remove();W(t,n,e,0);n.nodes().forEach((function(t){t.layoutDimensions=()=>{const e=t.data();return{w:e.width,h:e.height}}}));n.layout({name:"cose-bilkent",quality:"proof",styleEnabled:false,animate:false}).run();n.ready((t=>{a.Rm.info("Ready",t);r(n)}))}))}(0,a.K2)(j,"layoutMindmap");function V(t,e){e.nodes().map(((e,r)=>{const i=e.data();i.x=e.position().x;i.y=e.position().y;$(t,i);const n=t.getElementById(i.nodeId);a.Rm.info("Id:",r,"Position: (",e.position().x,", ",e.position().y,")",i);n.attr("transform",`translate(${e.position().x-i.width/2}, ${e.position().y-i.height/2})`);n.attr("attr",`apa-${r})`)}))}(0,a.K2)(V,"positionNodes");var K=(0,a.K2)((async(t,e,r,i)=>{a.Rm.debug("Rendering mindmap diagram\n"+t);const n=i.db;const s=n.getMindmap();if(!s){return}const h=(0,a.D7)();h.htmlLabels=false;const l=(0,o.D)(e);const c=l.append("g");c.attr("class","mindmap-edges");const u=l.append("g");u.attr("class","mindmap-nodes");await B(n,u,s,-1,h);const g=await j(s,h);H(c,g);V(n,g);(0,a.ot)(void 0,l,h.mindmap?.padding??a.UI.mindmap.padding,h.mindmap?.useMaxWidth??a.UI.mindmap.useMaxWidth)}),"draw");var z={draw:K};var q=(0,a.K2)((t=>{let e="";for(let r=0;r<t.THEME_COLOR_LIMIT;r++){t["lineColor"+r]=t["lineColor"+r]||t["cScaleInv"+r];if((0,u.A)(t["lineColor"+r])){t["lineColor"+r]=(0,g.A)(t["lineColor"+r],20)}else{t["lineColor"+r]=(0,d.A)(t["lineColor"+r],20)}}for(let r=0;r<t.THEME_COLOR_LIMIT;r++){const i=""+(17-3*r);e+=`\n    .section-${r-1} rect, .section-${r-1} path, .section-${r-1} circle, .section-${r-1} polygon, .section-${r-1} path  {\n      fill: ${t["cScale"+r]};\n    }\n    .section-${r-1} text {\n     fill: ${t["cScaleLabel"+r]};\n    }\n    .node-icon-${r-1} {\n      font-size: 40px;\n      color: ${t["cScaleLabel"+r]};\n    }\n    .section-edge-${r-1}{\n      stroke: ${t["cScale"+r]};\n    }\n    .edge-depth-${r-1}{\n      stroke-width: ${i};\n    }\n    .section-${r-1} line {\n      stroke: ${t["cScaleInv"+r]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `}return e}),"genSections");var Z=(0,a.K2)((t=>`\n  .edge {\n    stroke-width: 3;\n  }\n  ${q(t)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${t.git0};\n  }\n  .section-root text {\n    fill: ${t.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .mindmap-node-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n`),"getStyles");var Q=Z;var J={db:M,renderer:z,parser:f,styles:Q}}}]);