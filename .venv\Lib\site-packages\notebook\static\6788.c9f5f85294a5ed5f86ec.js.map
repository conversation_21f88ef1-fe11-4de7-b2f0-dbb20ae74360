{"version": 3, "file": "6788.c9f5f85294a5ed5f86ec.js?v=c9f5f85294a5ed5f86ec", "mappings": ";;;;;;;;;;AAAA;AACA,cAAc;AACd,gBAAgB,gBAAgB;AAChC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,8BAA8B,SAAS;AACvC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,oBAAoB,oBAAoB,yBAAyB;AACjE;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/modelica.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i=0; i<words.length; ++i)\n    obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = words(\"algorithm and annotation assert block break class connect connector constant constrainedby der discrete each else elseif elsewhen encapsulated end enumeration equation expandable extends external false final flow for function if import impure in initial inner input loop model not operator or outer output package parameter partial protected public pure record redeclare replaceable return stream then true type when while within\")\nvar builtin = words(\"abs acos actualStream asin atan atan2 cardinality ceil cos cosh delay div edge exp floor getInstanceName homotopy inStream integer log log10 mod pre reinit rem semiLinear sign sin sinh spatialDistribution sqrt tan tanh\")\nvar atoms = words(\"Real Boolean Integer String\")\n\nvar completions = [].concat(Object.keys(keywords), Object.keys(builtin), Object.keys(atoms))\n\nvar isSingleOperatorChar = /[;=\\(:\\),{}.*<>+\\-\\/^\\[\\]]/;\nvar isDoubleOperatorChar = /(:=|<=|>=|==|<>|\\.\\+|\\.\\-|\\.\\*|\\.\\/|\\.\\^)/;\nvar isDigit = /[0-9]/;\nvar isNonDigit = /[_a-zA-Z]/;\n\nfunction tokenLineComment(stream, state) {\n  stream.skipToEnd();\n  state.tokenize = null;\n  return \"comment\";\n}\n\nfunction tokenBlockComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(stream, state) {\n  var escaped = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == '\"' && !escaped) {\n      state.tokenize = null;\n      state.sol = false;\n      break;\n    }\n    escaped = !escaped && ch == \"\\\\\";\n  }\n\n  return \"string\";\n}\n\nfunction tokenIdent(stream, state) {\n  stream.eatWhile(isDigit);\n  while (stream.eat(isDigit) || stream.eat(isNonDigit)) { }\n\n\n  var cur = stream.current();\n\n  if(state.sol && (cur == \"package\" || cur == \"model\" || cur == \"when\" || cur == \"connector\")) state.level++;\n  else if(state.sol && cur == \"end\" && state.level > 0) state.level--;\n\n  state.tokenize = null;\n  state.sol = false;\n\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  else if (builtin.propertyIsEnumerable(cur)) return \"builtin\";\n  else if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  else return \"variable\";\n}\n\nfunction tokenQIdent(stream, state) {\n  while (stream.eat(/[^']/)) { }\n\n  state.tokenize = null;\n  state.sol = false;\n\n  if(stream.eat(\"'\"))\n    return \"variable\";\n  else\n    return \"error\";\n}\n\nfunction tokenUnsignedNumber(stream, state) {\n  stream.eatWhile(isDigit);\n  if (stream.eat('.')) {\n    stream.eatWhile(isDigit);\n  }\n  if (stream.eat('e') || stream.eat('E')) {\n    if (!stream.eat('-'))\n      stream.eat('+');\n    stream.eatWhile(isDigit);\n  }\n\n  state.tokenize = null;\n  state.sol = false;\n  return \"number\";\n}\n\n// Interface\nexport const modelica = {\n  name: \"modelica\",\n  startState: function() {\n    return {\n      tokenize: null,\n      level: 0,\n      sol: true\n    };\n  },\n\n  token: function(stream, state) {\n    if(state.tokenize != null) {\n      return state.tokenize(stream, state);\n    }\n\n    if(stream.sol()) {\n      state.sol = true;\n    }\n\n    // WHITESPACE\n    if(stream.eatSpace()) {\n      state.tokenize = null;\n      return null;\n    }\n\n    var ch = stream.next();\n\n    // LINECOMMENT\n    if(ch == '/' && stream.eat('/')) {\n      state.tokenize = tokenLineComment;\n    }\n    // BLOCKCOMMENT\n    else if(ch == '/' && stream.eat('*')) {\n      state.tokenize = tokenBlockComment;\n    }\n    // TWO SYMBOL TOKENS\n    else if(isDoubleOperatorChar.test(ch+stream.peek())) {\n      stream.next();\n      state.tokenize = null;\n      return \"operator\";\n    }\n    // SINGLE SYMBOL TOKENS\n    else if(isSingleOperatorChar.test(ch)) {\n      state.tokenize = null;\n      return \"operator\";\n    }\n    // IDENT\n    else if(isNonDigit.test(ch)) {\n      state.tokenize = tokenIdent;\n    }\n    // Q-IDENT\n    else if(ch == \"'\" && stream.peek() && stream.peek() != \"'\") {\n      state.tokenize = tokenQIdent;\n    }\n    // STRING\n    else if(ch == '\"') {\n      state.tokenize = tokenString;\n    }\n    // UNSIGNED_NUMBER\n    else if(isDigit.test(ch)) {\n      state.tokenize = tokenUnsignedNumber;\n    }\n    // ERROR\n    else {\n      state.tokenize = null;\n      return \"error\";\n    }\n\n    return state.tokenize(stream, state);\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != null) return null;\n\n    var level = state.level;\n    if(/(algorithm)/.test(textAfter)) level--;\n    if(/(equation)/.test(textAfter)) level--;\n    if(/(initial algorithm)/.test(textAfter)) level--;\n    if(/(initial equation)/.test(textAfter)) level--;\n    if(/(end)/.test(textAfter)) level--;\n\n    if(level > 0)\n      return cx.unit*level;\n    else\n      return 0;\n  },\n\n  languageData: {\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    autocomplete: completions\n  }\n};\n"], "names": [], "sourceRoot": ""}