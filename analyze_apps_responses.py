#!/usr/bin/env python3
"""
Analyze Digital Banking Apps and Developer Responses
"""

import pandas as pd
import numpy as np
from collections import Counter

def analyze_apps_and_responses():
    """Analyze app distribution and developer responses"""
    
    # Load the dataset
    print("📊 Loading dataset...")
    df = pd.read_csv('google_play_reviews_DigitalBank_20250703_205650.csv')
    
    print(f"✅ Dataset loaded: {len(df):,} reviews")
    
    # Analyze app distribution
    print("\n🏦 DIGITAL BANKING APPS ANALYSIS")
    print("=" * 50)
    
    app_counts = df['app_id'].value_counts()
    print(f"\n📱 Apps in dataset ({len(app_counts)} apps):")
    for app_id, count in app_counts.items():
        percentage = (count / len(df)) * 100
        print(f"   {app_id}: {count:,} reviews ({percentage:.1f}%)")
    
    # Analyze developer responses
    print(f"\n📞 DEVELOPER RESPONSE ANALYSIS")
    print("=" * 50)
    
    total_reviews = len(df)
    reviews_with_replies = df['replyContent'].notna().sum()
    reply_rate = (reviews_with_replies / total_reviews) * 100
    
    print(f"\n📊 Overall Response Statistics:")
    print(f"   Total reviews: {total_reviews:,}")
    print(f"   Reviews with replies: {reviews_with_replies:,}")
    print(f"   Overall reply rate: {reply_rate:.1f}%")
    
    # Response rate by app
    print(f"\n📱 Response Rate by App:")
    for app_id in app_counts.index:
        app_data = df[df['app_id'] == app_id]
        app_replies = app_data['replyContent'].notna().sum()
        app_reply_rate = (app_replies / len(app_data)) * 100
        print(f"   {app_id}: {app_reply_rate:.1f}% ({app_replies:,}/{len(app_data):,})")
    
    # Response rate by rating
    print(f"\n⭐ Response Rate by Rating:")
    for score in sorted(df['score'].unique()):
        score_data = df[df['score'] == score]
        score_replies = score_data['replyContent'].notna().sum()
        score_reply_rate = (score_replies / len(score_data)) * 100
        print(f"   {score} stars: {score_reply_rate:.1f}% ({score_replies:,}/{len(score_data):,})")
    
    # Response time analysis
    print(f"\n⏰ RESPONSE TIME ANALYSIS")
    print("=" * 50)
    
    # Convert timestamps
    df['at'] = pd.to_datetime(df['at'])
    df['repliedAt'] = pd.to_datetime(df['repliedAt'])
    
    # Calculate response time for reviews with replies
    replied_df = df[df['replyContent'].notna()].copy()
    if len(replied_df) > 0:
        replied_df['response_time_hours'] = (replied_df['repliedAt'] - replied_df['at']).dt.total_seconds() / 3600
        
        print(f"\n📈 Response Time Statistics:")
        print(f"   Average response time: {replied_df['response_time_hours'].mean():.1f} hours")
        print(f"   Median response time: {replied_df['response_time_hours'].median():.1f} hours")
        print(f"   Fastest response: {replied_df['response_time_hours'].min():.1f} hours")
        print(f"   Slowest response: {replied_df['response_time_hours'].max():.1f} hours")
        
        # Response time by app
        print(f"\n📱 Average Response Time by App:")
        for app_id in app_counts.index:
            app_replied = replied_df[replied_df['app_id'] == app_id]
            if len(app_replied) > 0:
                avg_response = app_replied['response_time_hours'].mean()
                print(f"   {app_id}: {avg_response:.1f} hours")
    
    return df, app_counts

if __name__ == "__main__":
    df, app_counts = analyze_apps_and_responses()
