{"version": 3, "file": "7866.b49b1018f43def5a8f30.js?v=b49b1018f43def5a8f30", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAwE;AACrB;AACR;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;AAC1B,OAAO,SAAS;AAChB,WAAW,6BAA6B;AACxC,aAAa,eAAe;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;AAC1B,UAAU,yBAAyB;AACnC,OAAO,mBAAmB;AAC1B,UAAU,YAAY;AACtB,OAAO,mBAAmB;AAC1B,UAAU,yBAAyB;AACnC,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB,iBAAiB;AACnC;;AAEA;;AAEA,2BAA2B,2BAAc;AACzC;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH,kBAAkB,mCAAmC;AACrD;AACA,CAAC;;AAED,qBAAqB,8BAAiB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iCAAiC,IAAI;AACrC;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,GAAG,iBAAiB;;AAErB,2BAA2B,8BAAiB;AAC5C,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,SAAS,SAAS;AAClB;AACA;AACA;;AAEA,mBAAmB,8BAAiB;AACpC;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,CAAC;;AAED;AACA;AACA,aAAa,8BAAiB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;;AAEA;;AAEA;;AAEA,yBAAyB,uBAAS;AAClC,kBAAkB,cAAI;AACtB,qDAAqD,cAAI;AACzD,WAAW,cAAI;AACf,iCAAiC,cAAI,WAAW,cAAI;AACpD,iBAAiB,cAAI;AACrB,2CAA2C,cAAI;AAC/C,MAAM,cAAI;AACV,wCAAwC,cAAI;AAC5C,WAAW,cAAI;AACf,kBAAkB,cAAI;AACtB,eAAe,cAAI;AACnB,CAAC;;AAED;AACA,eAAe,qBAAQ;AACvB;AACA,qDAAqD,iNAAiN,uCAAuC;AAC7S,gEAAgE,8eAA8e,wBAAwB,iBAAiB;AACvlB,4HAA4H,uEAAuE,GAAG,kBAAkB;AACxN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE,MAAM,GAAG,IAAI,2CAA2C,iBAAiB,IAAI,2JAA2J,KAAK,GAAG,MAAM,GAAG,yFAAyF,4CAA4C,KAAK,GAAG,+BAA+B,sBAAsB,KAAK,GAAG,uBAAuB,SAAS,SAAS,KAAK,GAAG,UAAU,kBAAkB,KAAK,GAAG,eAAe,oBAAoB,KAAK,GAAG,eAAe,YAAY,OAAO,GAAG,gCAAgC,KAAK,GAAG,2BAA2B,KAAK,GAAG,eAAe,YAAY,oCAAoC,KAAK,GAAG,gBAAgB,aAAa,+CAA+C,KAAK,GAAG,eAAe,WAAW,EAAE,2DAA2D,sBAAsB,KAAK,GAAG,2DAA2D,oEAAoE,KAAK,GAAG,0GAA0G,KAAK,GAAG,oEAAoE,KAAK,GAAG,yBAAyB,YAAY,kCAAkC,wDAAwD,KAAK,GAAG,wCAAwC,aAAa,wBAAwB,SAAS,SAAS,KAAK,GAAG,UAAU,6FAA6F,eAAe,KAAK,GAAG,iHAAiH,KAAK,GAAG,qCAAqC,KAAK,GAAG,wBAAwB,mDAAmD,KAAK,GAAG,gBAAgB,WAAW,sFAAsF,eAAe,KAAK,GAAG,8GAA8G,KAAK,GAAG,iDAAiD,YAAY,SAAS,YAAY,kBAAkB,SAAS,KAAK,GAAG,YAAY,gBAAgB,4CAA4C,KAAK,GAAG,0BAA0B,OAAO,MAAM,6FAA6F,eAAe,KAAK,GAAG,6GAA6G,KAAK,GAAG,iCAAiC,KAAK,GAAG,wBAAwB,mDAAmD,KAAK,GAAG,gBAAgB,6FAA6F,eAAe,KAAK,GAAG,8GAA8G,KAAK,GAAG,iDAAiD,QAAQ,SAAS,YAAY,kBAAkB,SAAS,KAAK,GAAG,YAAY,gBAAgB,4CAA4C,KAAK,GAAG,0BAA0B,OAAO,MAAM,8CAA8C,KAAK,GAAG,2DAA2D,cAAc,GAAG,6DAA6D,KAAK,GAAG,mFAAmF,0EAA0E,KAAK,GAAG,0DAA0D,sBAAsB,KAAK,GAAG,8DAA8D,cAAc,GAAG,wDAAwD,IAAI,sCAAsC,IAAI,6IAA6I,KAAK,GAAG,MAAM,GAAG,iDAAiD,uDAAuD,KAAK,GAAG,wCAAwC,GAAG,kCAAkC,KAAK,GAAG,wCAAwC,GAAG,QAAQ,KAAK,GAAG,2CAA2C,KAAK,GAAG,qDAAqD,KAAK,GAAG,qDAAqD,KAAK,GAAG,qDAAqD,KAAK,GAAG,yCAAyC,IAAI,QAAQ,KAAK,GAAG,kDAAkD,GAAG,KAAK,GAAG,qDAAqD,KAAK,GAAG,+DAA+D,MAAM,GAAG,8CAA8C,MAAM,GAAG,iCAAiC,MAAM,GAAG,kBAAkB,OAAO,iBAAiB,OAAO,KAAK,GAAG,gBAAgB,oDAAoD,MAAM,GAAG,qBAAqB,WAAW,KAAK,GAAG,gBAAgB,qBAAqB,mBAAmB,KAAK,GAAG,iBAAiB,mDAAmD,KAAK,GAAG,qDAAqD,KAAK,GAAG,eAAe,sCAAsC,KAAK,GAAG,qDAAqD,KAAK,GAAG,qDAAqD,KAAK,GAAG,qDAAqD,KAAK,GAAG,eAAe,gDAAgD,MAAM,GAAG,IAAI,0CAA0C,MAAM,GAAG,iCAAiC,MAAM,GAAG,8BAA8B,GAAG,MAAM,GAAG,yBAAyB,0CAA0C,MAAM,GAAG,4BAA4B,KAAK,GAAG,gBAAgB,iDAAiD,GAAG,MAAM,GAAG,wDAAwD,MAAM,GAAG,gCAAgC,KAAK,GAAG,gBAAgB,yDAAyD,IAAI,MAAM,GAAG,IAAI,YAAY,4BAA4B,KAAK,GAAG,cAAc,GAAG,gCAAgC,sBAAsB,KAAK,GAAG;AACloM;AACA,aAAa,kBAAkB;AAC/B,aAAa,6BAA6B;AAC1C;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,eAAe,uBAAuB;AACtC;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,6BAAU;AACnB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,kBAAkB,+BAA+B,iEAAiE;AAClH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,gCAAgC,SAAS;AAC5E,YAAY;AACZ,oBAAoB,gCAAgC,+BAA+B;AACnF;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEoC;;;;;;;;;;;;;ACnWmB;AACC;AACmE;AAC7E;AACM;AACgF;;AAEpI;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,YAAY,SAAS,+BAA+B;AACpD;AACA;AACA,kBAAkB,SAAS,cAAc;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,cAAc,SAAS,6BAA6B;AACpD;AACA;AACA;AACA;AACA,WAAW,SAAS,cAAc;AAClC,gBAAgB,SAAS,cAAc;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,YAAY,SAAS,eAAe;AACpC,gBAAgB,SAAS,kDAAkD;AAC3E,gBAAgB,SAAS,cAAc;AACvC;AACA,WAAW,SAAS,8BAA8B;AAClD,eAAe,SAAS,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS,oDAAoD;AAC1E,mBAAmB,SAAS,aAAa;AACzC,gBAAgB,SAAS,kDAAkD;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,iBAAiB;AACjB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,WAAW,SAAS,8BAA8B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,aAAa,SAAS,yBAAyB;AAC/C;AACA,UAAU,SAAS,eAAe;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,WAAW,SAAS,cAAc;AAClC;AACA,YAAY,SAAS,qDAAqD;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,aAAa,SAAS,2EAA2E;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,UAAU,SAAS,sEAAsE;AACzF,4DAA4D;AAC5D,gBAAgB,SAAS,uCAAuC;AAChE,cAAc,SAAS,4EAA4E;AACnG,cAAc,SAAS,qCAAqC;AAC5D;AACA,aAAa,SAAS,2BAA2B;AACjD;AACA,gBAAgB,SAAS,0BAA0B;AACnD,SAAS,SAAS,cAAc;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,YAAY,SAAS,cAAc;AACnC;AACA,cAAc,SAAS,sCAAsC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,UAAU,SAAS,+CAA+C;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,UAAU,SAAS,8FAA8F;AACjH;AACA,YAAY,SAAS,kBAAkB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,UAAU,oDAAoD;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD,yDAAyD;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,8BAA8B;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,8EAA8E,8BAA8B,uDAAuD;AACnK,yCAAyC;AACzC;AACA;AACA;AACA;AACA,aAAa;AACb,8DAA8D,2DAA2D;AACzH;AACA;AACA;AACA;AACA;AACA,uBAAuB,oCAAoC;AAC3D;AACA,uBAAuB,6DAA6D;AACpF,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,0CAA0C,mCAAmC;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,sEAAsE;AACrG;AACA;AACA,aAAa;AACb;AACA;AACA,UAAU,aAAa,kBAAkB,sCAAU;AACnD,iCAAiC,oDAAoD;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,+CAA+C;AACzD;AACA;AACA;;AAEA,gCAAgC,uCAAkB,oBAAoB,yBAAyB;AAC/F;AACA,MAAM;AACN;AACA,gBAAgB,uCAAkB,SAAS;AAC3C,MAAM;AACN;AACA,gBAAgB,gCAAW,SAAS;AACpC,MAAM;AACN;AACA,gBAAgB,gCAAW,SAAS;AACpC,MAAM;AACN;AACA;AACA,SAAS;AACT,4BAA4B;AAC5B,MAAM;AACN;AACA;AACA,SAAS;AACT,gBAAgB,uCAAkB,SAAS;AAC3C,MAAM;AACN;AACA;AACA,SAAS;AACT,gBAAgB,yBAAW;AAC3B;AACA;AACA,MAAM;AACN,6BAA6B,yBAAW,oBAAoB,eAAe;AAC3E,qDAAqD,cAAc,uCAAkB,SAAS;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,kCAAU;AACzC;AACA,yBAAyB,MAAM;AAC/B;AACA,yBAAyB,sCAAc;AACvC;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,yBAAyB,oCAAY;AACrC;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,aAAa;AACb,yBAAyB,6CAAqB;AAC9C;AACA,aAAa;AACb;AACA,KAAK;AACL;AACA,yBAAyB,SAAS,8BAA8B;AAChE;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,gBAAgB;AACvC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gBAAgB;AAC/B,4CAA4C,eAAe,uCAAuC,SAAS;AAC3G,eAAe,uCAAe;AAC9B,+BAA+B,gDAAgD;AAC/E;AACA,QAAQ,mCAAU;AAClB,QAAQ,qBAAG;AACX;AACA;AACA,MAAM,gBAAW;AACjB;AACA;AACA;AACA;AACA,mCAAmC,8BAAU;AAC7C;AACA;AACA;AACA,sCAAsC,QAAQ;AAC9C;AACA;AACA;AACA,cAAc,OAAO,iBAAiB,sCAAU;AAChD;AACA;AACA;AACA;AACA,iBAAiB,gBAAW;AAC5B;AACA,kCAAkC,KAAK;AACvC,yBAAyB,kBAAkB;AAC3C;AACA;AACA;AACA;AACA;AACA,+DAA+D,gBAAW;AAC1E;AACA,gCAAgC,KAAK;AACrC;AACA,2BAA2B,oCAAe;AAC1C,+BAA+B;AAC/B;AACA;AACA;AACA,iBAAiB;AACjB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,CAAC;;AAEuG", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@lezer/html/dist/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/lang-html/dist/index.js"], "sourcesContent": ["import { ContextTracker, ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { parseMixed } from '@lezer/common';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst scriptText = 54,\n  StartCloseScriptTag = 1,\n  styleText = 55,\n  StartCloseStyleTag = 2,\n  textareaText = 56,\n  StartCloseTextareaTag = 3,\n  EndTag = 4,\n  SelfClosingEndTag = 5,\n  StartTag = 6,\n  StartScriptTag = 7,\n  StartStyleTag = 8,\n  StartTextareaTag = 9,\n  StartSelfClosingTag = 10,\n  StartCloseTag = 11,\n  NoMatchStartCloseTag = 12,\n  MismatchedStartCloseTag = 13,\n  missingCloseTag = 57,\n  IncompleteCloseTag = 14,\n  commentContent$1 = 58,\n  Element = 20,\n  TagName = 22,\n  Attribute = 23,\n  AttributeName = 24,\n  AttributeValue = 26,\n  UnquotedAttributeValue = 27,\n  ScriptText = 28,\n  StyleText = 31,\n  TextareaText = 34,\n  OpenTag = 36,\n  CloseTag = 37,\n  Dialect_noMatch = 0,\n  Dialect_selfClosing = 1;\n\n/* Hand-written tokenizers for HTML. */\n\nconst selfClosers = {\n  area: true, base: true, br: true, col: true, command: true,\n  embed: true, frame: true, hr: true, img: true, input: true,\n  keygen: true, link: true, meta: true, param: true, source: true,\n  track: true, wbr: true, menuitem: true\n};\n\nconst implicitlyClosed = {\n  dd: true, li: true, optgroup: true, option: true, p: true,\n  rp: true, rt: true, tbody: true, td: true, tfoot: true,\n  th: true, tr: true\n};\n\nconst closeOnOpen = {\n  dd: {dd: true, dt: true},\n  dt: {dd: true, dt: true},\n  li: {li: true},\n  option: {option: true, optgroup: true},\n  optgroup: {optgroup: true},\n  p: {\n    address: true, article: true, aside: true, blockquote: true, dir: true,\n    div: true, dl: true, fieldset: true, footer: true, form: true,\n    h1: true, h2: true, h3: true, h4: true, h5: true, h6: true,\n    header: true, hgroup: true, hr: true, menu: true, nav: true, ol: true,\n    p: true, pre: true, section: true, table: true, ul: true\n  },\n  rp: {rp: true, rt: true},\n  rt: {rp: true, rt: true},\n  tbody: {tbody: true, tfoot: true},\n  td: {td: true, th: true},\n  tfoot: {tbody: true},\n  th: {td: true, th: true},\n  thead: {tbody: true, tfoot: true},\n  tr: {tr: true}\n};\n\nfunction nameChar(ch) {\n  return ch == 45 || ch == 46 || ch == 58 || ch >= 65 && ch <= 90 || ch == 95 || ch >= 97 && ch <= 122 || ch >= 161\n}\n\nfunction isSpace(ch) {\n  return ch == 9 || ch == 10 || ch == 13 || ch == 32\n}\n\nlet cachedName = null, cachedInput = null, cachedPos = 0;\nfunction tagNameAfter(input, offset) {\n  let pos = input.pos + offset;\n  if (cachedPos == pos && cachedInput == input) return cachedName\n  let next = input.peek(offset);\n  while (isSpace(next)) next = input.peek(++offset);\n  let name = \"\";\n  for (;;) {\n    if (!nameChar(next)) break\n    name += String.fromCharCode(next);\n    next = input.peek(++offset);\n  }\n  // Undefined to signal there's a <? or <!, null for just missing\n  cachedInput = input; cachedPos = pos;\n  return cachedName = name ? name.toLowerCase() : next == question || next == bang ? undefined : null\n}\n\nconst lessThan = 60, greaterThan = 62, slash = 47, question = 63, bang = 33, dash = 45;\n\nfunction ElementContext(name, parent) {\n  this.name = name;\n  this.parent = parent;\n  this.hash = parent ? parent.hash : 0;\n  for (let i = 0; i < name.length; i++) this.hash += (this.hash << 4) + name.charCodeAt(i) + (name.charCodeAt(i) << 8);\n}\n\nconst startTagTerms = [StartTag, StartSelfClosingTag, StartScriptTag, StartStyleTag, StartTextareaTag];\n\nconst elementContext = new ContextTracker({\n  start: null,\n  shift(context, term, stack, input) {\n    return startTagTerms.indexOf(term) > -1 ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  reduce(context, term) {\n    return term == Element && context ? context.parent : context\n  },\n  reuse(context, node, stack, input) {\n    let type = node.type.id;\n    return type == StartTag || type == OpenTag\n      ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  hash(context) { return context ? context.hash : 0 },\n  strict: false\n});\n\nconst tagStart = new ExternalTokenizer((input, stack) => {\n  if (input.next != lessThan) {\n    // End of file, close any open tags\n    if (input.next < 0 && stack.context) input.acceptToken(missingCloseTag);\n    return\n  }\n  input.advance();\n  let close = input.next == slash;\n  if (close) input.advance();\n  let name = tagNameAfter(input, 0);\n  if (name === undefined) return\n  if (!name) return input.acceptToken(close ? IncompleteCloseTag : StartTag)\n\n  let parent = stack.context ? stack.context.name : null;\n  if (close) {\n    if (name == parent) return input.acceptToken(StartCloseTag)\n    if (parent && implicitlyClosed[parent]) return input.acceptToken(missingCloseTag, -2)\n    if (stack.dialectEnabled(Dialect_noMatch)) return input.acceptToken(NoMatchStartCloseTag)\n    for (let cx = stack.context; cx; cx = cx.parent) if (cx.name == name) return\n    input.acceptToken(MismatchedStartCloseTag);\n  } else {\n    if (name == \"script\") return input.acceptToken(StartScriptTag)\n    if (name == \"style\") return input.acceptToken(StartStyleTag)\n    if (name == \"textarea\") return input.acceptToken(StartTextareaTag)\n    if (selfClosers.hasOwnProperty(name)) return input.acceptToken(StartSelfClosingTag)\n    if (parent && closeOnOpen[parent] && closeOnOpen[parent][name]) input.acceptToken(missingCloseTag, -1);\n    else input.acceptToken(StartTag);\n  }\n}, {contextual: true});\n\nconst commentContent = new ExternalTokenizer(input => {\n  for (let dashes = 0, i = 0;; i++) {\n    if (input.next < 0) {\n      if (i) input.acceptToken(commentContent$1);\n      break\n    }\n    if (input.next == dash) {\n      dashes++;\n    } else if (input.next == greaterThan && dashes >= 2) {\n      if (i > 3) input.acceptToken(commentContent$1, -2);\n      break\n    } else {\n      dashes = 0;\n    }\n    input.advance();\n  }\n});\n\nfunction inForeignElement(context) {\n  for (; context; context = context.parent)\n    if (context.name == \"svg\" || context.name == \"math\") return true\n  return false\n}\n\nconst endTag = new ExternalTokenizer((input, stack) => {\n  if (input.next == slash && input.peek(1) == greaterThan) {\n    let selfClosing = stack.dialectEnabled(Dialect_selfClosing) || inForeignElement(stack.context);\n    input.acceptToken(selfClosing ? SelfClosingEndTag : EndTag, 2);\n  } else if (input.next == greaterThan) {\n    input.acceptToken(EndTag, 1);\n  }\n});\n\nfunction contentTokenizer(tag, textToken, endToken) {\n  let lastState = 2 + tag.length;\n  return new ExternalTokenizer(input => {\n    // state means:\n    // - 0 nothing matched\n    // - 1 '<' matched\n    // - 2 '</' + possibly whitespace matched\n    // - 3-(1+tag.length) part of the tag matched\n    // - lastState whole tag + possibly whitespace matched\n    for (let state = 0, matchedLen = 0, i = 0;; i++) {\n      if (input.next < 0) {\n        if (i) input.acceptToken(textToken);\n        break\n      }\n      if (state == 0 && input.next == lessThan ||\n          state == 1 && input.next == slash ||\n          state >= 2 && state < lastState && input.next == tag.charCodeAt(state - 2)) {\n        state++;\n        matchedLen++;\n      } else if ((state == 2 || state == lastState) && isSpace(input.next)) {\n        matchedLen++;\n      } else if (state == lastState && input.next == greaterThan) {\n        if (i > matchedLen)\n          input.acceptToken(textToken, -matchedLen);\n        else\n          input.acceptToken(endToken, -(matchedLen - 2));\n        break\n      } else if ((input.next == 10 /* '\\n' */ || input.next == 13 /* '\\r' */) && i) {\n        input.acceptToken(textToken, 1);\n        break\n      } else {\n        state = matchedLen = 0;\n      }\n      input.advance();\n    }\n  })\n}\n\nconst scriptTokens = contentTokenizer(\"script\", scriptText, StartCloseScriptTag);\n\nconst styleTokens = contentTokenizer(\"style\", styleText, StartCloseStyleTag);\n\nconst textareaTokens = contentTokenizer(\"textarea\", textareaText, StartCloseTextareaTag);\n\nconst htmlHighlighting = styleTags({\n  \"Text RawText\": tags.content,\n  \"StartTag StartCloseTag SelfClosingEndTag EndTag\": tags.angleBracket,\n  TagName: tags.tagName,\n  \"MismatchedCloseTag/TagName\": [tags.tagName,  tags.invalid],\n  AttributeName: tags.attributeName,\n  \"AttributeValue UnquotedAttributeValue\": tags.attributeValue,\n  Is: tags.definitionOperator,\n  \"EntityReference CharacterReference\": tags.character,\n  Comment: tags.blockComment,\n  ProcessingInst: tags.processingInstruction,\n  DoctypeDecl: tags.documentMeta\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \",xOVO!rOOO!WQ#tO'#CqO!]Q#tO'#CzO!bQ#tO'#C}O!gQ#tO'#DQO!lQ#tO'#DSO!qOaO'#CpO!|ObO'#CpO#XOdO'#CpO$eO!rO'#CpOOO`'#Cp'#CpO$lO$fO'#DTO$tQ#tO'#DVO$yQ#tO'#DWOOO`'#Dk'#DkOOO`'#DY'#DYQVO!rOOO%OQ&rO,59]O%WQ&rO,59fO%`Q&rO,59iO%hQ&rO,59lO%sQ&rO,59nOOOa'#D^'#D^O%{OaO'#CxO&WOaO,59[OOOb'#D_'#D_O&`ObO'#C{O&kObO,59[OOOd'#D`'#D`O&sOdO'#DOO'OOdO,59[OOO`'#Da'#DaO'WO!rO,59[O'_Q#tO'#DROOO`,59[,59[OOOp'#Db'#DbO'dO$fO,59oOOO`,59o,59oO'lQ#|O,59qO'qQ#|O,59rOOO`-E7W-E7WO'vQ&rO'#CsOOQW'#DZ'#DZO(UQ&rO1G.wOOOa1G.w1G.wO(^Q&rO1G/QOOOb1G/Q1G/QO(fQ&rO1G/TOOOd1G/T1G/TO(nQ&rO1G/WOOO`1G/W1G/WOOO`1G/Y1G/YO(yQ&rO1G/YOOOa-E7[-E7[O)RQ#tO'#CyOOO`1G.v1G.vOOOb-E7]-E7]O)WQ#tO'#C|OOOd-E7^-E7^O)]Q#tO'#DPOOO`-E7_-E7_O)bQ#|O,59mOOOp-E7`-E7`OOO`1G/Z1G/ZOOO`1G/]1G/]OOO`1G/^1G/^O)gQ,UO,59_OOQW-E7X-E7XOOOa7+$c7+$cOOOb7+$l7+$lOOOd7+$o7+$oOOO`7+$r7+$rOOO`7+$t7+$tO)rQ#|O,59eO)wQ#|O,59hO)|Q#|O,59kOOO`1G/X1G/XO*RO7[O'#CvO*dOMhO'#CvOOQW1G.y1G.yOOO`1G/P1G/POOO`1G/S1G/SOOO`1G/V1G/VOOOO'#D['#D[O*uO7[O,59bOOQW,59b,59bOOOO'#D]'#D]O+WOMhO,59bOOOO-E7Y-E7YOOQW1G.|1G.|OOOO-E7Z-E7Z\",\n  stateData: \"+s~O!^OS~OUSOVPOWQOXROYTO[]O][O^^O`^Oa^Ob^Oc^Ox^O{_O!dZO~OfaO~OfbO~OfcO~OfdO~OfeO~O!WfOPlP!ZlP~O!XiOQoP!ZoP~O!YlORrP!ZrP~OUSOVPOWQOXROYTOZqO[]O][O^^O`^Oa^Ob^Oc^Ox^O!dZO~O!ZrO~P#dO![sO!euO~OfvO~OfwO~OS|OhyO~OS!OOhyO~OS!QOhyO~OS!SOT!TOhyO~OS!TOhyO~O!WfOPlX!ZlX~OP!WO!Z!XO~O!XiOQoX!ZoX~OQ!ZO!Z!XO~O!YlORrX!ZrX~OR!]O!Z!XO~O!Z!XO~P#dOf!_O~O![sO!e!aO~OS!bO~OS!cO~Oi!dOSgXhgXTgX~OS!fOhyO~OS!gOhyO~OS!hOhyO~OS!iOT!jOhyO~OS!jOhyO~Of!kO~Of!lO~Of!mO~OS!nO~Ok!qO!`!oO!b!pO~OS!rO~OS!sO~OS!tO~Oa!uOb!uOc!uO!`!wO!a!uO~Oa!xOb!xOc!xO!b!wO!c!xO~Oa!uOb!uOc!uO!`!{O!a!uO~Oa!xOb!xOc!xO!b!{O!c!xO~OT~bac!dx{!d~\",\n  goto: \"%p!`PPPPPPPPPPPPPPPPPPPP!a!gP!mPP!yP!|#P#S#Y#]#`#f#i#l#r#x!aP!a!aP$O$U$l$r$x%O%U%[%bPPPPPPPP%hX^OX`pXUOX`pezabcde{}!P!R!UR!q!dRhUR!XhXVOX`pRkVR!XkXWOX`pRnWR!XnXXOX`pQrXR!XpXYOX`pQ`ORx`Q{aQ}bQ!PcQ!RdQ!UeZ!e{}!P!R!UQ!v!oR!z!vQ!y!pR!|!yQgUR!VgQjVR!YjQmWR![mQpXR!^pQtZR!`tS_O`ToXp\",\n  nodeNames: \"⚠ StartCloseTag StartCloseTag StartCloseTag EndTag SelfClosingEndTag StartTag StartTag StartTag StartTag StartTag StartCloseTag StartCloseTag StartCloseTag IncompleteCloseTag Document Text EntityReference CharacterReference InvalidEntity Element OpenTag TagName Attribute AttributeName Is AttributeValue UnquotedAttributeValue ScriptText CloseTag OpenTag StyleText CloseTag OpenTag TextareaText CloseTag OpenTag CloseTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag CloseTag DoctypeDecl\",\n  maxTerm: 67,\n  context: elementContext,\n  nodeProps: [\n    [\"closedBy\", -10,1,2,3,7,8,9,10,11,12,13,\"EndTag\",6,\"EndTag SelfClosingEndTag\",-4,21,30,33,36,\"CloseTag\"],\n    [\"openedBy\", 4,\"StartTag StartCloseTag\",5,\"StartTag\",-4,29,32,35,37,\"OpenTag\"],\n    [\"group\", -9,14,17,18,19,20,39,40,41,42,\"Entity\",16,\"Entity TextContent\",-3,28,31,34,\"TextContent Entity\"]\n  ],\n  propSources: [htmlHighlighting],\n  skippedNodes: [0],\n  repeatNodeCount: 9,\n  tokenData: \"!<p!aR!YOX$qXY,QYZ,QZ[$q[]&X]^,Q^p$qpq,Qqr-_rs3_sv-_vw3}wxHYx}-_}!OH{!O!P-_!P!Q$q!Q![-_![!]Mz!]!^-_!^!_!$S!_!`!;x!`!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4U-_4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!Z$|c`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr$qrs&}sv$qvw+Pwx(tx!^$q!^!_*V!_!a&X!a#S$q#S#T&X#T;'S$q;'S;=`+z<%lO$q!R&bX`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&Xq'UV`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}P'pT`POv'kw!^'k!_;'S'k;'S;=`(P<%lO'kP(SP;=`<%l'kp([S!cpOv(Vx;'S(V;'S;=`(h<%lO(Vp(kP;=`<%l(Vq(qP;=`<%l&}a({W`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t`)jT!a`Or)esv)ew;'S)e;'S;=`)y<%lO)e`)|P;=`<%l)ea*SP;=`<%l(t!Q*^V!a`!cpOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!Q*vP;=`<%l*V!R*|P;=`<%l&XW+UYkWOX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+PW+wP;=`<%l+P!Z+}P;=`<%l$q!a,]``P!a`!cp!^^OX&XXY,QYZ,QZ]&X]^,Q^p&Xpq,Qqr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!_-ljhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q[/ebhSkWOX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+PS0rXhSqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0mS1bP;=`<%l0m[1hP;=`<%l/^!V1vchS`P!a`!cpOq&Xqr1krs&}sv1kvw0mwx(tx!P1k!P!Q&X!Q!^1k!^!_*V!_!a&X!a#s1k#s$f&X$f;'S1k;'S;=`3R<%l?Ah1k?Ah?BY&X?BY?Mn1k?MnO&X!V3UP;=`<%l1k!_3[P;=`<%l-_!Z3hV!`h`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}!_4WihSkWc!ROX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst>]tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^/^!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!Z5zbkWOX5uXZ7SZ[5u[^7S^p5uqr5urs7Sst+Ptw5uwx7Sx!]5u!]!^7w!^!a7S!a#S5u#S#T7S#T;'S5u;'S;=`8n<%lO5u!R7VVOp7Sqs7St!]7S!]!^7l!^;'S7S;'S;=`7q<%lO7S!R7qOa!R!R7tP;=`<%l7S!Z8OYkWa!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!Z8qP;=`<%l5u!_8{ihSkWOX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst/^tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^:j!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!_:sbhSkWa!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!V<QchSOp7Sqr;{rs7Sst0mtw;{wx7Sx!P;{!P!Q7S!Q!];{!]!^=]!^!a7S!a#s;{#s$f7S$f;'S;{;'S;=`>P<%l?Ah;{?Ah?BY7S?BY?Mn;{?MnO7S!V=dXhSa!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!V>SP;=`<%l;{!_>YP;=`<%l8t!_>dhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^/^!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!Z@TakWOX@OXZAYZ[@O[^AY^p@Oqr@OrsAYsw@OwxAYx!]@O!]!^Az!^!aAY!a#S@O#S#TAY#T;'S@O;'S;=`Bq<%lO@O!RA]UOpAYq!]AY!]!^Ao!^;'SAY;'S;=`At<%lOAY!RAtOb!R!RAwP;=`<%lAY!ZBRYkWb!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!ZBtP;=`<%l@O!_COhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^Dj!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!_DsbhSkWb!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!VFQbhSOpAYqrE{rsAYswE{wxAYx!PE{!P!QAY!Q!]E{!]!^GY!^!aAY!a#sE{#s$fAY$f;'SE{;'S;=`G|<%l?AhE{?Ah?BYAY?BY?MnE{?MnOAY!VGaXhSb!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!VHPP;=`<%lE{!_HVP;=`<%lBw!ZHcW!bx`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t!aIYlhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OKQ!O!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!aK_khS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!`&X!`!aMS!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!TM_X`P!a`!cp!eQOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!aNZ!ZhSfQ`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OMz!O!PMz!P!Q$q!Q![Mz![!]Mz!]!^-_!^!_*V!_!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f$}-_$}%OMz%O%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4UMz4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Je-_$Je$JgMz$Jg$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!a!$PP;=`<%lMz!R!$ZY!a`!cpOq*Vqr!$yrs(Vsv*Vwx)ex!a*V!a!b!4t!b;'S*V;'S;=`*s<%lO*V!R!%Q]!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!%y!O!f*V!f!g!']!g#W*V#W#X!0`#X;'S*V;'S;=`*s<%lO*V!R!&QX!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!&m!O;'S*V;'S;=`*s<%lO*V!R!&vV!a`!cp!dPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!'dX!a`!cpOr*Vrs(Vsv*Vwx)ex!q*V!q!r!(P!r;'S*V;'S;=`*s<%lO*V!R!(WX!a`!cpOr*Vrs(Vsv*Vwx)ex!e*V!e!f!(s!f;'S*V;'S;=`*s<%lO*V!R!(zX!a`!cpOr*Vrs(Vsv*Vwx)ex!v*V!v!w!)g!w;'S*V;'S;=`*s<%lO*V!R!)nX!a`!cpOr*Vrs(Vsv*Vwx)ex!{*V!{!|!*Z!|;'S*V;'S;=`*s<%lO*V!R!*bX!a`!cpOr*Vrs(Vsv*Vwx)ex!r*V!r!s!*}!s;'S*V;'S;=`*s<%lO*V!R!+UX!a`!cpOr*Vrs(Vsv*Vwx)ex!g*V!g!h!+q!h;'S*V;'S;=`*s<%lO*V!R!+xY!a`!cpOr!+qrs!,hsv!+qvw!-Swx!.[x!`!+q!`!a!/j!a;'S!+q;'S;=`!0Y<%lO!+qq!,mV!cpOv!,hvx!-Sx!`!,h!`!a!-q!a;'S!,h;'S;=`!.U<%lO!,hP!-VTO!`!-S!`!a!-f!a;'S!-S;'S;=`!-k<%lO!-SP!-kO{PP!-nP;=`<%l!-Sq!-xS!cp{POv(Vx;'S(V;'S;=`(h<%lO(Vq!.XP;=`<%l!,ha!.aX!a`Or!.[rs!-Ssv!.[vw!-Sw!`!.[!`!a!.|!a;'S!.[;'S;=`!/d<%lO!.[a!/TT!a`{POr)esv)ew;'S)e;'S;=`)y<%lO)ea!/gP;=`<%l!.[!R!/sV!a`!cp{POr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!0]P;=`<%l!+q!R!0gX!a`!cpOr*Vrs(Vsv*Vwx)ex#c*V#c#d!1S#d;'S*V;'S;=`*s<%lO*V!R!1ZX!a`!cpOr*Vrs(Vsv*Vwx)ex#V*V#V#W!1v#W;'S*V;'S;=`*s<%lO*V!R!1}X!a`!cpOr*Vrs(Vsv*Vwx)ex#h*V#h#i!2j#i;'S*V;'S;=`*s<%lO*V!R!2qX!a`!cpOr*Vrs(Vsv*Vwx)ex#m*V#m#n!3^#n;'S*V;'S;=`*s<%lO*V!R!3eX!a`!cpOr*Vrs(Vsv*Vwx)ex#d*V#d#e!4Q#e;'S*V;'S;=`*s<%lO*V!R!4XX!a`!cpOr*Vrs(Vsv*Vwx)ex#X*V#X#Y!+q#Y;'S*V;'S;=`*s<%lO*V!R!4{Y!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!a!4t!a!b!:]!b;'S!4t;'S;=`!;r<%lO!4tq!5pV!cpOv!5kvx!6Vx!a!5k!a!b!7W!b;'S!5k;'S;=`!8V<%lO!5kP!6YTO!a!6V!a!b!6i!b;'S!6V;'S;=`!7Q<%lO!6VP!6lTO!`!6V!`!a!6{!a;'S!6V;'S;=`!7Q<%lO!6VP!7QOxPP!7TP;=`<%l!6Vq!7]V!cpOv!5kvx!6Vx!`!5k!`!a!7r!a;'S!5k;'S;=`!8V<%lO!5kq!7yS!cpxPOv(Vx;'S(V;'S;=`(h<%lO(Vq!8YP;=`<%l!5ka!8bX!a`Or!8]rs!6Vsv!8]vw!6Vw!a!8]!a!b!8}!b;'S!8];'S;=`!:V<%lO!8]a!9SX!a`Or!8]rs!6Vsv!8]vw!6Vw!`!8]!`!a!9o!a;'S!8];'S;=`!:V<%lO!8]a!9vT!a`xPOr)esv)ew;'S)e;'S;=`)y<%lO)ea!:YP;=`<%l!8]!R!:dY!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!`!4t!`!a!;S!a;'S!4t;'S;=`!;r<%lO!4t!R!;]V!a`!cpxPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!;uP;=`<%l!4t!V!<TXiS`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X\",\n  tokenizers: [scriptTokens, styleTokens, textareaTokens, endTag, tagStart, commentContent, 0, 1, 2, 3, 4, 5],\n  topRules: {\"Document\":[0,15]},\n  dialects: {noMatch: 0, selfClosing: 485},\n  tokenPrec: 487\n});\n\nfunction getAttrs(openTag, input) {\n  let attrs = Object.create(null);\n  for (let att of openTag.getChildren(Attribute)) {\n    let name = att.getChild(AttributeName), value = att.getChild(AttributeValue) || att.getChild(UnquotedAttributeValue);\n    if (name) attrs[input.read(name.from, name.to)] =\n      !value ? \"\" : value.type.id == AttributeValue ? input.read(value.from + 1, value.to - 1) : input.read(value.from, value.to);\n  }\n  return attrs\n}\n\nfunction findTagName(openTag, input) {\n  let tagNameNode = openTag.getChild(TagName);\n  return tagNameNode ? input.read(tagNameNode.from, tagNameNode.to) : \" \"\n}\n\nfunction maybeNest(node, input, tags) {\n  let attrs;\n  for (let tag of tags) {\n    if (!tag.attrs || tag.attrs(attrs || (attrs = getAttrs(node.node.parent.firstChild, input))))\n      return {parser: tag.parser}\n  }\n  return null\n}\n\n// tags?: {\n//   tag: string,\n//   attrs?: ({[attr: string]: string}) => boolean,\n//   parser: Parser\n// }[]\n// attributes?: {\n//   name: string,\n//   tagName?: string,\n//   parser: Parser\n// }[]\n \nfunction configureNesting(tags = [], attributes = []) {\n  let script = [], style = [], textarea = [], other = [];\n  for (let tag of tags) {\n    let array = tag.tag == \"script\" ? script : tag.tag == \"style\" ? style : tag.tag == \"textarea\" ? textarea : other;\n    array.push(tag);\n  }\n  let attrs = attributes.length ? Object.create(null) : null;\n  for (let attr of attributes) (attrs[attr.name] || (attrs[attr.name] = [])).push(attr);\n\n  return parseMixed((node, input) => {\n    let id = node.type.id;\n    if (id == ScriptText) return maybeNest(node, input, script)\n    if (id == StyleText) return maybeNest(node, input, style)\n    if (id == TextareaText) return maybeNest(node, input, textarea)\n\n    if (id == Element && other.length) {\n      let n = node.node, open = n.firstChild, tagName = open && findTagName(open, input), attrs;\n      if (tagName) for (let tag of other) {\n        if (tag.tag == tagName && (!tag.attrs || tag.attrs(attrs || (attrs = getAttrs(n, input))))) {\n          let close = n.lastChild;\n          return {parser: tag.parser, overlay: [{from: open.to, to: close.type.id == CloseTag ? close.from : n.to}]}\n        }\n      }\n    }\n\n    if (attrs && id == Attribute) {\n      let n = node.node, nameNode;\n      if (nameNode = n.firstChild) {\n        let matches = attrs[input.read(nameNode.from, nameNode.to)];\n        if (matches) for (let attr of matches) {\n          if (attr.tagName && attr.tagName != findTagName(n.parent, input)) continue\n          let value = n.lastChild;\n          if (value.type.id == AttributeValue) {\n            let from = value.from + 1;\n            let last = value.lastChild, to = value.to - (last && last.isError ? 0 : 1);\n            if (to > from) return {parser: attr.parser, overlay: [{from, to}]}\n          } else if (value.type.id == UnquotedAttributeValue) {\n            return {parser: attr.parser, overlay: [{from: value.from, to: value.to}]}\n          }\n        }\n      }\n    }\n    return null\n  })\n}\n\nexport { configureNesting, parser };\n", "import { parser, configureNesting } from '@lezer/html';\nimport { cssLanguage, css } from '@codemirror/lang-css';\nimport { javascriptLanguage, typescriptLanguage, jsxLanguage, tsxLanguage, javascript } from '@codemirror/lang-javascript';\nimport { EditorView } from '@codemirror/view';\nimport { EditorSelection } from '@codemirror/state';\nimport { syntaxTree, LRLanguage, indentNodeProp, foldNodeProp, bracketMatchingHandle, LanguageSupport } from '@codemirror/language';\n\nconst Targets = [\"_blank\", \"_self\", \"_top\", \"_parent\"];\nconst Charsets = [\"ascii\", \"utf-8\", \"utf-16\", \"latin1\", \"latin1\"];\nconst Methods = [\"get\", \"post\", \"put\", \"delete\"];\nconst Encs = [\"application/x-www-form-urlencoded\", \"multipart/form-data\", \"text/plain\"];\nconst Bool = [\"true\", \"false\"];\nconst S = {}; // Empty tag spec\nconst Tags = {\n    a: {\n        attrs: {\n            href: null, ping: null, type: null,\n            media: null,\n            target: Targets,\n            hreflang: null\n        }\n    },\n    abbr: S,\n    address: S,\n    area: {\n        attrs: {\n            alt: null, coords: null, href: null, target: null, ping: null,\n            media: null, hreflang: null, type: null,\n            shape: [\"default\", \"rect\", \"circle\", \"poly\"]\n        }\n    },\n    article: S,\n    aside: S,\n    audio: {\n        attrs: {\n            src: null, mediagroup: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"],\n            preload: [\"none\", \"metadata\", \"auto\"],\n            autoplay: [\"autoplay\"],\n            loop: [\"loop\"],\n            controls: [\"controls\"]\n        }\n    },\n    b: S,\n    base: { attrs: { href: null, target: Targets } },\n    bdi: S,\n    bdo: S,\n    blockquote: { attrs: { cite: null } },\n    body: S,\n    br: S,\n    button: {\n        attrs: {\n            form: null, formaction: null, name: null, value: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"autofocus\"],\n            formenctype: Encs,\n            formmethod: Methods,\n            formnovalidate: [\"novalidate\"],\n            formtarget: Targets,\n            type: [\"submit\", \"reset\", \"button\"]\n        }\n    },\n    canvas: { attrs: { width: null, height: null } },\n    caption: S,\n    center: S,\n    cite: S,\n    code: S,\n    col: { attrs: { span: null } },\n    colgroup: { attrs: { span: null } },\n    command: {\n        attrs: {\n            type: [\"command\", \"checkbox\", \"radio\"],\n            label: null, icon: null, radiogroup: null, command: null, title: null,\n            disabled: [\"disabled\"],\n            checked: [\"checked\"]\n        }\n    },\n    data: { attrs: { value: null } },\n    datagrid: { attrs: { disabled: [\"disabled\"], multiple: [\"multiple\"] } },\n    datalist: { attrs: { data: null } },\n    dd: S,\n    del: { attrs: { cite: null, datetime: null } },\n    details: { attrs: { open: [\"open\"] } },\n    dfn: S,\n    div: S,\n    dl: S,\n    dt: S,\n    em: S,\n    embed: { attrs: { src: null, type: null, width: null, height: null } },\n    eventsource: { attrs: { src: null } },\n    fieldset: { attrs: { disabled: [\"disabled\"], form: null, name: null } },\n    figcaption: S,\n    figure: S,\n    footer: S,\n    form: {\n        attrs: {\n            action: null, name: null,\n            \"accept-charset\": Charsets,\n            autocomplete: [\"on\", \"off\"],\n            enctype: Encs,\n            method: Methods,\n            novalidate: [\"novalidate\"],\n            target: Targets\n        }\n    },\n    h1: S, h2: S, h3: S, h4: S, h5: S, h6: S,\n    head: {\n        children: [\"title\", \"base\", \"link\", \"style\", \"meta\", \"script\", \"noscript\", \"command\"]\n    },\n    header: S,\n    hgroup: S,\n    hr: S,\n    html: {\n        attrs: { manifest: null }\n    },\n    i: S,\n    iframe: {\n        attrs: {\n            src: null, srcdoc: null, name: null, width: null, height: null,\n            sandbox: [\"allow-top-navigation\", \"allow-same-origin\", \"allow-forms\", \"allow-scripts\"],\n            seamless: [\"seamless\"]\n        }\n    },\n    img: {\n        attrs: {\n            alt: null, src: null, ismap: null, usemap: null, width: null, height: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"]\n        }\n    },\n    input: {\n        attrs: {\n            alt: null, dirname: null, form: null, formaction: null,\n            height: null, list: null, max: null, maxlength: null, min: null,\n            name: null, pattern: null, placeholder: null, size: null, src: null,\n            step: null, value: null, width: null,\n            accept: [\"audio/*\", \"video/*\", \"image/*\"],\n            autocomplete: [\"on\", \"off\"],\n            autofocus: [\"autofocus\"],\n            checked: [\"checked\"],\n            disabled: [\"disabled\"],\n            formenctype: Encs,\n            formmethod: Methods,\n            formnovalidate: [\"novalidate\"],\n            formtarget: Targets,\n            multiple: [\"multiple\"],\n            readonly: [\"readonly\"],\n            required: [\"required\"],\n            type: [\"hidden\", \"text\", \"search\", \"tel\", \"url\", \"email\", \"password\", \"datetime\", \"date\", \"month\",\n                \"week\", \"time\", \"datetime-local\", \"number\", \"range\", \"color\", \"checkbox\", \"radio\",\n                \"file\", \"submit\", \"image\", \"reset\", \"button\"]\n        }\n    },\n    ins: { attrs: { cite: null, datetime: null } },\n    kbd: S,\n    keygen: {\n        attrs: {\n            challenge: null, form: null, name: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            keytype: [\"RSA\"]\n        }\n    },\n    label: { attrs: { for: null, form: null } },\n    legend: S,\n    li: { attrs: { value: null } },\n    link: {\n        attrs: {\n            href: null, type: null,\n            hreflang: null,\n            media: null,\n            sizes: [\"all\", \"16x16\", \"16x16 32x32\", \"16x16 32x32 64x64\"]\n        }\n    },\n    map: { attrs: { name: null } },\n    mark: S,\n    menu: { attrs: { label: null, type: [\"list\", \"context\", \"toolbar\"] } },\n    meta: {\n        attrs: {\n            content: null,\n            charset: Charsets,\n            name: [\"viewport\", \"application-name\", \"author\", \"description\", \"generator\", \"keywords\"],\n            \"http-equiv\": [\"content-language\", \"content-type\", \"default-style\", \"refresh\"]\n        }\n    },\n    meter: { attrs: { value: null, min: null, low: null, high: null, max: null, optimum: null } },\n    nav: S,\n    noscript: S,\n    object: {\n        attrs: {\n            data: null, type: null, name: null, usemap: null, form: null, width: null, height: null,\n            typemustmatch: [\"typemustmatch\"]\n        }\n    },\n    ol: { attrs: { reversed: [\"reversed\"], start: null, type: [\"1\", \"a\", \"A\", \"i\", \"I\"] },\n        children: [\"li\", \"script\", \"template\", \"ul\", \"ol\"] },\n    optgroup: { attrs: { disabled: [\"disabled\"], label: null } },\n    option: { attrs: { disabled: [\"disabled\"], label: null, selected: [\"selected\"], value: null } },\n    output: { attrs: { for: null, form: null, name: null } },\n    p: S,\n    param: { attrs: { name: null, value: null } },\n    pre: S,\n    progress: { attrs: { value: null, max: null } },\n    q: { attrs: { cite: null } },\n    rp: S,\n    rt: S,\n    ruby: S,\n    samp: S,\n    script: {\n        attrs: {\n            type: [\"text/javascript\"],\n            src: null,\n            async: [\"async\"],\n            defer: [\"defer\"],\n            charset: Charsets\n        }\n    },\n    section: S,\n    select: {\n        attrs: {\n            form: null, name: null, size: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            multiple: [\"multiple\"]\n        }\n    },\n    slot: { attrs: { name: null } },\n    small: S,\n    source: { attrs: { src: null, type: null, media: null } },\n    span: S,\n    strong: S,\n    style: {\n        attrs: {\n            type: [\"text/css\"],\n            media: null,\n            scoped: null\n        }\n    },\n    sub: S,\n    summary: S,\n    sup: S,\n    table: S,\n    tbody: S,\n    td: { attrs: { colspan: null, rowspan: null, headers: null } },\n    template: S,\n    textarea: {\n        attrs: {\n            dirname: null, form: null, maxlength: null, name: null, placeholder: null,\n            rows: null, cols: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            readonly: [\"readonly\"],\n            required: [\"required\"],\n            wrap: [\"soft\", \"hard\"]\n        }\n    },\n    tfoot: S,\n    th: { attrs: { colspan: null, rowspan: null, headers: null, scope: [\"row\", \"col\", \"rowgroup\", \"colgroup\"] } },\n    thead: S,\n    time: { attrs: { datetime: null } },\n    title: S,\n    tr: S,\n    track: {\n        attrs: {\n            src: null, label: null, default: null,\n            kind: [\"subtitles\", \"captions\", \"descriptions\", \"chapters\", \"metadata\"],\n            srclang: null\n        }\n    },\n    ul: { children: [\"li\", \"script\", \"template\", \"ul\", \"ol\"] },\n    var: S,\n    video: {\n        attrs: {\n            src: null, poster: null, width: null, height: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"],\n            preload: [\"auto\", \"metadata\", \"none\"],\n            autoplay: [\"autoplay\"],\n            mediagroup: [\"movie\"],\n            muted: [\"muted\"],\n            controls: [\"controls\"]\n        }\n    },\n    wbr: S\n};\nconst GlobalAttrs = {\n    accesskey: null,\n    class: null,\n    contenteditable: Bool,\n    contextmenu: null,\n    dir: [\"ltr\", \"rtl\", \"auto\"],\n    draggable: [\"true\", \"false\", \"auto\"],\n    dropzone: [\"copy\", \"move\", \"link\", \"string:\", \"file:\"],\n    hidden: [\"hidden\"],\n    id: null,\n    inert: [\"inert\"],\n    itemid: null,\n    itemprop: null,\n    itemref: null,\n    itemscope: [\"itemscope\"],\n    itemtype: null,\n    lang: [\"ar\", \"bn\", \"de\", \"en-GB\", \"en-US\", \"es\", \"fr\", \"hi\", \"id\", \"ja\", \"pa\", \"pt\", \"ru\", \"tr\", \"zh\"],\n    spellcheck: Bool,\n    autocorrect: Bool,\n    autocapitalize: Bool,\n    style: null,\n    tabindex: null,\n    title: null,\n    translate: [\"yes\", \"no\"],\n    rel: [\"stylesheet\", \"alternate\", \"author\", \"bookmark\", \"help\", \"license\", \"next\", \"nofollow\", \"noreferrer\", \"prefetch\", \"prev\", \"search\", \"tag\"],\n    role: /*@__PURE__*/\"alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer\".split(\" \"),\n    \"aria-activedescendant\": null,\n    \"aria-atomic\": Bool,\n    \"aria-autocomplete\": [\"inline\", \"list\", \"both\", \"none\"],\n    \"aria-busy\": Bool,\n    \"aria-checked\": [\"true\", \"false\", \"mixed\", \"undefined\"],\n    \"aria-controls\": null,\n    \"aria-describedby\": null,\n    \"aria-disabled\": Bool,\n    \"aria-dropeffect\": null,\n    \"aria-expanded\": [\"true\", \"false\", \"undefined\"],\n    \"aria-flowto\": null,\n    \"aria-grabbed\": [\"true\", \"false\", \"undefined\"],\n    \"aria-haspopup\": Bool,\n    \"aria-hidden\": Bool,\n    \"aria-invalid\": [\"true\", \"false\", \"grammar\", \"spelling\"],\n    \"aria-label\": null,\n    \"aria-labelledby\": null,\n    \"aria-level\": null,\n    \"aria-live\": [\"off\", \"polite\", \"assertive\"],\n    \"aria-multiline\": Bool,\n    \"aria-multiselectable\": Bool,\n    \"aria-owns\": null,\n    \"aria-posinset\": null,\n    \"aria-pressed\": [\"true\", \"false\", \"mixed\", \"undefined\"],\n    \"aria-readonly\": Bool,\n    \"aria-relevant\": null,\n    \"aria-required\": Bool,\n    \"aria-selected\": [\"true\", \"false\", \"undefined\"],\n    \"aria-setsize\": null,\n    \"aria-sort\": [\"ascending\", \"descending\", \"none\", \"other\"],\n    \"aria-valuemax\": null,\n    \"aria-valuemin\": null,\n    \"aria-valuenow\": null,\n    \"aria-valuetext\": null\n};\nconst eventAttributes = /*@__PURE__*/(\"beforeunload copy cut dragstart dragover dragleave dragenter dragend \" +\n    \"drag paste focus blur change click load mousedown mouseenter mouseleave \" +\n    \"mouseup keydown keyup resize scroll unload\").split(\" \").map(n => \"on\" + n);\nfor (let a of eventAttributes)\n    GlobalAttrs[a] = null;\nclass Schema {\n    constructor(extraTags, extraAttrs) {\n        this.tags = Object.assign(Object.assign({}, Tags), extraTags);\n        this.globalAttrs = Object.assign(Object.assign({}, GlobalAttrs), extraAttrs);\n        this.allTags = Object.keys(this.tags);\n        this.globalAttrNames = Object.keys(this.globalAttrs);\n    }\n}\nSchema.default = /*@__PURE__*/new Schema;\nfunction elementName(doc, tree, max = doc.length) {\n    if (!tree)\n        return \"\";\n    let tag = tree.firstChild;\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, Math.min(name.to, max)) : \"\";\n}\nfunction findParentElement(tree, skip = false) {\n    for (; tree; tree = tree.parent)\n        if (tree.name == \"Element\") {\n            if (skip)\n                skip = false;\n            else\n                return tree;\n        }\n    return null;\n}\nfunction allowedChildren(doc, tree, schema) {\n    let parentInfo = schema.tags[elementName(doc, findParentElement(tree))];\n    return (parentInfo === null || parentInfo === void 0 ? void 0 : parentInfo.children) || schema.allTags;\n}\nfunction openTags(doc, tree) {\n    let open = [];\n    for (let parent = findParentElement(tree); parent && !parent.type.isTop; parent = findParentElement(parent.parent)) {\n        let tagName = elementName(doc, parent);\n        if (tagName && parent.lastChild.name == \"CloseTag\")\n            break;\n        if (tagName && open.indexOf(tagName) < 0 && (tree.name == \"EndTag\" || tree.from >= parent.firstChild.to))\n            open.push(tagName);\n    }\n    return open;\n}\nconst identifier = /^[:\\-\\.\\w\\u00b7-\\uffff]*$/;\nfunction completeTag(state, schema, tree, from, to) {\n    let end = /\\s*>/.test(state.sliceDoc(to, to + 5)) ? \"\" : \">\";\n    let parent = findParentElement(tree, true);\n    return { from, to,\n        options: allowedChildren(state.doc, parent, schema).map(tagName => ({ label: tagName, type: \"type\" })).concat(openTags(state.doc, tree).map((tag, i) => ({ label: \"/\" + tag, apply: \"/\" + tag + end,\n            type: \"type\", boost: 99 - i }))),\n        validFor: /^\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/ };\n}\nfunction completeCloseTag(state, tree, from, to) {\n    let end = /\\s*>/.test(state.sliceDoc(to, to + 5)) ? \"\" : \">\";\n    return { from, to,\n        options: openTags(state.doc, tree).map((tag, i) => ({ label: tag, apply: tag + end, type: \"type\", boost: 99 - i })),\n        validFor: identifier };\n}\nfunction completeStartTag(state, schema, tree, pos) {\n    let options = [], level = 0;\n    for (let tagName of allowedChildren(state.doc, tree, schema))\n        options.push({ label: \"<\" + tagName, type: \"type\" });\n    for (let open of openTags(state.doc, tree))\n        options.push({ label: \"</\" + open + \">\", type: \"type\", boost: 99 - level++ });\n    return { from: pos, to: pos, options, validFor: /^<\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/ };\n}\nfunction completeAttrName(state, schema, tree, from, to) {\n    let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;\n    let localAttrs = info && info.attrs ? Object.keys(info.attrs) : [];\n    let names = info && info.globalAttrs === false ? localAttrs\n        : localAttrs.length ? localAttrs.concat(schema.globalAttrNames) : schema.globalAttrNames;\n    return { from, to,\n        options: names.map(attrName => ({ label: attrName, type: \"property\" })),\n        validFor: identifier };\n}\nfunction completeAttrValue(state, schema, tree, from, to) {\n    var _a;\n    let nameNode = (_a = tree.parent) === null || _a === void 0 ? void 0 : _a.getChild(\"AttributeName\");\n    let options = [], token = undefined;\n    if (nameNode) {\n        let attrName = state.sliceDoc(nameNode.from, nameNode.to);\n        let attrs = schema.globalAttrs[attrName];\n        if (!attrs) {\n            let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;\n            attrs = (info === null || info === void 0 ? void 0 : info.attrs) && info.attrs[attrName];\n        }\n        if (attrs) {\n            let base = state.sliceDoc(from, to).toLowerCase(), quoteStart = '\"', quoteEnd = '\"';\n            if (/^['\"]/.test(base)) {\n                token = base[0] == '\"' ? /^[^\"]*$/ : /^[^']*$/;\n                quoteStart = \"\";\n                quoteEnd = state.sliceDoc(to, to + 1) == base[0] ? \"\" : base[0];\n                base = base.slice(1);\n                from++;\n            }\n            else {\n                token = /^[^\\s<>='\"]*$/;\n            }\n            for (let value of attrs)\n                options.push({ label: value, apply: quoteStart + value + quoteEnd, type: \"constant\" });\n        }\n    }\n    return { from, to, options, validFor: token };\n}\nfunction htmlCompletionFor(schema, context) {\n    let { state, pos } = context, tree = syntaxTree(state).resolveInner(pos, -1), around = tree.resolve(pos);\n    for (let scan = pos, before; around == tree && (before = tree.childBefore(scan));) {\n        let last = before.lastChild;\n        if (!last || !last.type.isError || last.from < last.to)\n            break;\n        around = tree = before;\n        scan = last.from;\n    }\n    if (tree.name == \"TagName\") {\n        return tree.parent && /CloseTag$/.test(tree.parent.name) ? completeCloseTag(state, tree, tree.from, pos)\n            : completeTag(state, schema, tree, tree.from, pos);\n    }\n    else if (tree.name == \"StartTag\") {\n        return completeTag(state, schema, tree, pos, pos);\n    }\n    else if (tree.name == \"StartCloseTag\" || tree.name == \"IncompleteCloseTag\") {\n        return completeCloseTag(state, tree, pos, pos);\n    }\n    else if (tree.name == \"OpenTag\" || tree.name == \"SelfClosingTag\" || tree.name == \"AttributeName\") {\n        return completeAttrName(state, schema, tree, tree.name == \"AttributeName\" ? tree.from : pos, pos);\n    }\n    else if (tree.name == \"Is\" || tree.name == \"AttributeValue\" || tree.name == \"UnquotedAttributeValue\") {\n        return completeAttrValue(state, schema, tree, tree.name == \"Is\" ? pos : tree.from, pos);\n    }\n    else if (context.explicit && (around.name == \"Element\" || around.name == \"Text\" || around.name == \"Document\")) {\n        return completeStartTag(state, schema, tree, pos);\n    }\n    else {\n        return null;\n    }\n}\n/**\nHTML tag completion. Opens and closes tags and attributes in a\ncontext-aware way.\n*/\nfunction htmlCompletionSource(context) {\n    return htmlCompletionFor(Schema.default, context);\n}\n/**\nCreate a completion source for HTML extended with additional tags\nor attributes.\n*/\nfunction htmlCompletionSourceWith(config) {\n    let { extraTags, extraGlobalAttributes: extraAttrs } = config;\n    let schema = extraAttrs || extraTags ? new Schema(extraTags, extraAttrs) : Schema.default;\n    return (context) => htmlCompletionFor(schema, context);\n}\n\nconst jsonParser = /*@__PURE__*/javascriptLanguage.parser.configure({ top: \"SingleExpression\" });\nconst defaultNesting = [\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/typescript\" || attrs.lang == \"ts\",\n        parser: typescriptLanguage.parser },\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/babel\" || attrs.type == \"text/jsx\",\n        parser: jsxLanguage.parser },\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/typescript-jsx\",\n        parser: tsxLanguage.parser },\n    { tag: \"script\",\n        attrs(attrs) {\n            return /^(importmap|speculationrules|application\\/(.+\\+)?json)$/i.test(attrs.type);\n        },\n        parser: jsonParser },\n    { tag: \"script\",\n        attrs(attrs) {\n            return !attrs.type || /^(?:text|application)\\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(attrs.type);\n        },\n        parser: javascriptLanguage.parser },\n    { tag: \"style\",\n        attrs(attrs) {\n            return (!attrs.lang || attrs.lang == \"css\") && (!attrs.type || /^(text\\/)?(x-)?(stylesheet|css)$/i.test(attrs.type));\n        },\n        parser: cssLanguage.parser }\n];\nconst defaultAttrs = /*@__PURE__*/[\n    { name: \"style\",\n        parser: /*@__PURE__*/cssLanguage.parser.configure({ top: \"Styles\" }) }\n].concat(/*@__PURE__*/eventAttributes.map(name => ({ name, parser: javascriptLanguage.parser })));\n/**\nA language provider based on the [Lezer HTML\nparser](https://github.com/lezer-parser/html), extended with the\nJavaScript and CSS parsers to parse the content of `<script>` and\n`<style>` tags.\n*/\nconst htmlPlain = /*@__PURE__*/LRLanguage.define({\n    name: \"html\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Element(context) {\n                    let after = /^(\\s*)(<\\/)?/.exec(context.textAfter);\n                    if (context.node.to <= context.pos + after[0].length)\n                        return context.continue();\n                    return context.lineIndent(context.node.from) + (after[2] ? 0 : context.unit);\n                },\n                \"OpenTag CloseTag SelfClosingTag\"(context) {\n                    return context.column(context.node.from) + context.unit;\n                },\n                Document(context) {\n                    if (context.pos + /\\s*/.exec(context.textAfter)[0].length < context.node.to)\n                        return context.continue();\n                    let endElt = null, close;\n                    for (let cur = context.node;;) {\n                        let last = cur.lastChild;\n                        if (!last || last.name != \"Element\" || last.to != cur.to)\n                            break;\n                        endElt = cur = last;\n                    }\n                    if (endElt && !((close = endElt.lastChild) && (close.name == \"CloseTag\" || close.name == \"SelfClosingTag\")))\n                        return context.lineIndent(endElt.from) + context.unit;\n                    return null;\n                }\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                Element(node) {\n                    let first = node.firstChild, last = node.lastChild;\n                    if (!first || first.name != \"OpenTag\")\n                        return null;\n                    return { from: first.to, to: last.name == \"CloseTag\" ? last.from : node.to };\n                }\n            }),\n            /*@__PURE__*/bracketMatchingHandle.add({\n                \"OpenTag CloseTag\": node => node.getChild(\"TagName\")\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"<!--\", close: \"-->\" } },\n        indentOnInput: /^\\s*<\\/\\w+\\W$/,\n        wordChars: \"-._\"\n    }\n});\n/**\nA language provider based on the [Lezer HTML\nparser](https://github.com/lezer-parser/html), extended with the\nJavaScript and CSS parsers to parse the content of `<script>` and\n`<style>` tags.\n*/\nconst htmlLanguage = /*@__PURE__*/htmlPlain.configure({\n    wrap: /*@__PURE__*/configureNesting(defaultNesting, defaultAttrs)\n});\n/**\nLanguage support for HTML, including\n[`htmlCompletion`](https://codemirror.net/6/docs/ref/#lang-html.htmlCompletion) and JavaScript and\nCSS support extensions.\n*/\nfunction html(config = {}) {\n    let dialect = \"\", wrap;\n    if (config.matchClosingTags === false)\n        dialect = \"noMatch\";\n    if (config.selfClosingTags === true)\n        dialect = (dialect ? dialect + \" \" : \"\") + \"selfClosing\";\n    if (config.nestedLanguages && config.nestedLanguages.length ||\n        config.nestedAttributes && config.nestedAttributes.length)\n        wrap = configureNesting((config.nestedLanguages || []).concat(defaultNesting), (config.nestedAttributes || []).concat(defaultAttrs));\n    let lang = wrap ? htmlPlain.configure({ wrap, dialect }) : dialect ? htmlLanguage.configure({ dialect }) : htmlLanguage;\n    return new LanguageSupport(lang, [\n        htmlLanguage.data.of({ autocomplete: htmlCompletionSourceWith(config) }),\n        config.autoCloseTags !== false ? autoCloseTags : [],\n        javascript().support,\n        css().support\n    ]);\n}\nconst selfClosers = /*@__PURE__*/new Set(/*@__PURE__*/\"area base br col command embed frame hr img input keygen link meta param source track wbr menuitem\".split(\" \"));\n/**\nExtension that will automatically insert close tags when a `>` or\n`/` is typed.\n*/\nconst autoCloseTags = /*@__PURE__*/EditorView.inputHandler.of((view, from, to, text, insertTransaction) => {\n    if (view.composing || view.state.readOnly || from != to || (text != \">\" && text != \"/\") ||\n        !htmlLanguage.isActiveAt(view.state, from, -1))\n        return false;\n    let base = insertTransaction(), { state } = base;\n    let closeTags = state.changeByRange(range => {\n        var _a, _b, _c;\n        let didType = state.doc.sliceString(range.from - 1, range.to) == text;\n        let { head } = range, after = syntaxTree(state).resolveInner(head, -1), name;\n        if (didType && text == \">\" && after.name == \"EndTag\") {\n            let tag = after.parent;\n            if (((_b = (_a = tag.parent) === null || _a === void 0 ? void 0 : _a.lastChild) === null || _b === void 0 ? void 0 : _b.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag.parent, head)) &&\n                !selfClosers.has(name)) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `</${name}>`;\n                return { range, changes: { from: head, to, insert } };\n            }\n        }\n        else if (didType && text == \"/\" && after.name == \"IncompleteCloseTag\") {\n            let tag = after.parent;\n            if (after.from == head - 2 && ((_c = tag.lastChild) === null || _c === void 0 ? void 0 : _c.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag, head)) && !selfClosers.has(name)) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `${name}>`;\n                return {\n                    range: EditorSelection.cursor(head + insert.length, -1),\n                    changes: { from: head, to, insert }\n                };\n            }\n        }\n        return { range };\n    });\n    if (closeTags.changes.empty)\n        return false;\n    view.dispatch([\n        base,\n        state.update(closeTags, {\n            userEvent: \"input.complete\",\n            scrollIntoView: true\n        })\n    ]);\n    return true;\n});\n\nexport { autoCloseTags, html, htmlCompletionSource, htmlCompletionSourceWith, htmlLanguage, htmlPlain };\n"], "names": [], "sourceRoot": ""}