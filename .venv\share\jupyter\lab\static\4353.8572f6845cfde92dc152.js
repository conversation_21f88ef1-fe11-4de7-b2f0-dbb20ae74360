"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4353],{44353:(e,t,n)=>{n.r(t);n.d(t,{blockComment:()=>y,blockUncomment:()=>k,copyLineDown:()=>tn,copyLineUp:()=>en,cursorCharBackward:()=>ue,cursorCharBackwardLogical:()=>me,cursorCharForward:()=>ce,cursorCharForwardLogical:()=>he,cursorCharLeft:()=>ie,cursorCharRight:()=>ae,cursorDocEnd:()=>Dt,cursorDocStart:()=>Et,cursorGroupBackward:()=>we,cursorGroupForward:()=>ke,cursorGroupForwardWin:()=>ve,cursorGroupLeft:()=>ge,cursorGroupRight:()=>ye,cursorLineBoundaryBackward:()=>Je,cursorLineBoundaryForward:()=>Ge,cursorLineBoundaryLeft:()=>Pe,cursorLineBoundaryRight:()=>He,cursorLineDown:()=>Ie,cursorLineEnd:()=>ze,cursorLineStart:()=>We,cursorLineUp:()=>Oe,cursorMatchingBracket:()=>je,cursorPageDown:()=>Ne,cursorPageUp:()=>Fe,cursorSubwordBackward:()=>De,cursorSubwordForward:()=>Ee,cursorSyntaxLeft:()=>Me,cursorSyntaxRight:()=>be,defaultKeymap:()=>wn,deleteCharBackward:()=>Ft,deleteCharBackwardStrict:()=>Nt,deleteCharForward:()=>Ut,deleteGroupBackward:()=>Jt,deleteGroupForward:()=>Pt,deleteLine:()=>nn,deleteLineBoundaryBackward:()=>zt,deleteLineBoundaryForward:()=>_t,deleteToLineEnd:()=>Ht,deleteToLineStart:()=>Wt,deleteTrailingWhitespace:()=>jt,emacsStyleKeymap:()=>yn,history:()=>T,historyField:()=>O,historyKeymap:()=>ee,indentLess:()=>hn,indentMore:()=>dn,indentSelection:()=>fn,indentWithTab:()=>Sn,insertBlankLine:()=>an,insertNewline:()=>rn,insertNewlineAndIndent:()=>sn,insertNewlineKeepIndent:()=>on,insertTab:()=>gn,invertedEffects:()=>L,isolateHistory:()=>x,lineComment:()=>m,lineUncomment:()=>p,moveLineDown:()=>Yt,moveLineUp:()=>Xt,redo:()=>V,redoDepth:()=>J,redoSelection:()=>N,selectAll:()=>Mt,selectCharBackward:()=>Ze,selectCharBackwardLogical:()=>tt,selectCharForward:()=>Ye,selectCharForwardLogical:()=>et,selectCharLeft:()=>Qe,selectCharRight:()=>Xe,selectDocEnd:()=>Lt,selectDocStart:()=>xt,selectGroupBackward:()=>st,selectGroupForward:()=>lt,selectGroupForwardWin:()=>it,selectGroupLeft:()=>rt,selectGroupRight:()=>ot,selectLine:()=>bt,selectLineBoundaryBackward:()=>St,selectLineBoundaryForward:()=>wt,selectLineBoundaryLeft:()=>vt,selectLineBoundaryRight:()=>At,selectLineDown:()=>pt,selectLineEnd:()=>Bt,selectLineStart:()=>Ct,selectLineUp:()=>mt,selectMatchingBracket:()=>qe,selectPageDown:()=>kt,selectPageUp:()=>yt,selectParentSyntax:()=>Tt,selectSubwordBackward:()=>ut,selectSubwordForward:()=>ct,selectSyntaxLeft:()=>ft,selectSyntaxRight:()=>dt,simplifySelection:()=>Ot,splitLine:()=>qt,standardKeymap:()=>kn,temporarilySetTabFocusMode:()=>pn,toggleBlockComment:()=>g,toggleBlockCommentByLine:()=>w,toggleComment:()=>f,toggleLineComment:()=>h,toggleTabFocusMode:()=>mn,transposeChars:()=>Kt,undo:()=>R,undoDepth:()=>G,undoSelection:()=>F});var r=n(71674);var o=n.n(r);var l=n(22819);var s=n.n(l);var i=n(4452);var a=n.n(i);var c=n(66575);var u=n.n(c);const f=e=>{let{state:t}=e,n=t.doc.lineAt(t.selection.main.from),r=S(e.state,n.from);return r.line?h(e):r.block?w(e):false};function d(e,t){return({state:n,dispatch:r})=>{if(n.readOnly)return false;let o=e(t,n);if(!o)return false;r(n.update(o));return true}}const h=d(E,0);const m=d(E,1);const p=d(E,2);const g=d(B,0);const y=d(B,1);const k=d(B,2);const w=d(((e,t)=>B(e,t,C(t))),0);function S(e,t){let n=e.languageDataAt("commentTokens",t,1);return n.length?n[0]:{}}const v=50;function A(e,{open:t,close:n},r,o){let l=e.sliceDoc(r-v,r);let s=e.sliceDoc(o,o+v);let i=/\s*$/.exec(l)[0].length,a=/^\s*/.exec(s)[0].length;let c=l.length-i;if(l.slice(c-t.length,c)==t&&s.slice(a,a+n.length)==n){return{open:{pos:r-i,margin:i&&1},close:{pos:o+a,margin:a&&1}}}let u,f;if(o-r<=2*v){u=f=e.sliceDoc(r,o)}else{u=e.sliceDoc(r,r+v);f=e.sliceDoc(o-v,o)}let d=/^\s*/.exec(u)[0].length,h=/\s*$/.exec(f)[0].length;let m=f.length-h-n.length;if(u.slice(d,d+t.length)==t&&f.slice(m,m+n.length)==n){return{open:{pos:r+d+t.length,margin:/\s/.test(u.charAt(d+t.length))?1:0},close:{pos:o-h-n.length,margin:/\s/.test(f.charAt(m-1))?1:0}}}return null}function C(e){let t=[];for(let n of e.selection.ranges){let r=e.doc.lineAt(n.from);let o=n.to<=r.to?r:e.doc.lineAt(n.to);if(o.from>r.from&&o.from==n.to)o=n.to==r.to+1?r:e.doc.lineAt(n.to-1);let l=t.length-1;if(l>=0&&t[l].to>r.from)t[l].to=o.to;else t.push({from:r.from+/^\s*/.exec(r.text)[0].length,to:o.to})}return t}function B(e,t,n=t.selection.ranges){let r=n.map((e=>S(t,e.from).block));if(!r.every((e=>e)))return null;let o=n.map(((e,n)=>A(t,r[n],e.from,e.to)));if(e!=2&&!o.every((e=>e))){return{changes:t.changes(n.map(((e,t)=>{if(o[t])return[];return[{from:e.from,insert:r[t].open+" "},{from:e.to,insert:" "+r[t].close}]})))}}else if(e!=1&&o.some((e=>e))){let e=[];for(let t=0,n;t<o.length;t++)if(n=o[t]){let o=r[t],{open:l,close:s}=n;e.push({from:l.pos-o.open.length,to:l.pos+l.margin},{from:s.pos-s.margin,to:s.pos+o.close.length})}return{changes:e}}return null}function E(e,t,n=t.selection.ranges){let r=[];let o=-1;for(let{from:l,to:s}of n){let e=r.length,n=1e9;let i=S(t,l).line;if(!i)continue;for(let a=l;a<=s;){let e=t.doc.lineAt(a);if(e.from>o&&(l==s||s>e.from)){o=e.from;let t=/^\s*/.exec(e.text)[0].length;let l=t==e.length;let s=e.text.slice(t,t+i.length)==i?t:-1;if(t<e.text.length&&t<n)n=t;r.push({line:e,comment:s,token:i,indent:t,empty:l,single:false})}a=e.to+1}if(n<1e9)for(let t=e;t<r.length;t++)if(r[t].indent<r[t].line.text.length)r[t].indent=n;if(r.length==e+1)r[e].single=true}if(e!=2&&r.some((e=>e.comment<0&&(!e.empty||e.single)))){let e=[];for(let{line:t,token:o,indent:l,empty:s,single:i}of r)if(i||!s)e.push({from:t.from+l,insert:o+" "});let n=t.changes(e);return{changes:n,selection:t.selection.map(n,1)}}else if(e!=1&&r.some((e=>e.comment>=0))){let e=[];for(let{line:t,comment:n,token:o}of r)if(n>=0){let r=t.from+n,l=r+o.length;if(t.text[l-t.from]==" ")l++;e.push({from:r,to:l})}return{changes:e}}return null}const D=r.Annotation.define();const x=r.Annotation.define();const L=r.Facet.define();const M=r.Facet.define({combine(e){return(0,r.combineConfig)(e,{minDepth:100,newGroupDelay:500,joinToEvent:(e,t)=>t},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(e,t)=>(n,r)=>e(n,r)||t(n,r)})}});const b=r.StateField.define({create(){return Z.empty},update(e,t){let n=t.state.facet(M);let o=t.annotation(D);if(o){let r=P.fromTransaction(t,o.selection),l=o.side;let s=l==0?e.undone:e.done;if(r)s=H(s,s.length,n.minDepth,r);else s=K(s,t.startState.selection);return new Z(l==0?o.rest:s,l==0?s:o.rest)}let l=t.annotation(x);if(l=="full"||l=="before")e=e.isolate();if(t.annotation(r.Transaction.addToHistory)===false)return!t.changes.empty?e.addMapping(t.changes.desc):e;let s=P.fromTransaction(t);let i=t.annotation(r.Transaction.time),a=t.annotation(r.Transaction.userEvent);if(s)e=e.addChanges(s,i,a,n,t);else if(t.selection)e=e.addSelection(t.startState.selection,i,a,n.newGroupDelay);if(l=="full"||l=="after")e=e.isolate();return e},toJSON(e){return{done:e.done.map((e=>e.toJSON())),undone:e.undone.map((e=>e.toJSON()))}},fromJSON(e){return new Z(e.done.map(P.fromJSON),e.undone.map(P.fromJSON))}});function T(e={}){return[b,M.of(e),l.EditorView.domEventHandlers({beforeinput(e,t){let n=e.inputType=="historyUndo"?R:e.inputType=="historyRedo"?V:null;if(!n)return false;e.preventDefault();return n(t)}})]}const O=b;function I(e,t){return function({state:n,dispatch:r}){if(!t&&n.readOnly)return false;let o=n.field(b,false);if(!o)return false;let l=o.pop(e,n,t);if(!l)return false;r(l);return true}}const R=I(0,false);const V=I(1,false);const F=I(0,true);const N=I(1,true);function U(e){return function(t){let n=t.field(b,false);if(!n)return 0;let r=e==0?n.done:n.undone;return r.length-(r.length&&!r[0].changes?1:0)}}const G=U(0);const J=U(1);class P{constructor(e,t,n,r,o){this.changes=e;this.effects=t;this.mapped=n;this.startSelection=r;this.selectionsAfter=o}setSelAfter(e){return new P(this.changes,this.effects,this.mapped,this.startSelection,e)}toJSON(){var e,t,n;return{changes:(e=this.changes)===null||e===void 0?void 0:e.toJSON(),mapped:(t=this.mapped)===null||t===void 0?void 0:t.toJSON(),startSelection:(n=this.startSelection)===null||n===void 0?void 0:n.toJSON(),selectionsAfter:this.selectionsAfter.map((e=>e.toJSON()))}}static fromJSON(e){return new P(e.changes&&r.ChangeSet.fromJSON(e.changes),[],e.mapped&&r.ChangeDesc.fromJSON(e.mapped),e.startSelection&&r.EditorSelection.fromJSON(e.startSelection),e.selectionsAfter.map(r.EditorSelection.fromJSON))}static fromTransaction(e,t){let n=j;for(let r of e.startState.facet(L)){let t=r(e);if(t.length)n=n.concat(t)}if(!n.length&&e.changes.empty)return null;return new P(e.changes.invert(e.startState.doc),n,undefined,t||e.startState.selection,j)}static selection(e){return new P(undefined,j,undefined,undefined,e)}}function H(e,t,n,r){let o=t+1>n+20?t-n-1:0;let l=e.slice(o,t);l.push(r);return l}function W(e,t){let n=[],r=false;e.iterChangedRanges(((e,t)=>n.push(e,t)));t.iterChangedRanges(((e,t,o,l)=>{for(let s=0;s<n.length;){let e=n[s++],t=n[s++];if(l>=e&&o<=t)r=true}}));return r}function z(e,t){return e.ranges.length==t.ranges.length&&e.ranges.filter(((e,n)=>e.empty!=t.ranges[n].empty)).length===0}function _(e,t){return!e.length?t:!t.length?e:e.concat(t)}const j=[];const q=200;function K(e,t){if(!e.length){return[P.selection([t])]}else{let n=e[e.length-1];let r=n.selectionsAfter.slice(Math.max(0,n.selectionsAfter.length-q));if(r.length&&r[r.length-1].eq(t))return e;r.push(t);return H(e,e.length-1,1e9,n.setSelAfter(r))}}function $(e){let t=e[e.length-1];let n=e.slice();n[e.length-1]=t.setSelAfter(t.selectionsAfter.slice(0,t.selectionsAfter.length-1));return n}function Q(e,t){if(!e.length)return e;let n=e.length,r=j;while(n){let o=X(e[n-1],t,r);if(o.changes&&!o.changes.empty||o.effects.length){let t=e.slice(0,n);t[n-1]=o;return t}else{t=o.mapped;n--;r=o.selectionsAfter}}return r.length?[P.selection(r)]:j}function X(e,t,n){let o=_(e.selectionsAfter.length?e.selectionsAfter.map((e=>e.map(t))):j,n);if(!e.changes)return P.selection(o);let l=e.changes.map(t),s=t.mapDesc(e.changes,true);let i=e.mapped?e.mapped.composeDesc(s):s;return new P(l,r.StateEffect.mapEffects(e.effects,t),i,e.startSelection.map(s),o)}const Y=/^(input\.type|delete)($|\.)/;class Z{constructor(e,t,n=0,r=undefined){this.done=e;this.undone=t;this.prevTime=n;this.prevUserEvent=r}isolate(){return this.prevTime?new Z(this.done,this.undone):this}addChanges(e,t,n,o,l){let s=this.done,i=s[s.length-1];if(i&&i.changes&&!i.changes.empty&&e.changes&&(!n||Y.test(n))&&(!i.selectionsAfter.length&&t-this.prevTime<o.newGroupDelay&&o.joinToEvent(l,W(i.changes,e.changes))||n=="input.type.compose")){s=H(s,s.length-1,o.minDepth,new P(e.changes.compose(i.changes),_(r.StateEffect.mapEffects(e.effects,i.changes),i.effects),i.mapped,i.startSelection,j))}else{s=H(s,s.length,o.minDepth,e)}return new Z(s,j,t,n)}addSelection(e,t,n,r){let o=this.done.length?this.done[this.done.length-1].selectionsAfter:j;if(o.length>0&&t-this.prevTime<r&&n==this.prevUserEvent&&n&&/^select($|\.)/.test(n)&&z(o[o.length-1],e))return this;return new Z(K(this.done,e),this.undone,t,n)}addMapping(e){return new Z(Q(this.done,e),Q(this.undone,e),this.prevTime,this.prevUserEvent)}pop(e,t,n){let r=e==0?this.done:this.undone;if(r.length==0)return null;let o=r[r.length-1],l=o.selectionsAfter[0]||t.selection;if(n&&o.selectionsAfter.length){return t.update({selection:o.selectionsAfter[o.selectionsAfter.length-1],annotations:D.of({side:e,rest:$(r),selection:l}),userEvent:e==0?"select.undo":"select.redo",scrollIntoView:true})}else if(!o.changes){return null}else{let n=r.length==1?j:r.slice(0,r.length-1);if(o.mapped)n=Q(n,o.mapped);return t.update({changes:o.changes,selection:o.startSelection,effects:o.effects,annotations:D.of({side:e,rest:n,selection:l}),filter:false,userEvent:e==0?"undo":"redo",scrollIntoView:true})}}}Z.empty=new Z(j,j);const ee=[{key:"Mod-z",run:R,preventDefault:true},{key:"Mod-y",mac:"Mod-Shift-z",run:V,preventDefault:true},{linux:"Ctrl-Shift-z",run:V,preventDefault:true},{key:"Mod-u",run:F,preventDefault:true},{key:"Alt-u",mac:"Mod-Shift-u",run:N,preventDefault:true}];function te(e,t){return r.EditorSelection.create(e.ranges.map(t),e.mainIndex)}function ne(e,t){return e.update({selection:t,scrollIntoView:true,userEvent:"select"})}function re({state:e,dispatch:t},n){let r=te(e.selection,n);if(r.eq(e.selection,true))return false;t(ne(e,r));return true}function oe(e,t){return r.EditorSelection.cursor(t?e.to:e.from)}function le(e,t){return re(e,(n=>n.empty?e.moveByChar(n,t):oe(n,t)))}function se(e){return e.textDirectionAt(e.state.selection.main.head)==l.Direction.LTR}const ie=e=>le(e,!se(e));const ae=e=>le(e,se(e));const ce=e=>le(e,true);const ue=e=>le(e,false);function fe(e,t,n){let o=t.head,l=e.doc.lineAt(o);if(o==(n?l.to:l.from))o=n?Math.min(e.doc.length,l.to+1):Math.max(0,l.from-1);else o=l.from+(0,r.findClusterBreak)(l.text,o-l.from,n);return r.EditorSelection.cursor(o,n?-1:1)}function de(e,t){return re(e,(n=>n.empty?fe(e.state,n,t):oe(n,t)))}const he=e=>de(e,true);const me=e=>de(e,false);function pe(e,t){return re(e,(n=>n.empty?e.moveByGroup(n,t):oe(n,t)))}const ge=e=>pe(e,!se(e));const ye=e=>pe(e,se(e));const ke=e=>pe(e,true);const we=e=>pe(e,false);function Se(e,t,n){let o=e.state.charCategorizer(t);let l=o(n),s=l!=r.CharCategory.Space;return e=>{let t=o(e);if(t!=r.CharCategory.Space)return s&&t==l;s=false;return true}}const ve=e=>re(e,(t=>t.empty?e.moveByChar(t,true,(n=>Se(e,t.head,n))):oe(t,true)));const Ae=typeof Intl!="undefined"&&Intl.Segmenter?new Intl.Segmenter(undefined,{granularity:"word"}):null;function Ce(e,t,n){let o=e.state.charCategorizer(t.from);let l=r.CharCategory.Space,s=t.from,i=0;let a=false,c=false,u=false;let f=t=>{if(a)return false;s+=n?t.length:-t.length;let f=o(t),d;if(f==r.CharCategory.Word&&t.charCodeAt(0)<128&&/[\W_]/.test(t))f=-1;if(l==r.CharCategory.Space)l=f;if(l!=f)return false;if(l==r.CharCategory.Word){if(t.toLowerCase()==t){if(!n&&c)return false;u=true}else if(u){if(n)return false;a=true}else{if(c&&n&&o(d=e.state.sliceDoc(s,s+1))==r.CharCategory.Word&&d.toLowerCase()==d)return false;c=true}}i++;return true};let d=e.moveByChar(t,n,(e=>{f(e);return f}));if(Ae&&l==r.CharCategory.Word&&d.from==t.from+i*(n?1:-1)){let o=Math.min(t.head,d.head),l=Math.max(t.head,d.head);let s=e.state.sliceDoc(o,l);if(s.length>1&&/[\u4E00-\uffff]/.test(s)){let e=Array.from(Ae.segment(s));if(e.length>1){if(n)return r.EditorSelection.cursor(t.head+e[1].index,-1);return r.EditorSelection.cursor(d.head+e[e.length-1].index,1)}}}return d}function Be(e,t){return re(e,(n=>n.empty?Ce(e,n,t):oe(n,t)))}const Ee=e=>Be(e,true);const De=e=>Be(e,false);function xe(e,t,n){if(t.type.prop(n))return true;let r=t.to-t.from;return r&&(r>2||/[^\s,.;:]/.test(e.sliceDoc(t.from,t.to)))||t.firstChild}function Le(e,t,n){let o=(0,i.syntaxTree)(e).resolveInner(t.head);let l=n?c.NodeProp.closedBy:c.NodeProp.openedBy;for(let r=t.head;;){let t=n?o.childAfter(r):o.childBefore(r);if(!t)break;if(xe(e,t,l))o=t;else r=n?t.to:t.from}let s=o.type.prop(l),a,u;if(s&&(a=n?(0,i.matchBrackets)(e,o.from,1):(0,i.matchBrackets)(e,o.to,-1))&&a.matched)u=n?a.end.to:a.end.from;else u=n?o.to:o.from;return r.EditorSelection.cursor(u,n?-1:1)}const Me=e=>re(e,(t=>Le(e.state,t,!se(e))));const be=e=>re(e,(t=>Le(e.state,t,se(e))));function Te(e,t){return re(e,(n=>{if(!n.empty)return oe(n,t);let r=e.moveVertically(n,t);return r.head!=n.head?r:e.moveToLineBoundary(n,t)}))}const Oe=e=>Te(e,false);const Ie=e=>Te(e,true);function Re(e){let t=e.scrollDOM.clientHeight<e.scrollDOM.scrollHeight-2;let n=0,r=0,o;if(t){for(let t of e.state.facet(l.EditorView.scrollMargins)){let o=t(e);if(o===null||o===void 0?void 0:o.top)n=Math.max(o===null||o===void 0?void 0:o.top,n);if(o===null||o===void 0?void 0:o.bottom)r=Math.max(o===null||o===void 0?void 0:o.bottom,r)}o=e.scrollDOM.clientHeight-n-r}else{o=(e.dom.ownerDocument.defaultView||window).innerHeight}return{marginTop:n,marginBottom:r,selfScroll:t,height:Math.max(e.defaultLineHeight,o-5)}}function Ve(e,t){let n=Re(e);let{state:r}=e,o=te(r.selection,(r=>r.empty?e.moveVertically(r,t,n.height):oe(r,t)));if(o.eq(r.selection))return false;let s;if(n.selfScroll){let t=e.coordsAtPos(r.selection.main.head);let i=e.scrollDOM.getBoundingClientRect();let a=i.top+n.marginTop,c=i.bottom-n.marginBottom;if(t&&t.top>a&&t.bottom<c)s=l.EditorView.scrollIntoView(o.main.head,{y:"start",yMargin:t.top-a})}e.dispatch(ne(r,o),{effects:s});return true}const Fe=e=>Ve(e,false);const Ne=e=>Ve(e,true);function Ue(e,t,n){let o=e.lineBlockAt(t.head),l=e.moveToLineBoundary(t,n);if(l.head==t.head&&l.head!=(n?o.to:o.from))l=e.moveToLineBoundary(t,n,false);if(!n&&l.head==o.from&&o.length){let n=/^\s*/.exec(e.state.sliceDoc(o.from,Math.min(o.from+100,o.to)))[0].length;if(n&&t.head!=o.from+n)l=r.EditorSelection.cursor(o.from+n)}return l}const Ge=e=>re(e,(t=>Ue(e,t,true)));const Je=e=>re(e,(t=>Ue(e,t,false)));const Pe=e=>re(e,(t=>Ue(e,t,!se(e))));const He=e=>re(e,(t=>Ue(e,t,se(e))));const We=e=>re(e,(t=>r.EditorSelection.cursor(e.lineBlockAt(t.head).from,1)));const ze=e=>re(e,(t=>r.EditorSelection.cursor(e.lineBlockAt(t.head).to,-1)));function _e(e,t,n){let o=false,l=te(e.selection,(t=>{let l=(0,i.matchBrackets)(e,t.head,-1)||(0,i.matchBrackets)(e,t.head,1)||t.head>0&&(0,i.matchBrackets)(e,t.head-1,1)||t.head<e.doc.length&&(0,i.matchBrackets)(e,t.head+1,-1);if(!l||!l.end)return t;o=true;let s=l.start.from==t.head?l.end.to:l.end.from;return n?r.EditorSelection.range(t.anchor,s):r.EditorSelection.cursor(s)}));if(!o)return false;t(ne(e,l));return true}const je=({state:e,dispatch:t})=>_e(e,t,false);const qe=({state:e,dispatch:t})=>_e(e,t,true);function Ke(e,t){let n=te(e.state.selection,(e=>{let n=t(e);return r.EditorSelection.range(e.anchor,n.head,n.goalColumn,n.bidiLevel||undefined)}));if(n.eq(e.state.selection))return false;e.dispatch(ne(e.state,n));return true}function $e(e,t){return Ke(e,(n=>e.moveByChar(n,t)))}const Qe=e=>$e(e,!se(e));const Xe=e=>$e(e,se(e));const Ye=e=>$e(e,true);const Ze=e=>$e(e,false);const et=e=>Ke(e,(t=>fe(e.state,t,true)));const tt=e=>Ke(e,(t=>fe(e.state,t,false)));function nt(e,t){return Ke(e,(n=>e.moveByGroup(n,t)))}const rt=e=>nt(e,!se(e));const ot=e=>nt(e,se(e));const lt=e=>nt(e,true);const st=e=>nt(e,false);const it=e=>Ke(e,(t=>e.moveByChar(t,true,(n=>Se(e,t.head,n)))));function at(e,t){return Ke(e,(n=>Ce(e,n,t)))}const ct=e=>at(e,true);const ut=e=>at(e,false);const ft=e=>Ke(e,(t=>Le(e.state,t,!se(e))));const dt=e=>Ke(e,(t=>Le(e.state,t,se(e))));function ht(e,t){return Ke(e,(n=>e.moveVertically(n,t)))}const mt=e=>ht(e,false);const pt=e=>ht(e,true);function gt(e,t){return Ke(e,(n=>e.moveVertically(n,t,Re(e).height)))}const yt=e=>gt(e,false);const kt=e=>gt(e,true);const wt=e=>Ke(e,(t=>Ue(e,t,true)));const St=e=>Ke(e,(t=>Ue(e,t,false)));const vt=e=>Ke(e,(t=>Ue(e,t,!se(e))));const At=e=>Ke(e,(t=>Ue(e,t,se(e))));const Ct=e=>Ke(e,(t=>r.EditorSelection.cursor(e.lineBlockAt(t.head).from)));const Bt=e=>Ke(e,(t=>r.EditorSelection.cursor(e.lineBlockAt(t.head).to)));const Et=({state:e,dispatch:t})=>{t(ne(e,{anchor:0}));return true};const Dt=({state:e,dispatch:t})=>{t(ne(e,{anchor:e.doc.length}));return true};const xt=({state:e,dispatch:t})=>{t(ne(e,{anchor:e.selection.main.anchor,head:0}));return true};const Lt=({state:e,dispatch:t})=>{t(ne(e,{anchor:e.selection.main.anchor,head:e.doc.length}));return true};const Mt=({state:e,dispatch:t})=>{t(e.update({selection:{anchor:0,head:e.doc.length},userEvent:"select"}));return true};const bt=({state:e,dispatch:t})=>{let n=$t(e).map((({from:t,to:n})=>r.EditorSelection.range(t,Math.min(n+1,e.doc.length))));t(e.update({selection:r.EditorSelection.create(n),userEvent:"select"}));return true};const Tt=({state:e,dispatch:t})=>{let n=te(e.selection,(t=>{let n=(0,i.syntaxTree)(e),o=n.resolveStack(t.from,1);if(t.empty){let e=n.resolveStack(t.from,-1);if(e.node.from>=o.node.from&&e.node.to<=o.node.to)o=e}for(let e=o;e;e=e.next){let{node:n}=e;if((n.from<t.from&&n.to>=t.to||n.to>t.to&&n.from<=t.from)&&e.next)return r.EditorSelection.range(n.to,n.from)}return t}));if(n.eq(e.selection))return false;t(ne(e,n));return true};const Ot=({state:e,dispatch:t})=>{let n=e.selection,o=null;if(n.ranges.length>1)o=r.EditorSelection.create([n.main]);else if(!n.main.empty)o=r.EditorSelection.create([r.EditorSelection.cursor(n.main.head)]);if(!o)return false;t(ne(e,o));return true};function It(e,t){if(e.state.readOnly)return false;let n="delete.selection",{state:o}=e;let s=o.changeByRange((o=>{let{from:l,to:s}=o;if(l==s){let r=t(o);if(r<l){n="delete.backward";r=Rt(e,r,false)}else if(r>l){n="delete.forward";r=Rt(e,r,true)}l=Math.min(l,r);s=Math.max(s,r)}else{l=Rt(e,l,false);s=Rt(e,s,true)}return l==s?{range:o}:{changes:{from:l,to:s},range:r.EditorSelection.cursor(l,l<o.head?-1:1)}}));if(s.changes.empty)return false;e.dispatch(o.update(s,{scrollIntoView:true,userEvent:n,effects:n=="delete.selection"?l.EditorView.announce.of(o.phrase("Selection deleted")):undefined}));return true}function Rt(e,t,n){if(e instanceof l.EditorView)for(let r of e.state.facet(l.EditorView.atomicRanges).map((t=>t(e))))r.between(t,t,((e,r)=>{if(e<t&&r>t)t=n?r:e}));return t}const Vt=(e,t,n)=>It(e,(o=>{let l=o.from,{state:s}=e,a=s.doc.lineAt(l),c,u;if(n&&!t&&l>a.from&&l<a.from+200&&!/[^ \t]/.test(c=a.text.slice(0,l-a.from))){if(c[c.length-1]=="\t")return l-1;let e=(0,r.countColumn)(c,s.tabSize),t=e%(0,i.getIndentUnit)(s)||(0,i.getIndentUnit)(s);for(let n=0;n<t&&c[c.length-1-n]==" ";n++)l--;u=l}else{u=(0,r.findClusterBreak)(a.text,l-a.from,t,t)+a.from;if(u==l&&a.number!=(t?s.doc.lines:1))u+=t?1:-1;else if(!t&&/[\ufe00-\ufe0f]/.test(a.text.slice(u-a.from,l-a.from)))u=(0,r.findClusterBreak)(a.text,u-a.from,false,false)+a.from}return u}));const Ft=e=>Vt(e,false,true);const Nt=e=>Vt(e,false,false);const Ut=e=>Vt(e,true,false);const Gt=(e,t)=>It(e,(n=>{let o=n.head,{state:l}=e,s=l.doc.lineAt(o);let i=l.charCategorizer(o);for(let e=null;;){if(o==(t?s.to:s.from)){if(o==n.head&&s.number!=(t?l.doc.lines:1))o+=t?1:-1;break}let a=(0,r.findClusterBreak)(s.text,o-s.from,t)+s.from;let c=s.text.slice(Math.min(o,a)-s.from,Math.max(o,a)-s.from);let u=i(c);if(e!=null&&u!=e)break;if(c!=" "||o!=n.head)e=u;o=a}return o}));const Jt=e=>Gt(e,false);const Pt=e=>Gt(e,true);const Ht=e=>It(e,(t=>{let n=e.lineBlockAt(t.head).to;return t.head<n?n:Math.min(e.state.doc.length,t.head+1)}));const Wt=e=>It(e,(t=>{let n=e.lineBlockAt(t.head).from;return t.head>n?n:Math.max(0,t.head-1)}));const zt=e=>It(e,(t=>{let n=e.moveToLineBoundary(t,false).head;return t.head>n?n:Math.max(0,t.head-1)}));const _t=e=>It(e,(t=>{let n=e.moveToLineBoundary(t,true).head;return t.head<n?n:Math.min(e.state.doc.length,t.head+1)}));const jt=({state:e,dispatch:t})=>{if(e.readOnly)return false;let n=[];for(let r=0,o="",l=e.doc.iter();;){l.next();if(l.lineBreak||l.done){let e=o.search(/\s+$/);if(e>-1)n.push({from:r-(o.length-e),to:r});if(l.done)break;o=""}else{o=l.value}r+=l.value.length}if(!n.length)return false;t(e.update({changes:n,userEvent:"delete"}));return true};const qt=({state:e,dispatch:t})=>{if(e.readOnly)return false;let n=e.changeByRange((e=>({changes:{from:e.from,to:e.to,insert:r.Text.of(["",""])},range:r.EditorSelection.cursor(e.from)})));t(e.update(n,{scrollIntoView:true,userEvent:"input"}));return true};const Kt=({state:e,dispatch:t})=>{if(e.readOnly)return false;let n=e.changeByRange((t=>{if(!t.empty||t.from==0||t.from==e.doc.length)return{range:t};let n=t.from,o=e.doc.lineAt(n);let l=n==o.from?n-1:(0,r.findClusterBreak)(o.text,n-o.from,false)+o.from;let s=n==o.to?n+1:(0,r.findClusterBreak)(o.text,n-o.from,true)+o.from;return{changes:{from:l,to:s,insert:e.doc.slice(n,s).append(e.doc.slice(l,n))},range:r.EditorSelection.cursor(s)}}));if(n.changes.empty)return false;t(e.update(n,{scrollIntoView:true,userEvent:"move.character"}));return true};function $t(e){let t=[],n=-1;for(let r of e.selection.ranges){let o=e.doc.lineAt(r.from),l=e.doc.lineAt(r.to);if(!r.empty&&r.to==l.from)l=e.doc.lineAt(r.to-1);if(n>=o.number){let e=t[t.length-1];e.to=l.to;e.ranges.push(r)}else{t.push({from:o.from,to:l.to,ranges:[r]})}n=l.number+1}return t}function Qt(e,t,n){if(e.readOnly)return false;let o=[],l=[];for(let s of $t(e)){if(n?s.to==e.doc.length:s.from==0)continue;let t=e.doc.lineAt(n?s.to+1:s.from-1);let i=t.length+1;if(n){o.push({from:s.to,to:t.to},{from:s.from,insert:t.text+e.lineBreak});for(let t of s.ranges)l.push(r.EditorSelection.range(Math.min(e.doc.length,t.anchor+i),Math.min(e.doc.length,t.head+i)))}else{o.push({from:t.from,to:s.from},{from:s.to,insert:e.lineBreak+t.text});for(let e of s.ranges)l.push(r.EditorSelection.range(e.anchor-i,e.head-i))}}if(!o.length)return false;t(e.update({changes:o,scrollIntoView:true,selection:r.EditorSelection.create(l,e.selection.mainIndex),userEvent:"move.line"}));return true}const Xt=({state:e,dispatch:t})=>Qt(e,t,false);const Yt=({state:e,dispatch:t})=>Qt(e,t,true);function Zt(e,t,n){if(e.readOnly)return false;let r=[];for(let o of $t(e)){if(n)r.push({from:o.from,insert:e.doc.slice(o.from,o.to)+e.lineBreak});else r.push({from:o.to,insert:e.lineBreak+e.doc.slice(o.from,o.to)})}t(e.update({changes:r,scrollIntoView:true,userEvent:"input.copyline"}));return true}const en=({state:e,dispatch:t})=>Zt(e,t,false);const tn=({state:e,dispatch:t})=>Zt(e,t,true);const nn=e=>{if(e.state.readOnly)return false;let{state:t}=e,n=t.changes($t(t).map((({from:e,to:n})=>{if(e>0)e--;else if(n<t.doc.length)n++;return{from:e,to:n}})));let r=te(t.selection,(t=>{let n=undefined;if(e.lineWrapping){let r=e.lineBlockAt(t.head),o=e.coordsAtPos(t.head,t.assoc||1);if(o)n=r.bottom+e.documentTop-o.bottom+e.defaultLineHeight/2}return e.moveVertically(t,true,n)})).map(n);e.dispatch({changes:n,selection:r,scrollIntoView:true,userEvent:"delete.line"});return true};const rn=({state:e,dispatch:t})=>{t(e.update(e.replaceSelection(e.lineBreak),{scrollIntoView:true,userEvent:"input"}));return true};const on=({state:e,dispatch:t})=>{t(e.update(e.changeByRange((t=>{let n=/^\s*/.exec(e.doc.lineAt(t.from).text)[0];return{changes:{from:t.from,to:t.to,insert:e.lineBreak+n},range:r.EditorSelection.cursor(t.from+n.length+1)}})),{scrollIntoView:true,userEvent:"input"}));return true};function ln(e,t){if(/\(\)|\[\]|\{\}/.test(e.sliceDoc(t-1,t+1)))return{from:t,to:t};let n=(0,i.syntaxTree)(e).resolveInner(t);let r=n.childBefore(t),o=n.childAfter(t),l;if(r&&o&&r.to<=t&&o.from>=t&&(l=r.type.prop(c.NodeProp.closedBy))&&l.indexOf(o.name)>-1&&e.doc.lineAt(r.to).from==e.doc.lineAt(o.from).from&&!/\S/.test(e.sliceDoc(r.to,o.from)))return{from:r.to,to:o.from};return null}const sn=cn(false);const an=cn(true);function cn(e){return({state:t,dispatch:n})=>{if(t.readOnly)return false;let o=t.changeByRange((n=>{let{from:o,to:l}=n,s=t.doc.lineAt(o);let a=!e&&o==l&&ln(t,o);if(e)o=l=(l<=s.to?s:t.doc.lineAt(l)).to;let c=new i.IndentContext(t,{simulateBreak:o,simulateDoubleBreak:!!a});let u=(0,i.getIndentation)(c,o);if(u==null)u=(0,r.countColumn)(/^\s*/.exec(t.doc.lineAt(o).text)[0],t.tabSize);while(l<s.to&&/\s/.test(s.text[l-s.from]))l++;if(a)({from:o,to:l}=a);else if(o>s.from&&o<s.from+100&&!/\S/.test(s.text.slice(0,o)))o=s.from;let f=["",(0,i.indentString)(t,u)];if(a)f.push((0,i.indentString)(t,c.lineIndent(s.from,-1)));return{changes:{from:o,to:l,insert:r.Text.of(f)},range:r.EditorSelection.cursor(o+1+f[1].length)}}));n(t.update(o,{scrollIntoView:true,userEvent:"input"}));return true}}function un(e,t){let n=-1;return e.changeByRange((o=>{let l=[];for(let r=o.from;r<=o.to;){let s=e.doc.lineAt(r);if(s.number>n&&(o.empty||o.to>s.from)){t(s,l,o);n=s.number}r=s.to+1}let s=e.changes(l);return{changes:l,range:r.EditorSelection.range(s.mapPos(o.anchor,1),s.mapPos(o.head,1))}}))}const fn=({state:e,dispatch:t})=>{if(e.readOnly)return false;let n=Object.create(null);let r=new i.IndentContext(e,{overrideIndentation:e=>{let t=n[e];return t==null?-1:t}});let o=un(e,((t,o,l)=>{let s=(0,i.getIndentation)(r,t.from);if(s==null)return;if(!/\S/.test(t.text))s=0;let a=/^\s*/.exec(t.text)[0];let c=(0,i.indentString)(e,s);if(a!=c||l.from<t.from+a.length){n[t.from]=s;o.push({from:t.from,to:t.from+a.length,insert:c})}}));if(!o.changes.empty)t(e.update(o,{userEvent:"indent"}));return true};const dn=({state:e,dispatch:t})=>{if(e.readOnly)return false;t(e.update(un(e,((t,n)=>{n.push({from:t.from,insert:e.facet(i.indentUnit)})})),{userEvent:"input.indent"}));return true};const hn=({state:e,dispatch:t})=>{if(e.readOnly)return false;t(e.update(un(e,((t,n)=>{let o=/^\s*/.exec(t.text)[0];if(!o)return;let l=(0,r.countColumn)(o,e.tabSize),s=0;let a=(0,i.indentString)(e,Math.max(0,l-(0,i.getIndentUnit)(e)));while(s<o.length&&s<a.length&&o.charCodeAt(s)==a.charCodeAt(s))s++;n.push({from:t.from+s,to:t.from+o.length,insert:a.slice(s)})})),{userEvent:"delete.dedent"}));return true};const mn=e=>{e.setTabFocusMode();return true};const pn=e=>{e.setTabFocusMode(2e3);return true};const gn=({state:e,dispatch:t})=>{if(e.selection.ranges.some((e=>!e.empty)))return dn({state:e,dispatch:t});t(e.update(e.replaceSelection("\t"),{scrollIntoView:true,userEvent:"input"}));return true};const yn=[{key:"Ctrl-b",run:ie,shift:Qe,preventDefault:true},{key:"Ctrl-f",run:ae,shift:Xe},{key:"Ctrl-p",run:Oe,shift:mt},{key:"Ctrl-n",run:Ie,shift:pt},{key:"Ctrl-a",run:We,shift:Ct},{key:"Ctrl-e",run:ze,shift:Bt},{key:"Ctrl-d",run:Ut},{key:"Ctrl-h",run:Ft},{key:"Ctrl-k",run:Ht},{key:"Ctrl-Alt-h",run:Jt},{key:"Ctrl-o",run:qt},{key:"Ctrl-t",run:Kt},{key:"Ctrl-v",run:Ne}];const kn=[{key:"ArrowLeft",run:ie,shift:Qe,preventDefault:true},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:ge,shift:rt,preventDefault:true},{mac:"Cmd-ArrowLeft",run:Pe,shift:vt,preventDefault:true},{key:"ArrowRight",run:ae,shift:Xe,preventDefault:true},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:ye,shift:ot,preventDefault:true},{mac:"Cmd-ArrowRight",run:He,shift:At,preventDefault:true},{key:"ArrowUp",run:Oe,shift:mt,preventDefault:true},{mac:"Cmd-ArrowUp",run:Et,shift:xt},{mac:"Ctrl-ArrowUp",run:Fe,shift:yt},{key:"ArrowDown",run:Ie,shift:pt,preventDefault:true},{mac:"Cmd-ArrowDown",run:Dt,shift:Lt},{mac:"Ctrl-ArrowDown",run:Ne,shift:kt},{key:"PageUp",run:Fe,shift:yt},{key:"PageDown",run:Ne,shift:kt},{key:"Home",run:Je,shift:St,preventDefault:true},{key:"Mod-Home",run:Et,shift:xt},{key:"End",run:Ge,shift:wt,preventDefault:true},{key:"Mod-End",run:Dt,shift:Lt},{key:"Enter",run:sn,shift:sn},{key:"Mod-a",run:Mt},{key:"Backspace",run:Ft,shift:Ft},{key:"Delete",run:Ut},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Jt},{key:"Mod-Delete",mac:"Alt-Delete",run:Pt},{mac:"Mod-Backspace",run:zt},{mac:"Mod-Delete",run:_t}].concat(yn.map((e=>({mac:e.key,run:e.run,shift:e.shift}))));const wn=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:Me,shift:ft},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:be,shift:dt},{key:"Alt-ArrowUp",run:Xt},{key:"Shift-Alt-ArrowUp",run:en},{key:"Alt-ArrowDown",run:Yt},{key:"Shift-Alt-ArrowDown",run:tn},{key:"Escape",run:Ot},{key:"Mod-Enter",run:an},{key:"Alt-l",mac:"Ctrl-l",run:bt},{key:"Mod-i",run:Tt,preventDefault:true},{key:"Mod-[",run:hn},{key:"Mod-]",run:dn},{key:"Mod-Alt-\\",run:fn},{key:"Shift-Mod-k",run:nn},{key:"Shift-Mod-\\",run:je},{key:"Mod-/",run:f},{key:"Alt-A",run:g},{key:"Ctrl-m",mac:"Shift-Alt-m",run:mn}].concat(kn);const Sn={key:"Tab",run:dn,shift:hn}}}]);