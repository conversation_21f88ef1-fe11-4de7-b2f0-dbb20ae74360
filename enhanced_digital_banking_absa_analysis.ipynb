{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🏦 Enhanced Digital Banking ABSA Analysis\n", "\n", "## Comprehensive Analysis Including:\n", "1. **🎯 Aspect-Based Sentiment Analysis** per aplikasi\n", "2. **📞 Developer Response Analysis** berdasarkan replyContent dan repliedAt\n", "3. **📊 Comparative Analysis** antar aplikasi digital banking\n", "4. **⏰ Response Time Analysis** untuk setiap app_id\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install pandas numpy mat<PERSON><PERSON>lib seaborn scikit-learn imbalanced-learn scipy plotly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Machine Learning imports\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.svm import SVC\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.metrics import (\n", "    accuracy_score, precision_recall_fscore_support, \n", "    confusion_matrix, classification_report\n", ")\n", "from sklearn.preprocessing import LabelEncoder\n", "from imblearn.over_sampling import SMOTE\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "pd.set_option('display.max_columns', None)\n", "\n", "print(\"✅ All libraries imported successfully!\")\n", "print(f\"📅 Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Data Loading and App Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the original dataset with developer responses\n", "print(\"📂 Loading original dataset with developer responses...\")\n", "df_original = pd.read_csv('google_play_reviews_DigitalBank_20250703_205650.csv')\n", "\n", "print(f\"📊 Original dataset shape: {df_original.shape}\")\n", "print(f\"📋 Columns: {df_original.columns.tolist()}\")\n", "\n", "# Load the preprocessed dataset with sentiment analysis\n", "print(\"\\n📂 Loading preprocessed dataset with sentiment analysis...\")\n", "try:\n", "    df_sentiment = pd.read_csv('google_play_reviews_DigitalBank_sentiment_analysis.csv')\n", "    print(f\"📊 Sentiment dataset shape: {df_sentiment.shape}\")\n", "    print(f\"📋 Sentiment columns: {df_sentiment.columns.tolist()}\")\n", "except FileNotFoundError:\n", "    print(\"❌ Sentiment analysis file not found. Please run sentiment analysis first.\")\n", "    df_sentiment = None\n", "\n", "# Display basic information about apps\n", "print(\"\\n🏦 DIGITAL BANKING APPS OVERVIEW\")\n", "print(\"=\" * 50)\n", "\n", "app_counts = df_original['app_id'].value_counts()\n", "print(f\"\\n📱 Apps in dataset ({len(app_counts)} apps):\")\n", "for app_id, count in app_counts.items():\n", "    percentage = (count / len(df_original)) * 100\n", "    print(f\"   {app_id}: {count:,} reviews ({percentage:.1f}%)\")\n", "\n", "# Create app name mapping for better visualization\n", "app_names = {\n", "    'com.jago.digitalBanking': 'Bank Jago',\n", "    'id.co.bankbkemobile.digitalbank': 'Bank BKE Mobile',\n", "    'com.btpn.dc': 'BTPN Jenius',\n", "    'com.bcadigital.blu': 'BCA Digital (blu)',\n", "    'com.bnc.finance': 'Neo Bank'\n", "}\n", "\n", "df_original['app_name'] = df_original['app_id'].map(app_names)\n", "print(f\"\\n📱 App Name Mapping:\")\n", "for app_id, app_name in app_names.items():\n", "    print(f\"   {app_id} → {app_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📞 Developer Response Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive Developer Response Analysis\n", "print(\"\\n📞 DEVELOPER RESPONSE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Convert timestamps\n", "df_original['at'] = pd.to_datetime(df_original['at'])\n", "df_original['repliedAt'] = pd.to_datetime(df_original['repliedAt'])\n", "\n", "# Overall response statistics\n", "total_reviews = len(df_original)\n", "reviews_with_replies = df_original['replyContent'].notna().sum()\n", "reply_rate = (reviews_with_replies / total_reviews) * 100\n", "\n", "print(f\"\\n📊 Overall Response Statistics:\")\n", "print(f\"   Total reviews: {total_reviews:,}\")\n", "print(f\"   Reviews with replies: {reviews_with_replies:,}\")\n", "print(f\"   Overall reply rate: {reply_rate:.1f}%\")\n", "\n", "# Response rate by app\n", "print(f\"\\n📱 Response Rate by App:\")\n", "app_response_stats = []\n", "for app_id in app_counts.index:\n", "    app_data = df_original[df_original['app_id'] == app_id]\n", "    app_replies = app_data['replyContent'].notna().sum()\n", "    app_reply_rate = (app_replies / len(app_data)) * 100\n", "    app_name = app_names[app_id]\n", "    \n", "    app_response_stats.append({\n", "        'app_id': app_id,\n", "        'app_name': app_name,\n", "        'total_reviews': len(app_data),\n", "        'replies': app_replies,\n", "        'reply_rate': app_reply_rate\n", "    })\n", "    \n", "    print(f\"   {app_name}: {app_reply_rate:.1f}% ({app_replies:,}/{len(app_data):,})\")\n", "\n", "# Response rate by rating\n", "print(f\"\\n⭐ Response Rate by Rating:\")\n", "rating_response_stats = []\n", "for score in sorted(df_original['score'].unique()):\n", "    score_data = df_original[df_original['score'] == score]\n", "    score_replies = score_data['replyContent'].notna().sum()\n", "    score_reply_rate = (score_replies / len(score_data)) * 100\n", "    \n", "    rating_response_stats.append({\n", "        'rating': score,\n", "        'total_reviews': len(score_data),\n", "        'replies': score_replies,\n", "        'reply_rate': score_reply_rate\n", "    })\n", "    \n", "    print(f\"   {score} stars: {score_reply_rate:.1f}% ({score_replies:,}/{len(score_data):,})\")\n", "\n", "# Convert to DataFrames for visualization\n", "app_response_df = pd.DataFrame(app_response_stats)\n", "rating_response_df = pd.DataFrame(rating_response_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Response Time Analysis\n", "print(f\"\\n⏰ RESPONSE TIME ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Calculate response time for reviews with replies\n", "replied_df = df_original[df_original['replyContent'].notna()].copy()\n", "\n", "if len(replied_df) > 0:\n", "    # Calculate response time in hours\n", "    replied_df['response_time_hours'] = (replied_df['repliedAt'] - replied_df['at']).dt.total_seconds() / 3600\n", "    \n", "    # Filter out negative response times (data quality issues)\n", "    valid_responses = replied_df[replied_df['response_time_hours'] >= 0]\n", "    \n", "    if len(valid_responses) > 0:\n", "        print(f\"\\n📈 Response Time Statistics (Valid responses: {len(valid_responses):,}):\")\n", "        print(f\"   Average response time: {valid_responses['response_time_hours'].mean():.1f} hours\")\n", "        print(f\"   Median response time: {valid_responses['response_time_hours'].median():.1f} hours\")\n", "        print(f\"   Fastest response: {valid_responses['response_time_hours'].min():.1f} hours\")\n", "        print(f\"   Slowest response: {valid_responses['response_time_hours'].max():.1f} hours\")\n", "        \n", "        # Response time by app\n", "        print(f\"\\n📱 Average Response Time by App:\")\n", "        app_response_times = []\n", "        for app_id in app_counts.index:\n", "            app_replied = valid_responses[valid_responses['app_id'] == app_id]\n", "            if len(app_replied) > 0:\n", "                avg_response = app_replied['response_time_hours'].mean()\n", "                median_response = app_replied['response_time_hours'].median()\n", "                app_name = app_names[app_id]\n", "                \n", "                app_response_times.append({\n", "                    'app_id': app_id,\n", "                    'app_name': app_name,\n", "                    'avg_response_hours': avg_response,\n", "                    'median_response_hours': median_response,\n", "                    'total_responses': len(app_replied)\n", "                })\n", "                \n", "                print(f\"   {app_name}: {avg_response:.1f}h avg, {median_response:.1f}h median ({len(app_replied):,} responses)\")\n", "        \n", "        app_response_times_df = pd.DataFrame(app_response_times)\n", "    else:\n", "        print(\"❌ No valid response times found\")\n", "        app_response_times_df = pd.DataFrame()\n", "else:\n", "    print(\"❌ No replies found in dataset\")\n", "    app_response_times_df = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Enhanced Aspect-Based Sentiment Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced ABSA with Digital Banking Specific Aspects\n", "def extract_digital_banking_aspects(text):\n", "    \"\"\"Extract aspects specific to digital banking apps\"\"\"\n", "    \n", "    # Enhanced aspect keywords for digital banking\n", "    aspect_keywords = {\n", "        'transfer_payment': [\n", "            'transfer', 'payment', 'pembayaran', 'bayar', 'kirim', 'uang', 'dana', 'saldo',\n", "            'transaksi', 'tf', 'trf', 'bifast', 'qris', 'va', 'virtual', 'account',\n", "            'gratis', 'admin', 'biaya', 'fee', 'charge', 'potong', 'pending', 'gagal',\n", "            'berhasil', 'sukses', 'masuk', 'keluar', 'tujuan', 'penerima', 'rekening'\n", "        ],\n", "        'app_performance': [\n", "            'loading', 'lambat', 'cepat', 'lemot', 'lag', 'buffering', 'delay', 'responsif',\n", "            'kinerja', 'performance', 'speed', 'ringan', 'berat', 'lancar', 'smooth',\n", "            'muter', 'mutar', 'stuck', 'freeze', 'hang', 'timeout'\n", "        ],\n", "        'bugs_errors': [\n", "            'bug', 'error', 'crash', 'gagal', 'ngebug', 'bermasalah', 'trouble',\n", "            'gangguan', 'rusak', 'tidak', 'bisa', 'gabisa', 'maintenance', 'down',\n", "            'koneksi', 'jaringan', 'session', 'expired', 'logout', 'keluar', 'sendiri'\n", "        ],\n", "        'user_interface': [\n", "            'interface', 'ui', 'design', 'tampilan', 'layout', 'menu', 'navigasi',\n", "            'antarmuka', 'tombol', 'button', 'halaman', 'screen', 'layar', 'warna',\n", "            'bagus', 'jelek', 'rapi', 'berantakan', 'mudah', 'sulit', 'bingung'\n", "        ],\n", "        'security_verification': [\n", "            'security', 'keamanan', 'aman', 'password', 'pin', 'biometric', 'fingerprint',\n", "            'otp', 'verifikasi', 'wajah', 'face', 'scan', 'login', 'masuk', 'kunci',\n", "            'lock', 'unlock', 'blokir', 'blocked', 'aktif', 'nonaktif', 'suspended'\n", "        ],\n", "        'features_services': [\n", "            'fitur', 'feature', 'layanan', 'service', 'fungsi', 'pulsa', 'topup', 'top',\n", "            'ewallet', 'wallet', 'dompet', 'investasi', 'tabung', 'nabung', 'simpan',\n", "            'bunga', 'deposito', 'pinjam', 'kredit', 'limit', 'qris', 'kartu', 'debit',\n", "            'tarik', 'tunai', 'atm', 'cashback', 'promo', 'diskon', 'bonus'\n", "        ],\n", "        'customer_service': [\n", "            'customer', 'service', 'cs', 'admin', 'support', 'help', 'bantuan',\n", "            'respon', 'response', 'chat', 'email', 'telepon', 'telpon', 'wa', 'whatsapp',\n", "            'live', 'bot', 'manusia', 'jawab', 'balas', 'reply', 'lambat', 'cepat',\n", "            'pengaduan', 'keluhan', 'komplain', 'lapor', 'solusi'\n", "        ],\n", "        'app_stability': [\n", "            'stabil', 'reliable', 'konsisten', 'handal', 'terpercaya', 'aman',\n", "            'nyaman', 'tenang', 'puas', 'kecewa', 'frustrasi', 'kesal', 'marah',\n", "            'senang', 'suka', 'recommend', 'rekomen', 'bagus', 'jelek', 'buruk'\n", "        ]\n", "    }\n", "    \n", "    text = str(text).lower()\n", "    found_aspects = []\n", "    aspect_scores = {}\n", "    \n", "    for aspect, keywords in aspect_keywords.items():\n", "        score = 0\n", "        for keyword in keywords:\n", "            if keyword in text:\n", "                score += text.count(keyword)\n", "        \n", "        if score > 0:\n", "            found_aspects.append(aspect)\n", "            aspect_scores[aspect] = score\n", "    \n", "    # If no specific aspects found, classify as 'general'\n", "    if not found_aspects:\n", "        found_aspects = ['general']\n", "        aspect_scores['general'] = 1\n", "    \n", "    # Get dominant aspect\n", "    dominant_aspect = max(aspect_scores.keys(), key=lambda x: aspect_scores[x])\n", "    \n", "    return {\n", "        'aspects': found_aspects,\n", "        'dominant_aspect': dominant_aspect,\n", "        'aspect_scores': aspect_scores\n", "    }\n", "\n", "print(\"✅ Digital Banking ABSA function defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply ABSA to original dataset\n", "print(\"\\n🎯 APPLYING ABSA TO DATASET\")\n", "print(\"=\" * 50)\n", "\n", "# Apply aspect extraction to all reviews\n", "print(\"📊 Extracting aspects from reviews...\")\n", "aspect_results = df_original['content'].apply(extract_digital_banking_aspects)\n", "\n", "# Extract aspect information\n", "df_original['dominant_aspect'] = aspect_results.apply(lambda x: x['dominant_aspect'])\n", "df_original['all_aspects'] = aspect_results.apply(lambda x: x['aspects'])\n", "df_original['aspect_scores'] = aspect_results.apply(lambda x: x['aspect_scores'])\n", "\n", "print(f\"✅ Aspects extracted for {len(df_original):,} reviews\")\n", "\n", "# Overall aspect distribution\n", "print(f\"\\n📊 Overall Aspect Distribution:\")\n", "aspect_counts = df_original['dominant_aspect'].value_counts()\n", "for aspect, count in aspect_counts.items():\n", "    percentage = (count / len(df_original)) * 100\n", "    print(f\"   {aspect}: {count:,} reviews ({percentage:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Comparative Analysis by App"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive App Comparison Analysis\n", "print(\"\\n📊 COMPARATIVE ANALYSIS BY APP\")\n", "print(\"=\" * 50)\n", "\n", "# 1. Aspect Distribution by App\n", "print(f\"\\n🎯 Aspect Distribution by App:\")\n", "app_aspect_analysis = []\n", "\n", "for app_id in app_counts.index:\n", "    app_data = df_original[df_original['app_id'] == app_id]\n", "    app_name = app_names[app_id]\n", "    \n", "    print(f\"\\n   📱 {app_name} ({app_id}):\")\n", "    app_aspects = app_data['dominant_aspect'].value_counts()\n", "    \n", "    for aspect, count in app_aspects.items():\n", "        percentage = (count / len(app_data)) * 100\n", "        print(f\"      {aspect}: {count:,} ({percentage:.1f}%)\")\n", "        \n", "        app_aspect_analysis.append({\n", "            'app_id': app_id,\n", "            'app_name': app_name,\n", "            'aspect': aspect,\n", "            'count': count,\n", "            'percentage': percentage,\n", "            'total_reviews': len(app_data)\n", "        })\n", "\n", "# Convert to DataFrame for analysis\n", "app_aspect_df = pd.DataFrame(app_aspect_analysis)\n", "\n", "# 2. Rating Distribution by App\n", "print(f\"\\n⭐ Rating Distribution by App:\")\n", "app_rating_analysis = []\n", "\n", "for app_id in app_counts.index:\n", "    app_data = df_original[df_original['app_id'] == app_id]\n", "    app_name = app_names[app_id]\n", "    \n", "    print(f\"\\n   📱 {app_name}:\")\n", "    app_ratings = app_data['score'].value_counts().sort_index()\n", "    avg_rating = app_data['score'].mean()\n", "    \n", "    print(f\"      Average rating: {avg_rating:.2f}\")\n", "    for rating, count in app_ratings.items():\n", "        percentage = (count / len(app_data)) * 100\n", "        print(f\"      {rating} stars: {count:,} ({percentage:.1f}%)\")\n", "        \n", "        app_rating_analysis.append({\n", "            'app_id': app_id,\n", "            'app_name': app_name,\n", "            'rating': rating,\n", "            'count': count,\n", "            'percentage': percentage,\n", "            'avg_rating': avg_rating\n", "        })\n", "\n", "app_rating_df = pd.DataFrame(app_rating_analysis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. Critical Issues Analysis by App\n", "print(f\"\\n⚠️ CRITICAL ISSUES ANALYSIS BY APP\")\n", "print(\"=\" * 50)\n", "\n", "# Focus on negative reviews (1-2 stars) and their aspects\n", "negative_reviews = df_original[df_original['score'] <= 2]\n", "\n", "print(f\"\\n🔴 Critical Issues (1-2 star reviews):\")\n", "critical_issues_analysis = []\n", "\n", "for app_id in app_counts.index:\n", "    app_negative = negative_reviews[negative_reviews['app_id'] == app_id]\n", "    app_name = app_names[app_id]\n", "    \n", "    if len(app_negative) > 0:\n", "        print(f\"\\n   📱 {app_name} - Critical Issues:\")\n", "        critical_aspects = app_negative['dominant_aspect'].value_counts()\n", "        \n", "        for aspect, count in critical_aspects.head(5).items():  # Top 5 critical aspects\n", "            percentage = (count / len(app_negative)) * 100\n", "            print(f\"      {aspect}: {count:,} ({percentage:.1f}% of negative reviews)\")\n", "            \n", "            critical_issues_analysis.append({\n", "                'app_id': app_id,\n", "                'app_name': app_name,\n", "                'critical_aspect': aspect,\n", "                'count': count,\n", "                'percentage_of_negative': percentage,\n", "                'total_negative_reviews': len(app_negative)\n", "            })\n", "\n", "critical_issues_df = pd.DataFrame(critical_issues_analysis)\n", "\n", "# 4. Developer Response Effectiveness\n", "print(f\"\\n📞 Developer Response Effectiveness:\")\n", "response_effectiveness = []\n", "\n", "for app_id in app_counts.index:\n", "    app_data = df_original[df_original['app_id'] == app_id]\n", "    app_name = app_names[app_id]\n", "    \n", "    # Response rate for negative reviews\n", "    app_negative = app_data[app_data['score'] <= 2]\n", "    negative_with_response = app_negative['replyContent'].notna().sum()\n", "    negative_response_rate = (negative_with_response / len(app_negative) * 100) if len(app_negative) > 0 else 0\n", "    \n", "    # Response rate for positive reviews\n", "    app_positive = app_data[app_data['score'] >= 4]\n", "    positive_with_response = app_positive['replyContent'].notna().sum()\n", "    positive_response_rate = (positive_with_response / len(app_positive) * 100) if len(app_positive) > 0 else 0\n", "    \n", "    print(f\"\\n   📱 {app_name}:\")\n", "    print(f\"      Negative reviews response rate: {negative_response_rate:.1f}%\")\n", "    print(f\"      Positive reviews response rate: {positive_response_rate:.1f}%\")\n", "    \n", "    response_effectiveness.append({\n", "        'app_id': app_id,\n", "        'app_name': app_name,\n", "        'negative_response_rate': negative_response_rate,\n", "        'positive_response_rate': positive_response_rate,\n", "        'total_negative': len(app_negative),\n", "        'total_positive': len(app_positive)\n", "    })\n", "\n", "response_effectiveness_df = pd.DataFrame(response_effectiveness)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Comprehensive Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive visualizations\n", "print(\"\\n📊 CREATING COMPREHENSIVE VISUALIZATIONS\")\n", "print(\"=\" * 50)\n", "\n", "# 1. App Response Rate Comparison\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "fig.suptitle('Digital Banking Apps: Comprehensive Analysis Dashboard', fontsize=16, fontweight='bold')\n", "\n", "# Response Rate by App\n", "ax1 = axes[0, 0]\n", "app_response_df_sorted = app_response_df.sort_values('reply_rate', ascending=True)\n", "bars1 = ax1.barh(app_response_df_sorted['app_name'], app_response_df_sorted['reply_rate'], \n", "                color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])\n", "ax1.set_xlabel('Response Rate (%)')\n", "ax1.set_title('Developer Response Rate by App', fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Add value labels\n", "for i, bar in enumerate(bars1):\n", "    width = bar.get_width()\n", "    ax1.text(width + 1, bar.get_y() + bar.get_height()/2, \n", "            f'{width:.1f}%', ha='left', va='center', fontweight='bold')\n", "\n", "# Average Rating by App\n", "ax2 = axes[0, 1]\n", "app_avg_ratings = app_rating_df.groupby('app_name')['avg_rating'].first().sort_values(ascending=True)\n", "bars2 = ax2.barh(app_avg_ratings.index, app_avg_ratings.values,\n", "                color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])\n", "ax2.set_xlabel('Average Rating')\n", "ax2.set_title('Average Rating by App', fontweight='bold')\n", "ax2.set_xlim(0, 5)\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Add value labels\n", "for i, bar in enumerate(bars2):\n", "    width = bar.get_width()\n", "    ax2.text(width + 0.05, bar.get_y() + bar.get_height()/2, \n", "            f'{width:.2f}', ha='left', va='center', fontweight='bold')\n", "\n", "# Aspect Distribution Heatmap\n", "ax3 = axes[1, 0]\n", "aspect_pivot = app_aspect_df.pivot(index='app_name', columns='aspect', values='percentage').fillna(0)\n", "sns.heatmap(aspect_pivot, annot=True, fmt='.1f', cmap='YlOrRd', ax=ax3, cbar_kws={'label': 'Percentage (%)'})\n", "ax3.set_title('Aspect Distribution by App (%)', fontweight='bold')\n", "ax3.set_xlabel('Aspects')\n", "ax3.set_ylabel('Apps')\n", "\n", "# Response Effectiveness\n", "ax4 = axes[1, 1]\n", "x = np.arange(len(response_effectiveness_df))\n", "width = 0.35\n", "\n", "bars3 = ax4.bar(x - width/2, response_effectiveness_df['negative_response_rate'], \n", "               width, label='Negative Reviews', color='#FF6B6B', alpha=0.8)\n", "bars4 = ax4.bar(x + width/2, response_effectiveness_df['positive_response_rate'], \n", "               width, label='Positive Reviews', color='#4ECDC4', alpha=0.8)\n", "\n", "ax4.set_xlabel('Apps')\n", "ax4.set_ylabel('Response Rate (%)')\n", "ax4.set_title('Response Rate: Negative vs Positive Reviews', fontweight='bold')\n", "ax4.set_xticks(x)\n", "ax4.set_xticklabels([name.split()[0] for name in response_effectiveness_df['app_name']], rotation=45)\n", "ax4.legend()\n", "ax4.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('digital_banking_comprehensive_analysis.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"✅ Comprehensive dashboard saved as 'digital_banking_comprehensive_analysis.png'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Summary and Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary and recommendations\n", "print(\"\\n📋 COMPREHENSIVE SUMMARY AND RECOMMENDATIONS\")\n", "print(\"=\" * 60)\n", "\n", "# 1. App Performance Summary\n", "print(f\"\\n🏆 APP PERFORMANCE RANKING:\")\n", "app_summary = []\n", "\n", "for app_id in app_counts.index:\n", "    app_data = df_original[df_original['app_id'] == app_id]\n", "    app_name = app_names[app_id]\n", "    \n", "    # Calculate key metrics\n", "    avg_rating = app_data['score'].mean()\n", "    response_rate = (app_data['replyContent'].notna().sum() / len(app_data)) * 100\n", "    negative_rate = (len(app_data[app_data['score'] <= 2]) / len(app_data)) * 100\n", "    \n", "    # Most common critical aspect\n", "    app_negative = app_data[app_data['score'] <= 2]\n", "    if len(app_negative) > 0:\n", "        top_critical_aspect = app_negative['dominant_aspect'].value_counts().index[0]\n", "    else:\n", "        top_critical_aspect = 'None'\n", "    \n", "    app_summary.append({\n", "        'app_name': app_name,\n", "        'avg_rating': avg_rating,\n", "        'response_rate': response_rate,\n", "        'negative_rate': negative_rate,\n", "        'top_critical_aspect': top_critical_aspect,\n", "        'total_reviews': len(app_data)\n", "    })\n", "\n", "app_summary_df = pd.DataFrame(app_summary)\n", "app_summary_df = app_summary_df.sort_values('avg_rating', ascending=False)\n", "\n", "for i, row in app_summary_df.iterrows():\n", "    print(f\"\\n   {i+1}. {row['app_name']}:\")\n", "    print(f\"      📊 Average Rating: {row['avg_rating']:.2f}/5.0\")\n", "    print(f\"      📞 Response Rate: {row['response_rate']:.1f}%\")\n", "    print(f\"      🔴 Negative Reviews: {row['negative_rate']:.1f}%\")\n", "    print(f\"      ⚠️ Top Critical Issue: {row['top_critical_aspect']}\")\n", "    print(f\"      📈 Total Reviews: {row['total_reviews']:,}\")\n", "\n", "# 2. Critical Issues Across All Apps\n", "print(f\"\\n⚠️ CRITICAL ISSUES ACROSS ALL APPS:\")\n", "all_critical_aspects = negative_reviews['dominant_aspect'].value_counts()\n", "print(f\"\\n   Top 5 Critical Aspects (from {len(negative_reviews):,} negative reviews):\")\n", "for i, (aspect, count) in enumerate(all_critical_aspects.head(5).items(), 1):\n", "    percentage = (count / len(negative_reviews)) * 100\n", "    print(f\"      {i}. {aspect}: {count:,} issues ({percentage:.1f}%)\")\n", "\n", "# 3. Developer Response Insights\n", "print(f\"\\n📞 DEVELOPER RESPONSE INSIGHTS:\")\n", "best_responder = app_response_df.loc[app_response_df['reply_rate'].idxmax()]\n", "worst_responder = app_response_df.loc[app_response_df['reply_rate'].idxmin()]\n", "\n", "print(f\"   🏆 Best Response Rate: {best_responder['app_name']} ({best_responder['reply_rate']:.1f}%)\")\n", "print(f\"   📉 Lowest Response Rate: {worst_responder['app_name']} ({worst_responder['reply_rate']:.1f}%)\")\n", "\n", "# Response strategy analysis\n", "print(f\"\\n   📊 Response Strategy Analysis:\")\n", "for _, row in response_effectiveness_df.iterrows():\n", "    if row['negative_response_rate'] > row['positive_response_rate']:\n", "        strategy = \"Prioritizes negative feedback\"\n", "    elif row['positive_response_rate'] > row['negative_response_rate']:\n", "        strategy = \"Prioritizes positive feedback\"\n", "    else:\n", "        strategy = \"Balanced approach\"\n", "    \n", "    print(f\"      {row['app_name']}: {strategy}\")\n", "    print(f\"         Negative: {row['negative_response_rate']:.1f}%, Positive: {row['positive_response_rate']:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Strategic Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Strategic Recommendations Based on Analysis\n", "print(\"\\n🎯 STRATEGIC RECOMMENDATIONS\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n🏦 FOR DIGITAL BANKING INDUSTRY:\")\n", "print(f\"\\n1. 🔴 CRITICAL PRIORITY - Transfer & Payment Issues:\")\n", "print(f\"   • Most common complaint across all apps\")\n", "print(f\"   • Focus on transaction reliability and speed\")\n", "print(f\"   • Implement real-time transaction monitoring\")\n", "print(f\"   • Improve error handling and user communication\")\n", "\n", "print(f\"\\n2. ⚡ HIGH PRIORITY - App Performance:\")\n", "print(f\"   • Loading time optimization is crucial\")\n", "print(f\"   • Implement performance monitoring dashboards\")\n", "print(f\"   • Regular performance testing and optimization\")\n", "\n", "print(f\"\\n3. 🐛 MEDIUM PRIORITY - Bug Resolution:\")\n", "print(f\"   • Establish robust QA processes\")\n", "print(f\"   • Implement crash reporting and monitoring\")\n", "print(f\"   • Regular app stability testing\")\n", "\n", "print(f\"\\n📞 FOR CUSTOMER SERVICE STRATEGY:\")\n", "print(f\"\\n1. 🎯 Response Rate Optimization:\")\n", "print(f\"   • Target 80%+ response rate for negative reviews\")\n", "print(f\"   • Prioritize 1-2 star reviews for immediate response\")\n", "print(f\"   • Implement automated response systems for common issues\")\n", "\n", "print(f\"\\n2. ⏰ Response Time Improvement:\")\n", "print(f\"   • Target <24 hours for critical issues\")\n", "print(f\"   • Implement escalation procedures\")\n", "print(f\"   • Use AI-powered response suggestions\")\n", "\n", "print(f\"\\n📊 FOR INDIVIDUAL APPS:\")\n", "\n", "# App-specific recommendations\n", "for _, row in app_summary_df.iterrows():\n", "    print(f\"\\n   📱 {row['app_name']}:\")\n", "    \n", "    if row['avg_rating'] < 3.5:\n", "        print(f\"      🔴 URGENT: Rating below 3.5 - Focus on core functionality\")\n", "    elif row['avg_rating'] < 4.0:\n", "        print(f\"      🟡 ATTENTION: Rating needs improvement\")\n", "    else:\n", "        print(f\"      🟢 GOOD: Maintain current quality\")\n", "    \n", "    if row['response_rate'] < 50:\n", "        print(f\"      📞 Increase response rate (currently {row['response_rate']:.1f}%)\")\n", "    \n", "    if row['negative_rate'] > 30:\n", "        print(f\"      ⚠️ High negative feedback rate ({row['negative_rate']:.1f}%)\")\n", "    \n", "    print(f\"      🎯 Focus on: {row['top_critical_aspect']} issues\")\n", "\n", "print(f\"\\n🚀 IMPLEMENTATION ROADMAP:\")\n", "print(f\"\\n   Phase 1 (0-3 months): Critical Issues\")\n", "print(f\"   • Fix transfer/payment reliability\")\n", "print(f\"   • Improve app performance\")\n", "print(f\"   • Enhance customer service response\")\n", "\n", "print(f\"\\n   Phase 2 (3-6 months): Quality Improvements\")\n", "print(f\"   • UI/UX enhancements\")\n", "print(f\"   • Security feature improvements\")\n", "print(f\"   • Advanced feature development\")\n", "\n", "print(f\"\\n   Phase 3 (6-12 months): Innovation\")\n", "print(f\"   • New feature rollouts\")\n", "print(f\"   • AI-powered customer service\")\n", "print(f\"   • Predictive issue resolution\")\n", "\n", "print(f\"\\n📈 SUCCESS METRICS:\")\n", "print(f\"   • Average rating > 4.0 across all apps\")\n", "print(f\"   • Response rate > 80% for negative reviews\")\n", "print(f\"   • Transfer success rate > 99.5%\")\n", "print(f\"   • App crash rate < 0.1%\")\n", "print(f\"   • Customer satisfaction score > 85%\")\n", "\n", "print(f\"\\n✅ Analysis completed! Use these insights for strategic planning.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📁 Export Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export all analysis results\n", "print(\"\\n📁 EXPORTING ANALYSIS RESULTS\")\n", "print(\"=\" * 50)\n", "\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "\n", "# Export datasets\n", "try:\n", "    # Main dataset with aspects\n", "    df_original.to_csv(f'digital_banking_absa_analysis_{timestamp}.csv', index=False)\n", "    print(f\"✅ Main analysis: digital_banking_absa_analysis_{timestamp}.csv\")\n", "    \n", "    # App comparison data\n", "    app_aspect_df.to_csv(f'app_aspect_comparison_{timestamp}.csv', index=False)\n", "    print(f\"✅ Aspect comparison: app_aspect_comparison_{timestamp}.csv\")\n", "    \n", "    # Response analysis\n", "    app_response_df.to_csv(f'app_response_analysis_{timestamp}.csv', index=False)\n", "    print(f\"✅ Response analysis: app_response_analysis_{timestamp}.csv\")\n", "    \n", "    # Critical issues\n", "    critical_issues_df.to_csv(f'critical_issues_analysis_{timestamp}.csv', index=False)\n", "    print(f\"✅ Critical issues: critical_issues_analysis_{timestamp}.csv\")\n", "    \n", "    # Summary report\n", "    app_summary_df.to_csv(f'app_performance_summary_{timestamp}.csv', index=False)\n", "    print(f\"✅ Performance summary: app_performance_summary_{timestamp}.csv\")\n", "    \n", "    print(f\"\\n📊 All analysis results exported successfully!\")\n", "    print(f\"📈 Visualization saved: digital_banking_comprehensive_analysis.png\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error exporting results: {e}\")\n", "\n", "print(f\"\\n🎯 ENHANCED DIGITAL BANKING ABSA ANALYSIS COMPLETE!\")\n", "print(f\"\\n📋 Key Deliverables:\")\n", "print(f\"   ✅ Aspect-based sentiment analysis per aplikasi\")\n", "print(f\"   ✅ Developer response analysis (replyContent & repliedAt)\")\n", "print(f\"   ✅ Comparative analysis antar aplikasi berdasarkan app_id\")\n", "print(f\"   ✅ Strategic recommendations untuk improvement\")\n", "print(f\"   ✅ Comprehensive visualizations dan dashboards\")\n", "print(f\"   ✅ Exportable datasets untuk further analysis\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}