{"title": "Top Bar", "description": "Top Bar settings.", "jupyter.lab.toolbars": {"TopBar": [{"name": "spacer", "type": "spacer", "rank": 50}]}, "jupyter.lab.transform": true, "properties": {"toolbar": {"title": "Top bar items", "description": "Note: To disable a item,\ncopy it to User Preferences and add the\n\"disabled\" key. The following example will disable the user menu:\n{\n  \"toolbar\": [\n    {\n      \"name\": \"user-menu\",\n      \"disabled\": true\n    }\n  ]\n}\n\nTop bar description:", "items": {"$ref": "#/definitions/toolbarItem"}, "type": "array", "default": []}}, "additionalProperties": false, "type": "object", "definitions": {"toolbarItem": {"properties": {"name": {"title": "Unique name", "type": "string"}, "args": {"title": "Command arguments", "type": "object"}, "command": {"title": "Command id", "type": "string", "default": ""}, "disabled": {"title": "Whether the item is ignored or not", "type": "boolean", "default": false}, "icon": {"title": "Item icon id", "description": "If defined, it will override the command icon", "type": "string"}, "label": {"title": "Item label", "description": "If defined, it will override the command label", "type": "string"}, "caption": {"title": "Item caption", "description": "If defined, it will override the command caption", "type": "string"}, "type": {"title": "Item type", "type": "string", "enum": ["command", "spacer"]}, "rank": {"title": "Item rank", "type": "number", "minimum": 0, "default": 50}}, "required": ["name"], "additionalProperties": false, "type": "object"}}}